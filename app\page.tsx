'use client';

import React, { useState } from 'react';
import { Layout, Tooltip } from 'antd';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';

import IconSidebar from '@/components/IconSideBar';
import LeftPanel from '@/components/LeftPanelCore';
import MainPanel from '@/components/MainPanelCore';
import RightPanel from '@/components/RightPanelCore';
import { useIconSidebarContext } from '@/context/IconSideBarContext';

const HomePage: React.FC = () => {
  // 管理侧栏的开关以及当前 icon 的选择
  const {
    selectedKey, setSelectedKey,
    leftVisible, setLeftVisible,
    rightVisible, setRightVisible,
  } = useIconSidebarContext();

  return (
    <Layout style={{ height: '100vh', overflow: 'hidden', flexDirection: 'row' }}>
      {/* 最左侧 icon 栏 */}
      <IconSidebar selectedKey={selectedKey} onSelect={setSelectedKey} />

      {/* 右侧三栏：左栏、主栏、右栏，可拖动宽度 */}
      <div style={{ flex: 1, height: '100vh', overflow: 'hidden', background: 'white'}}>
        <PanelGroup direction="horizontal" style={{ height: '100vh' }}>

          {/* 左侧第二栏 */}
          {leftVisible && (
            <Panel defaultSize={18} minSize={10} maxSize={50} order={1}>
              <div style={{ position: 'relative', height: '100%' }}>
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    right: 10,
                    bottom: 0,
                    left: 0,
                    borderRadius: 0,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.12)',
                    background: 'white',
                    overflow: 'hidden',
                    borderLeft: '1px solid #eee',
                    zIndex: 10,
                    display: 'flex',
                    flexDirection: 'column',
                    height: 'auto',
                  }}
                >
                  <LeftPanel />
                </div>
              </div>
            </Panel>
          )}

          {leftVisible && <PanelResizeHandle style={{ width: '0px', background: '#d9d9d9', cursor: 'col-resize', position: 'relative', right: '10px', zIndex: 20}} />}
          
          {/* 主内容栏 */}
          <Panel order={2} defaultSize={rightVisible ? 60 : 85} minSize={30}>
            <div style={{ background: 'white', height: '100%' }}>
              <MainPanel />
            </div>
          </Panel>
          
          {rightVisible && <PanelResizeHandle style={{ width: '0px', background: '#d9d9d9', cursor: 'col-resize', position: 'relative', left: '10px', zIndex: 20 }} />}
          
          {/* 右侧栏 */}
          {rightVisible && (
            <Panel order={3} defaultSize={24} minSize={12} maxSize={50}>
              <div style={{ position: 'relative', height: '100%' }}>
                {/* 关闭按钮，悬浮在右上角 */}
                {/* <Tooltip title="关闭右侧栏"> */}
                  <button
                    onClick={() => setRightVisible(false)}
                    className="
                    absolute top-4 right-4 w-8 h-8 rounded-full bg-white shadow flex items-center justify-center
                    z-50 cursor-pointer text-xl text-gray-800 hover:shadow-lg
                    dark:bg-gray-800 dark:text-gray-100 leading-none"
                  >
                    ×
                  </button>
                {/* </Tooltip> */}
                <div className="
                    absolute top-2 right-2 bottom-2 left-[10px] rounded-2xl shadow-lg bg-white
                    overflow-hidden border-l border-[#eee] z-10 flex flex-col h-auto
                    dark:bg-gray-800"
                >
                  <RightPanel />
                </div>
              </div>
            </Panel>
          )}

        </PanelGroup>
      </div>
    </Layout>
  );
};

export default HomePage;