import { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 创建一个AbortController来处理客户端中断
    const controller = new AbortController();
    const { signal } = controller;

    // 监听请求断开连接
    request.signal.addEventListener('abort', () => {
      controller.abort();
    });
    
    // 转发请求到后端
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/stream-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
      signal,
    });

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`后端服务响应错误: ${response.status}`);
    }

    // 创建一个新的 ReadableStream
    const stream = new ReadableStream({
      async start(controller) {
        // 处理后端的流式响应
        const reader = response.body?.getReader();
        if (!reader) {
          controller.close();
          return;
        }

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              controller.close();
              break;
            }
            // 将数据块传递给客户端
            controller.enqueue(value);
          }
        } catch (error) {
          console.error('流处理错误:', error);
          controller.error(error);
        } finally {
          reader.releaseLock();
        }
      }
    });

    // 返回流式响应
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error('流式聊天API错误:', error);
    return new Response(JSON.stringify({ error: '服务暂时不可用' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}