import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { pdfUrl, pages } = await req.json();
    if (!pdfUrl) return NextResponse.json({ error: '缺少 pdfUrl' }, { status: 400 });

    // 调用后端 API
    const apiRes = await fetch(
      process.env.BACKEND_URL ? `${process.env.BACKEND_URL}/pdf2text` : 'http://localhost:8848/pdf2text',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pdf_url: pdfUrl, pages: pages || '' })
      }
    );
    const data = await apiRes.json();
    if (!apiRes.ok) {
      return NextResponse.json({ error: data.detail || 'PDF解析失败' }, { status: 500 });
    }
    return NextResponse.json({ text: data.text });
  } catch (e) {
    return NextResponse.json({ error: 'PDF解析失败' }, { status: 500 });
  }
} 