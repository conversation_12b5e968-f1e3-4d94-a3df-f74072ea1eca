'use client';

import React from 'react';
import { Bookmark } from './bookmark/LeftPanel';
import AuthPanel from './auth/AuthPanel';
import { useIconSidebarContext } from '@/context/IconSideBarContext';


export default function LeftPanel() {
  const { selectedKey, leftVisible, setLeftVisible } = useIconSidebarContext();

  if (!leftVisible) return null;

  const getPanelByKey = () => {
    switch (selectedKey) {
      case 'bookmark':
        return <Bookmark />;

      case 'user':
        return <AuthPanel />;

      // 以后可扩展更多 case
      default:
        return null;
    }
  };

  return getPanelByKey();
};