---
applyTo: '**'
---
Coding standards, domain knowledge, and preferences that AI should follow.

## 目录结构
```
    axsight-front/
    ├── app/                    # Next.js 应用主目录
    │   ├── survey/           # 网站主界面，用于论文搜索和对话综述等功能
    │   ├── config/           # 应用配置
    │   ├── layout.tsx        # 全局布局组件
    │   ├── globals.css       # 全局样式文件
    │   └── api/               # API 路由接口
    │       ├── abstract-refinement-multi/  # 摘要精炼功能
    │       ├── pdf2md/                     # pdf 转 md
    │       ├── semantic-search/            # 检索核心函数
    │       ├── stream-chat/                # LLM 流式对话
    │       ├── translate/                  # 翻译
    │       └── databse/                    # 数据库相关增删查改操作
    │
    ├── components/            # 私有组件库
    │   ├── bookmark/           # 收藏组件
    │   ├── chat/               # 聊天组件
    │   ├── search/             # 搜索组件
    │   ├── IconSideBar/        # 最左侧 Icon 图标栏
    │   ├── MainPanelCore/       # 主页面管理
    │   ├── LeftPanelCore/       # 左侧栏管理
    │   └── RightPanelCore/      # 右侧栏管理
    │
    ├── lib/                   # 工具库和共享代码（如 LLM 的 id name，预设参数等）
    │   ├── database/          # 数据库表相关增删查改工具
    │   └── llm/               # LLM 相关工具
    │
    ├── context/               # 状态管理（单实例对象或者全局对象）
    │
    ├── public/                # 静态资源文件
    │
    ├── types/                 # 类型定义
    │   └── database/          # 数据库表相关类型定义
    │
    ├── 配置文件
    │   ├── .env.local         # 环境变量
    │   ├── next.config.ts     # Next.js 配置
    │   ├── tailwind.config.ts # Tailwind CSS 配置
    │   ├── tsconfig.json      # TypeScript 配置
    │   ├── postcss.config.mjs # PostCSS 配置
    │   └── .eslintrc.json     # ESLint 配置
    │
    ├── package.json           # 项目依赖配置
    └── README.md              # 项目说明文档
```

## 组织原则
── 保持项目结构清晰，遵循摸块化原则
── 相关功能应放在同一目录下
── 使用适当的目录命名，反映其包含内容
── 单个代码文件行数不宜超过三百行
── 单个函数不宜超过 50 行

## 命名规范

类名：PascalCase(大驼峰)
── 函数名：camelCase(小驼蜂) 或 snake_case
── 常量：UPPER SNAKE CASE
── 函数参数：使用语义化名称（如 `userInput` 而非 `input1`）
── 接口/类型：`IProps` → `ChatComponentProps`（具体化命名）
── 布尔变量：以 is/has/can 开头（`isLoading`, `hasPermission`）
── 事件处理：handle[元素][事件]（`handleInputChange`, `handleSubmitClick`）

## 注释规范

── 关键部分代码应有良好且简介的中文注释，但不宜过多，适量即可
── 所有注释之间应该形成良好的层次感和协同

## 前端架构

样式主要使用 Tailwind 来控制，极少数情况再用 Style 来控制
所有组件统一使用 antd 组件
尽可能以蓝、黑、白色调为主，所有实现都要考虑暗黑模式下的表现

## 代码编写原则

—— 前端代码无需撰写测试单元，也无需单独进行命令行验证，用户会自行验证
—— 代码尽可能解耦，高内聚低耦合
—— 代码要考虑可扩展性，避免写死的参数