'use client';

import React from 'react';
import { Tooltip, Avatar } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { FaSearch } from "react-icons/fa";
import { TbBookmarks } from "react-icons/tb";
import { PiChatTeardropDotsBold } from "react-icons/pi";
import { useUserContext } from '@/context/UserContext';

const iconItems = [
  { key: 'search', icon: <FaSearch size={18}/>, label: '搜索' },
  { key: 'bookmark', icon: <TbBookmarks size={26}/>, label: '收藏' },
  { key: 'chat', icon: <PiChatTeardropDotsBold size={24}/>, label: 'AI 助手' },
];

interface IconSideBarProps {
  selectedKey: string;
  onSelect: (key: string) => void;
}


const IconSidebar: React.FC<IconSideBarProps> = ({ selectedKey, onSelect }) => {
  const { user } = useUserContext();

  const handleAvatarClick = () => {
    onSelect('user'); // 选择用户相关的key
  };

  return (
    <div
      className="w-12 m-1 flex flex-col items-center"
    >
      {/* 用户头像 */}
      <Tooltip title={user ? user.username : '登录/注册'} placement="right">
        <div
          className={`cursor-pointer rounded-full mb-2 mt-2 border-2 transition-colors duration-200
            ${selectedKey === 'user' ? 'border-blue-400 shadow-[0_2px_8px_#e6f4ff]' : 'border-gray-200'}
            hover:border-blue-300`
          }
          onClick={handleAvatarClick}
        >
          <Avatar
            size={40}
            src={user?.avatar_url}
            icon={<UserOutlined />}
            className="bg-white"
          />
        </div>
      </Tooltip>

      {/* 分隔线 */}
      <div className="w-[80%] h-px bg-gray-200 my-2" />

      {/* 图标列表 */}
      {iconItems.map(item => (
        <Tooltip title={item.label} placement="right" key={item.key}>
          <div
            className={`m-2 cursor-pointer rounded-[8px] transition-colors duration-200
              w-[36px] h-[36px] flex items-center justify-center
              ${selectedKey === item.key ? 'bg-gray-300 shadow-[0_2px_8px_#e6f4ff]' : 'text-gray-500'}
              hover:bg-gray-300`
            }
            onClick={() => onSelect(item.key)}
          >
            {item.icon}
          </div>
        </Tooltip>
      ))}
    </div>
  );
};

export default IconSidebar; 