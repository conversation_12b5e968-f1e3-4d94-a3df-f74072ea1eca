'use client';

import React, { createContext, useContext, useState } from 'react';

interface IconSidebarProps {
    selectedKey: string;
    setSelectedKey: React.Dispatch<React.SetStateAction<string>>;
    leftVisible: boolean;
    setLeftVisible: React.Dispatch<React.SetStateAction<boolean>>;
    rightVisible: boolean;
    setRightVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const IconSidebarContext = createContext<IconSidebarProps | undefined>(undefined);

export const IconSidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedKey, setSelectedKey] = useState<string>('search');
  const [leftVisible, setLeftVisible] = useState<boolean>(true);
  const [rightVisible, setRightVisible] = useState<boolean>(true);

  return (
    <IconSidebarContext.Provider value={{ selectedKey, setSelectedKey, leftVisible, setLeftVisible, rightVisible, setRightVisible }}>
      {children}
    </IconSidebarContext.Provider>
  );
};

export const useIconSidebarContext = () => {
  const context = useContext(IconSidebarContext);
  if (!context) throw new Error('useIconSidebarContext 必须在 IconSidebarProvider 内使用');
  return context;
};
