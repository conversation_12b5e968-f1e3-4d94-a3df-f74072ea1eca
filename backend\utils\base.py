''' 用于语义搜索的工具类 '''
import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

from utils.config import config

import asyncio
from datetime import datetime
import smtplib
from email.mime.text import MIMEText
from email.header import Header

def write_search_log(log_content):
    """写入搜索日志"""
    # 获取当前时间
    current_time = datetime.now()
    date_str = current_time.strftime("%Y-%m-%d")
    time_str = current_time.strftime("%H:%M:%S")
    
    # 创建日志内容
    print(log_content)
    log_content = f"时间：{time_str} || {log_content}\n"

    # 确保 logs 目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 写入以日期命名的日志文件
    log_file = f"logs/{date_str}_search.log"
    try:
        with open(log_file, "a") as f:
            f.write(log_content)
    except IOError as e:
        print(f"写入日志文件失败: {e}")

def send_email(title, content):
    '''
    发送邮件（163邮箱服务）
    Args:
        subject: 邮件主题
        content: 邮件正文
    '''
    # 从环境变量读取配置
    mail_host = 'smtp.163.com'
    mail_user = config.get('SEND_EMAIL')  # 发件人邮箱
    mail_pass = config.get('AUTH_163')  # 授权码
    mail_receivers = config.get('RECEIVE_EMAIL')  # 多个收件人用逗号分隔

    if not (mail_user and mail_pass and mail_receivers):
        print('邮件配置不完整，请设置 MAIL_USER, MAIL_PASS, MAIL_RECEIVERS 环境变量')
        return False

    message = MIMEText(content, 'plain', 'utf-8')
    message['From'] = Header(mail_user)
    message['To'] = Header(mail_receivers)
    message['Subject'] = Header(title, 'utf-8')

    try:
        smtpObj = smtplib.SMTP()
        smtpObj.connect(mail_host, 25)
        smtpObj.login(mail_user, mail_pass)
        smtpObj.sendmail(mail_user, mail_receivers.split(','), message.as_string())
        smtpObj.quit()
        print('邮件发送成功')
        return True
    except Exception as e:
        print(f'邮件发送失败: {e}')
        return False

async def main():
    send_email('测试', '测试')

if __name__ == "__main__":
    asyncio.run(main())