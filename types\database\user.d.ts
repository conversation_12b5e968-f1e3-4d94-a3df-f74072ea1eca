export interface User {
  id: number; // 用户编号，主键
  email: string; // 邮箱
  password: string; // 密码
  username: string; // 用户昵称，不允许为空
  role: string; // 用户角色（admin、user、guest，权限管理）
  avatar_url?: string; // 头像 url，使用 OSS 存储
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
  last_login_at?: string; // 最后登录时间
  phone?: string; // 手机号
  social_type?: string; // 第三方平台类型（如微信、GitHub等）
  social_id?: string; // 第三方登录ID
  preferences?: any; // 用户偏好设置（可用 JSON 存储）
}

// 用于创建用户的类型（不包含自增ID）
export type CreateUserData = Omit<User, 'id'>;