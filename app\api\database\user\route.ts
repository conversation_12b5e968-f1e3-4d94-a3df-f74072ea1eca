import { NextRequest, NextResponse } from 'next/server';
import {
  getUserById,
  getUserByEmail,
  createUser,
  updateUser,
  deleteUser,
} from '@/lib/database/cloud_sql';

// GET: /api/database/user?id= 或 /api/database/user?email=
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  const email = searchParams.get('email');
  try {
    let user = null;
    if (id) {
      user = await getUserById(Number(id));
    } else if (email) {
      user = await getUserByEmail(email);
    } else {
      return NextResponse.json({ error: 'Missing id or email' }, { status: 400 });
    }
    if (!user) return NextResponse.json({ error: 'User not found' }, { status: 404 });
    return NextResponse.json(user);
  } catch (e) {
    return NextResponse.json({ error: (e as Error).message }, { status: 500 });
  }
}

// POST: /api/database/user
export async function POST(req: NextRequest) {
  try {
    const user = await req.json();
    const result = await createUser(user);
    return NextResponse.json({ success: true, result });
  } catch (e) {
    return NextResponse.json({ error: (e as Error).message }, { status: 500 });
  }
}

// PUT: /api/database/user?id=
export async function PUT(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  if (!id) return NextResponse.json({ error: 'Missing id' }, { status: 400 });
  try {
    const user = await req.json();
    const result = await updateUser(Number(id), user);
    return NextResponse.json({ success: true, result });
  } catch (e) {
    return NextResponse.json({ error: (e as Error).message }, { status: 500 });
  }
}

// DELETE: /api/database/user?id=
export async function DELETE(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  if (!id) return NextResponse.json({ error: 'Missing id' }, { status: 400 });
  try {
    const result = await deleteUser(Number(id));
    return NextResponse.json({ success: true, result });
  } catch (e) {
    return NextResponse.json({ error: (e as Error).message }, { status: 500 });
  }
}
