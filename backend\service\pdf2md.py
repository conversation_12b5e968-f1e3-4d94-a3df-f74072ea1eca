import os
import requests
import fitz  # pymupdf
from typing import Optional, List
import socket
import subprocess
import tempfile


def pdf_url_special_deal(pdf_url: str) -> str:
    '''
    对 PDF 链接进行特殊规则处理
    1. 如果包含 aclanthology 并且结尾不包括 .pdf 则添加 .pdf
    '''
    if 'aclanthology' in pdf_url and not pdf_url.endswith('.pdf'):
        pdf_url = pdf_url + '.pdf'
    return pdf_url

def pdf_url_to_text(pdf_url: str, pages: Optional[List[int]] = None, tmp_dir: Optional[str] = None) -> str:
    '''
    根据 PDF 链接下载并提取指定页码的文本内容
    Args:
        pdf_url: PDF 文件的网络地址
        pages: 需要提取的页码列表（从1开始），如 [1,2,3]，若为 None 则提取全部
        tmp_dir: 临时文件存储目录，默认为当前目录下 tmp
    Returns:
        str: 提取的文本内容
    '''
    # 1. 创建临时目录
    if tmp_dir is None:
        tmp_dir = os.path.join(os.path.dirname(__file__), 'tmp')
    os.makedirs(tmp_dir, exist_ok=True)
    print(f"临时文件存储目录: {tmp_dir}")

    # 2. 下载 PDF 文件
    pdf_url = pdf_url_special_deal(pdf_url) # 处理特殊规则
    with tempfile.NamedTemporaryFile(suffix='.pdf', dir=tmp_dir, delete=False) as tmp_file:
        pdf_path = tmp_file.name
    try:
        # 检查本地 6666 端口是否开启
        # 参考 https://pypi.org/project/shadowsocksr-cli/
        # 这个只能开启 socket5 所以不能直接 https 请求
        def is_port_open(host: str, port: int) -> bool:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(1)
            try:
                s.connect((host, port))
                s.close()
                return True
            except Exception:
                return False

        use_proxy = is_port_open('127.0.0.1', 6666)
        if use_proxy:
            print("检测到 6666 端口已开启，使用 curl socks5 代理下载 PDF")
            curl_cmd = [
                'curl',
                '--socks5', '127.0.0.1:6666',
                pdf_url,
                '-o', pdf_path,
                '--max-time', '30'
            ]
            result = subprocess.run(curl_cmd, capture_output=True, text=True)
            if result.returncode != 0:
                # 删除临时文件
                os.remove(pdf_path)
                raise RuntimeError(f"curl 下载失败: {result.stderr}")
            print(f"PDF 文件已下载至: {pdf_path}")
        else:
            print("未检测到 6666 端口，直接下载 PDF")
            response = requests.get(pdf_url, timeout=15)
            response.raise_for_status()
            with open(pdf_path, 'wb') as f:
                f.write(response.content)
            print(f"PDF 文件已下载至: {pdf_path}")
        # 3. 读取 PDF 并提取文本
        try:
            doc = fitz.open(pdf_path)
            text_all = ''
            if pages is None:
                page_indices = range(len(doc))
            else:
                # 页码从1开始，fitz下标从0开始
                page_indices = [p-1 for p in pages if 1 <= p <= len(doc)]
            for i in page_indices:
                page = doc[i]
                text = page.get_text()
                text_all += text + '\n'
                print(f"已提取第 {i+1} 页文本")
            print("文本提取完成")
            doc.close()
            return text_all.strip()
        finally:
            # 用完后删除临时 PDF 文件
            if os.path.exists(pdf_path):
                os.remove(pdf_path)
    except Exception as e:
        # 确保异常时也删除临时文件
        if os.path.exists(pdf_path):
            os.remove(pdf_path)
        raise RuntimeError(f"PDF 下载失败: {e}")
