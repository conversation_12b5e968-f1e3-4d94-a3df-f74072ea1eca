'''用于存放定时任务'''

import json
import os
import sys
from tqdm import tqdm

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

from utils.base import send_email

def filter_sc_balance_to_file():
    """过滤剩余金额大于0.2的key，输出到对应文件中"""
    import requests
    save_file_path = os.path.join(os.path.dirname(ROOT_DIR), "siliconflow_balance.key")
    with open(os.path.join(os.path.dirname(ROOT_DIR), "siliconflow.key"), "r") as f:
        keys = f.readlines()
    
    available_keys = []
    url = "https://api.siliconflow.cn/v1/user/info"
    for key in tqdm(keys):
        sc_key = key.strip()
        headers = {"Authorization": f"Bearer {sc_key}"}
        try:
            response = requests.request("GET", url, headers=headers, timeout=10)
            info = json.loads(response.text)
            balance = float(info['data']['balance'])
            if balance > 0.5:
                available_keys.append(sc_key)
        except Exception as e:
            print(f"Key {sc_key} 检查失败: {e}")
    
    if len(available_keys) > 0:
        with open(save_file_path, "w") as f:
            for k in available_keys:
                f.write(k + "\n")
        print(f"可用 key 数量: {len(available_keys)}")
    else:
        print("没有可用 key")
        send_email("硅基流动 无可用key", f"总数 {len(keys)}，可用 {len(available_keys)}")



if __name__ == "__main__":
    # 1. 过滤硅基流动key
    filter_sc_balance_to_file()