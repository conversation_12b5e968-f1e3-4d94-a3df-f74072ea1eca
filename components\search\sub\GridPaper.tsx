"use client";

import React from 'react';
import { Spin, Card, Tooltip } from 'antd';
import { useSearchContext } from '@/context/SearchContext';
import { MdPictureAsPdf } from "react-icons/md";
import { Bookmark } from "lucide-react";
import { useIconSidebarContext } from '@/context/IconSideBarContext';



export default function GridPaper() {
  const { papers, loading } = useSearchContext();
  const {
    selectedKey, setSelectedKey,
    leftVisible, setLeftVisible,
    rightVisible, setRightVisible,
  } = useIconSidebarContext();

  if (loading) return <Spin className="w-full mt-10" />;

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-3 gap-4 mt-6
        max-w-[1066px]
      `}
    >
      {papers.map((paper, idx) => (
        <Card
          size='small'
          loading={loading}
          key={paper.pdf_url || idx}
          title={
            <Tooltip title={paper.translated_title || paper.title} placement="top">
              <span className="font-bold">{`${idx + 1}. ${paper.translated_title || paper.title}`}</span>
            </Tooltip>
          }
          extra={
            <span className="flex gap-1 items-center">
              {paper.pdf_url && (
                <a href={paper.pdf_url} target="_blank" rel="noopener noreferrer">
                  <Tooltip title="查看 PDF" placement="top">
                    <MdPictureAsPdf className="text-xl text-gray-500 hover:text-gray-700" size={18}/>
                  </Tooltip>
                </a>
              )}
              <Tooltip title="收藏" placement="top">
                <Bookmark className="text-xl text-gray-500 hover:text-gray-700 cursor-pointer" size={18} />
              </Tooltip>
            </span>
          }
          className="shadow-md transition-shadow duration-200 hover:shadow-xl bg-white dark:bg-neutral-900"
        >
          <div className="text-xs text-gray-500 mb-1">
            {(paper.published || '').slice(0, 10)} - {paper.source}
          </div>
          <div className="mb-2 line-clamp-3">{paper.summary}</div>
        </Card>
        // <Card
        //   size="small"
        //   loading={loading}
        //   key={paper.pdf_url || idx}
        //   className="shadow-md transition-shadow duration-200 hover:shadow-xl bg-white dark:bg-neutral-900"
        // >
        //   {/* 自定义头部: 使用 float 实现文字环绕效果 */}
        //   <div className="mb-2 flow-root">
        //     <span className="float-right flex gap-1 items-center pl-2">
        //       {paper.pdf_url && (
        //         <a href={paper.pdf_url} target="_blank" rel="noopener noreferrer">
        //           <Tooltip title="查看 PDF" placement="top">
        //             <MdPictureAsPdf className="text-xl text-gray-500 hover:text-gray-700" size={18}/>
        //           </Tooltip>
        //         </a>
        //       )}
        //       <Tooltip title="收藏" placement="top">
        //         <Bookmark className="text-xl text-gray-500 hover:text-gray-700 cursor-pointer" size={18} />
        //       </Tooltip>
        //     </span>
        //     <span className="font-bold">
        //       {`${idx + 1}. ${paper.translated_title || paper.title}`}
        //     </span>
        //   </div>
        //   {/* 其他内容 */}
        //   <div className="text-xs text-gray-500 mb-1">
        //     {(paper.published || '').slice(0, 10)} - {paper.source}
        //   </div>
        //   <div className="mb-2 line-clamp-3">{paper.summary}</div>
        // </Card>
      ))}
    </div>
  );
}
