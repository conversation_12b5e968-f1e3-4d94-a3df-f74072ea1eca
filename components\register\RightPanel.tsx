'use client';

import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, Space, App } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, SafetyOutlined } from '@ant-design/icons';
import { useUserContext } from '@/context/UserContext';

const { Title, Text } = Typography;

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  verificationCode: string;
}

export default function RegisterPanel() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { login, setIsLoading } = useUserContext();
  const { message } = App.useApp();

  // 发送验证码
  const handleSendCode = async () => {
    try {
      const email = form.getFieldValue('email');
      console.log('准备发送验证码到邮箱:', email);

      if (!email) {
        message.error('请先输入邮箱地址');
        return;
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        message.error('请输入有效的邮箱地址');
        return;
      }

      setSendingCode(true);

      const response = await fetch('/api/auth/send-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();
      console.log('发送验证码API响应:', result);

      if (response.ok && result.success) {
        message.success('验证码已发送，请查收邮件');
        // console.log('验证码发送成功，开发环境验证码:', result.code);
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(result.error || '发送验证码失败');
      }
    } catch (error) {
      console.error('发送验证码错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  };

  const handleRegister = async (values: RegisterFormData) => {
    setLoading(true);
    setIsLoading(true);

    try {
      console.log('准备注册用户:', {
        email: values.email,
        username: values.username,
        verificationCode: values.verificationCode
      });

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: values.email,
          password: values.password,
          username: values.username,
          verificationCode: values.verificationCode,
        }),
      });

      const result = await response.json();
      console.log('注册API响应:', result);

      if (response.ok && result.success) {
        // 注册成功，自动登录
        login(result.user);
        message.success('注册成功！');
      } else {
        message.error(result.error || '注册失败，请重试');
      }
    } catch (error) {
      console.error('注册错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 h-full bg-white overflow-y-auto">
      <Card className="shadow-sm">
        <div className="text-center mb-6">
          <Title level={3}>用户注册</Title>
          <Text type="secondary">创建您的新账户</Text>
        </div>

        <Form
          form={form}
          name="register"
          onFinish={handleRegister}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            label="用户名"
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, max: 20, message: '用户名长度为2-20个字符' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="请输入邮箱地址"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="邮箱验证码"
            name="verificationCode"
            rules={[
              { required: true, message: '请输入验证码' },
              { len: 6, message: '验证码为6位数字' }
            ]}
          >
            <Space.Compact style={{ width: '100%' }}>
              <Input
                prefix={<SafetyOutlined />}
                placeholder="请输入6位验证码"
                size="large"
                style={{ flex: 1 }}
              />
              <Button
                size="large"
                onClick={handleSendCode}
                loading={sendingCode}
                disabled={countdown > 0}
              >
                {countdown > 0 ? `${countdown}s` : '发送验证码'}
              </Button>
            </Space.Compact>
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6位字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="确认密码"
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入密码"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
            >
              注册
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}