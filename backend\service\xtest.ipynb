{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["query: Large Language Model literature review prompting, 翻译后: Large Language Model literature review prompting\n", "years_filter: published like \"%\", source_filter: source == \"arxiv\"\n", "filter: (published like \"%\") and (source == \"arxiv\")\n", "翻译：Large Language Model literature review prompting |==>| Large Language Model literature review prompting\n", "step1 总耗时: 0.76s || 翻译：0.09s, 向量化：0.24s, 搜索：0.43s\n"]}], "source": ["import sys\n", "sys.path.append('E:\\\\project\\\\arxiv-insight\\\\backend')\n", "sys.path.append('E:\\\\project\\\\arxiv-insight')\n", "\n", "from utils.llm import BaseLLM, ModelSelector\n", "from backend.service.search import semantic_search, deep_semantic_search\n", "\n", "query = \"Large Language Model literature review prompting\"\n", "results = await semantic_search(query, limit=500, source=['arxiv'], output_fields=[\"title\", \"authors\", \"summary\", \"published\", \"source\", \"pdf_url\", \"vector\"])"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'title': 'Psychometric Predictive Power of Large Language Models', 'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'], 'summary': 'Instruction tuning aligns the response of large language models (LLMs) with\\nhuman preferences. Despite such efforts in human--LLM alignment, we find that\\ninstruction tuning does not always make LLMs human-like from a cognitive\\nmodeling perspective. More specifically, next-word probabilities estimated by\\ninstruction-tuned LLMs are often worse at simulating human reading behavior\\nthan those estimated by base LLMs. In addition, we explore prompting\\nmethodologies for simulating human reading behavior with LLMs. Our results show\\nthat prompts reflecting a particular linguistic hypothesis improve psychometric\\npredictive power, but are still inferior to small base models. These findings\\nhighlight that recent advancements in LLMs, i.e., instruction tuning and\\nprompting, do not offer better estimates than direct probability measurements\\nfrom base LLMs in cognitive modeling. In other words, pure next-word\\nprobability remains a strong predictor for human reading behavior, even in the\\nage of LLMs.', 'published': '2023-11-13T17:19:14+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2311.07484v3', 'vector': '[-0.031669050455093384, -0.009922715835273266, -0.012774070724844933, -0.01487456914037466, -0.04178185760974884, -0.03915860876441002, 0.018666870892047882, -0.013648486696183681, 0.0005387872806750238, -0.0236092209815979, -0.06322404742240906, -0.03235337510704994, -0.014028667472302914, 0.015549389645457268, -0.0277151707559824, 0.006044873036444187, -0.007019085809588432, -0.004132088739424944, -0.028475532308220863, -0.019455745816230774, 0.011139294132590294, 0.03176409751176834, 0.014732001349329948, 0.05531628802418709, -0.00675771152600646, 0.007983794435858727, -0.06204548850655556, -0.031897157430648804, -0.0007478866609744728, 0.02208849787712097, -0.028000306338071823, -0.011766592040657997, 0.0483589842915535, 0.003457268001511693, 0.005631426349282265, -0.022278588265180588, 0.018752412870526314, -0.0360221192240715, -0.019151601940393448, -0.03347490727901459, 0.028722651302814484, -0.032866619527339935, 0.0015623050276190042, -0.006068634334951639, -0.0010793567635118961, -0.005850030109286308, 0.00905305240303278, -0.015036146156489849, 0.004654837306588888, -0.03600310906767845, 0.007812713272869587, -0.03339887410402298, 0.018381735309958458, -0.012232313863933086, -0.003911108709871769, -0.0063014947809278965, -0.029425984248518944, 0.03292364627122879, -0.022335615009069443, -0.0048710647970438, -0.01238438580185175, -0.005403317976742983, 0.006615143734961748, -0.022468678653240204, -0.013135242275893688, 0.010550013743340969, -0.01669943705201149, 0.04155374690890312, -0.01760236546397209, -0.05843377113342285, 0.029311930760741234, 0.045963846147060394, -0.008349718526005745, -0.021309128031134605, -0.04771267622709274, 0.042314108461141586, -0.03491959720849991, -0.016347769647836685, -0.005750232841819525, 0.020434711128473282, -0.007584604434669018, -0.007703410927206278, 0.050906192511320114, -0.011414924636483192, 0.022601742297410965, 0.02879868634045124, -0.0034952862188220024, -0.01285961177200079, 0.01415222603827715, -0.023533184081315994, 0.00997974257916212, 0.01611015573143959, 0.058357734233140945, -0.06478279083967209, -0.018913988023996353, -0.023742282763123512, -0.03265751898288727, 0.00519421836361289, 0.004386334680020809, -0.0023095975629985332, -0.0013341965386644006, 0.04379681497812271, -0.00687651801854372, 0.008796430192887783, -0.005042146425694227, -0.007841226644814014, -0.018885474652051926, 0.0300913006067276, 0.038284193724393845, -0.01740276999771595, 0.016842003911733627, 0.017982546240091324, 0.028266433626413345, 0.041363656520843506, 0.003972887992858887, -0.023647237569093704, 0.02841850556433201, -0.035584911704063416, 0.0389685183763504, -0.020681828260421753, 0.0081026004627347, 0.030243372544646263, 0.04436708614230156, 0.009452242404222488, -0.010264878161251545, -0.001539731747470796, 0.037942033261060715, -0.0021836627274751663, -0.031859140843153, 0.00816913228482008, 0.0027848235331475735, 0.030642563477158546, -0.045469608157873154, 0.008592083118855953, -0.06561918556690216, -0.05702710151672363, -0.01982642337679863, 0.02288687787950039, 0.0019626826979219913, -0.03467247635126114, 0.02938796579837799, 0.07508568465709686, 0.009960734285414219, -0.039234645664691925, -0.0012795455986633897, -0.0460018627345562, 0.03682049736380577, -0.029901210218667984, -0.02524399757385254, -0.04862510785460472, -0.03104175254702568, 0.008867714554071426, 0.011576501652598381, 0.05349142104387283, -0.006572373677045107, 0.023095976561307907, -0.022867867723107338, 0.026137422770261765, 0.030889680609107018, 0.02712589129805565, 0.012555466964840889, -0.028152380138635635, 0.004018034785985947, 0.0049803671427071095, 0.03231535851955414, 0.031669050455093384, 0.004405343439429998, 0.046344026923179626, -0.029425984248518944, -0.042200054973363876, 0.009542535059154034, -0.020358676090836525, -0.018343718722462654, -0.005042146425694227, 0.01462745200842619, 0.007945775985717773, 0.06284386664628983, 0.032638512551784515, 0.03516671434044838, 0.0075180730782449245, 0.023286066949367523, -0.010930194519460201, 0.043682761490345, -0.029464002698659897, -0.015577903017401695, 0.0002224650961579755, -0.016547363251447678, -0.0661514401435852, 0.027582108974456787, -0.03372202813625336, 0.00035374623257666826, -0.04866312816739082, 0.024122463539242744, -0.005602912977337837, 0.0530732236802578, -0.09656589478254318, -0.050221867859363556, -0.04083140566945076, -0.004008530173450708, -0.020472729578614235, 0.007042847108095884, 0.023780301213264465, -0.033094726502895355, -0.04109753295779228, -0.03813212364912033, 0.010074787773191929, -0.05345340445637703, -0.043682761490345, 0.029673101380467415, -0.03184013068675995, 0.018457772210240364, 0.014009658247232437, 0.05736926570534706, -0.00476413918659091, 0.009761138819158077, -0.0005482918350026011, -0.04402492195367813, 0.04794078320264816, -0.007461045868694782, -0.0530732236802578, -0.03885446488857269, 0.08569272607564926, 0.0068385000340640545, -0.006848004646599293, 0.0053225294686853886, 0.018115609884262085, 0.03520473092794418, -0.0045146457850933075, -0.021917415782809258, -0.033588964492082596, 0.01787799596786499, 0.024616699665784836, -0.020035522058606148, -0.008401992730796337, -0.05512619763612747, -0.037923023104667664, -0.045127447694540024, 0.015853533521294594, 0.01597709394991398, -0.0007668957114219666, -0.027277963235974312, -0.06664567440748215, -0.001241527497768402, 0.026270484551787376, -0.046420060098171234, -0.026384539902210236, -0.017326734960079193, -0.004685726948082447, 0.046800240874290466, 0.028000306338071823, -0.010882671922445297, -0.018980519846081734, -0.04782672971487045, -0.02786724455654621, 0.008259424939751625, 0.009642332792282104, 0.010359923355281353, -0.027220936492085457, -0.041363656520843506, -0.018856961280107498, 0.048435017466545105, 0.01984543167054653, 0.020738856866955757, 0.009951229207217693, 0.019360700622200966, 0.01357245072722435, 0.010122310370206833, 0.010702086612582207, -0.018571825698018074, 0.0036212210543453693, -0.012793079949915409, -0.05466998368501663, 0.04105951264500618, 0.010321905836462975, -0.01635727472603321, 0.02784823440015316, -0.04782672971487045, -0.033703017979860306, 0.06542909890413284, -0.03258148580789566, -0.018837952986359596, -0.007513320539146662, 0.01480803731828928, -0.02877967804670334, 0.011994700878858566, -0.04432906582951546, -0.045621681958436966, -0.026232466101646423, -0.022810840979218483, -0.08151073753833771, 0.035565901547670364, -0.007712915539741516, 0.06394638866186142, -0.03406418859958649, -0.04406294226646423, -0.0507161021232605, 0.014047675766050816, -0.16484634578227997, 0.007294716779142618, -0.04417699575424194, 0.01803957298398018, 0.009114831686019897, 0.021290117874741554, -0.03092769905924797, 0.017440788447856903, -0.0043744537979364395, 0.015939075499773026, 0.01190915983170271, -0.04307447001338005, 0.054479893296957016, -0.002180098555982113, -0.036060135811567307, 0.030946707352995872, 0.017754437401890755, 0.0019424856873229146, -0.005469849333167076, -0.02786724455654621, -0.0028347221668809652, -0.05288313329219818, 0.042314108461141586, -0.017868492752313614, -0.009989247657358646, -0.015245245769619942, 0.005465097259730101, -0.0016217082738876343, -0.02030164748430252, 0.017079617828130722, 0.004999375902116299, 0.028741659596562386, -0.003288562875241041, 0.01760236546397209, -0.007385009899735451, 0.032752566039562225, -0.026232466101646423, -0.015159704722464085, -0.010084292851388454, -0.020928947255015373, 0.04151573032140732, 0.058129627257585526, -0.008891475386917591, 0.055392324924468994, -0.023514175787568092, -0.049955740571022034, 0.006558116525411606, -0.005973589140921831, 0.017478806897997856, -0.023647237569093704, 0.01399064902216196, 0.009941725060343742, -0.025871295481920242, 0.037219688296318054, -0.047256458550691605, -0.023875346407294273, 0.006686427630484104, -0.005745480768382549, -0.010778122581541538, 0.004037043545395136, -0.0389685183763504, -0.027182918041944504, 0.0019508021650835872, -0.05501214414834976, -0.032619502395391464, 0.006833747960627079, 0.09428481012582779, 0.0230009313672781, 0.03843626752495766, -0.06314801424741745, 0.011139294132590294, -0.04695231467485428, 0.020738856866955757, -0.027772199362516403, 0.03670644387602806, 0.030889680609107018, 0.014009658247232437, -0.013762541115283966, 0.009114831686019897, -0.08067434281110764, -0.02016858570277691, 0.021100027486681938, 0.007009581197053194, 0.03406418859958649, -0.023267056792974472, -0.054213766008615494, 0.04322654381394386, -0.006220706272870302, 0.034520406275987625, 0.23221436142921448, 0.009827670641243458, -0.029825175181031227, -0.023419130593538284, 0.015549389645457268, -0.005294016096740961, -0.007945775985717773, 0.008316452614963055, 0.008297443389892578, -0.024844806641340256, 0.01692754402756691, 0.04987970367074013, 0.01511218212544918, 0.03090868890285492, 0.023628229275345802, 0.015596912242472172, -0.07877343893051147, -0.0008346153772436082, 0.05254096910357475, 0.006657914258539677, 0.0185813307762146, 0.014693982899188995, 0.03883545845746994, -0.003911108709871769, -0.05333935096859932, -0.0072329374961555, -0.008463772013783455, 0.009328683838248253, 0.014789028093218803, 0.015017136931419373, -0.031003734096884727, 0.041021496057510376, 0.04508942738175392, 0.006762464065104723, 0.0271449014544487, -0.012983170337975025, -0.039956990629434586, -0.03406418859958649, -0.00428891321644187, 0.028038324788212776, -0.016395291313529015, -0.013648486696183681, -0.032524459064006805, -0.00024191965349018574, 0.008924741297960281, -0.004015658516436815, 0.013705513440072536, 0.015672948211431503, 0.02395138330757618, -0.03326581045985222, 0.01957930438220501, 0.012355872429907322, -0.002704035025089979, 0.025282016023993492, 0.014427856542170048, 0.01174758281558752, 0.01256497111171484, 0.03271454945206642, 0.012717043980956078, 0.02973012998700142, 0.037827976047992706, 0.0015741856768727303, 0.010540509596467018, 0.006106652319431305, 0.004455242305994034, 0.02621345780789852, -0.006985819898545742, -0.031098779290914536, 0.056228723376989365, 0.027525082230567932, 0.03564193844795227, 0.04379681497812271, -0.0028869970701634884, 0.006596134975552559, 0.02359021082520485, -0.014693982899188995, 0.05888998880982399, 0.0790015459060669, 0.016347769647836685, -0.010426455177366734, -0.019322684034705162, 0.0028109608683735132, -0.015492362901568413, -0.015492362901568413, -0.0053225294686853886, -0.0031721326522529125, 0.012184791266918182, 0.03020535595715046, 0.009167106822133064, -0.021803362295031548, -0.019750386476516724, 0.015473353676497936, 0.0016609143931418657, 9.571345435688272e-05, -0.018144123256206512, 0.00459305802360177, 0.02290588617324829, -0.019978495314717293, -0.04866312816739082, 0.024806790053844452, 0.006752959452569485, 0.012783575803041458, 0.059764403849840164, 0.02834247052669525, 0.019408224150538445, -0.025890303775668144, -0.027943279594182968, 0.02526300586760044, 0.00022974199964664876, 0.04261825606226921, -0.03784698620438576, 0.02524399757385254, -0.01238438580185175, -0.03613617271184921, 0.008934246376156807, 0.0577874630689621, -0.004578800871968269, 0.0283044520765543, 0.0024426609743386507, 0.0031982699874788523, -0.011348393745720387, 0.05949827656149864, -0.0425041988492012, 0.026232466101646423, 0.0046429564245045185, -0.03752383217215538, -0.00939046312123537, 0.04824492707848549, -0.009770643897354603, -0.013486909680068493, 0.0283044520765543, 0.004885321948677301, -0.005602912977337837, 0.01309722475707531, 0.03448238596320152, -0.0023975144140422344, 0.0024854312650859356, -0.0034667726140469313, -0.017032094299793243, 0.04334059730172157, -0.05854782462120056, 0.0044694989919662476, 0.015691958367824554, -0.019427232444286346, 0.02866562269628048, -0.0177164189517498, -0.021157054230570793, -0.02149921841919422, -0.0389305017888546, 0.03387409821152687, -0.024122463539242744, 0.03260049223899841, 0.023780301213264465, -0.04334059730172157, -0.013838577084243298, -0.04318852722644806, -0.025529133155941963, -0.016005607321858406, 0.01930367387831211, -0.04934745281934738, 0.0042556473053991795, 0.0389685183763504, -0.02263975888490677, 0.01663290522992611, 0.025985348969697952, 0.032524459064006805, 0.011243843473494053, 0.0236092209815979, -0.03972887992858887, -0.028950758278369904, -0.042542219161987305, -0.0075228251516819, -0.0021753462497144938, 0.036155182868242264, 0.022240569815039635, 0.04934745281934738, -0.049385469406843185, -0.018818942829966545, -0.03446337953209877, -0.035356804728507996, 0.01399064902216196, 0.0068147387355566025, -0.020263630896806717, 0.03961482644081116, 0.029768146574497223, -0.027277963235974312, 0.014047675766050816, -0.02431255392730236, 0.030395446345210075, -0.029178867116570473, -0.03927266597747803, 0.0980105772614479, -0.025282016023993492, -0.019788404926657677, 0.051742590963840485, 0.02347615733742714, 0.01900903321802616, 0.020320657640695572, 0.021328136324882507, -0.03583202883601189, -0.003873090725392103, 0.0026398797053843737, -0.03218229487538338, 0.04235212877392769, -0.028171388432383537, 0.0015801260014995933, -0.009509269148111343, -0.0013959759380668402, 0.003903980366885662, -0.0040346672758460045, -0.017868492752313614, -0.02018759399652481, -0.0613991804420948, 0.022867867723107338, 0.015558894723653793, -0.0021503970492631197, 0.020966963842511177, 0.05603863298892975, -0.004747506231069565, 0.02172732539474964, 0.017820969223976135, 0.00201614573597908, -0.023343093693256378, -0.021214082837104797, -0.0590040422976017, 0.020206604152917862, 0.011519474908709526, 0.02064381167292595, 0.006771968211978674, 0.0081026004627347, -0.0087108900770545, -0.04060329496860504, 0.01606263406574726, 0.0030248125549405813, 0.01572997495532036, 0.050906192511320114, 0.011414924636483192, 0.02429354563355446, -0.029654093086719513, 0.014532406814396381, -0.04410095885396004, 0.020453721284866333, 0.002889373106881976, 0.0041510979644954205, 0.012099250219762325, -0.0294830109924078, -0.03066157177090645, -0.018343718722462654, 0.010359923355281353, -0.041135549545288086, 0.02393237315118313, -0.05318727716803551, 0.0036331017035990953, -0.004298417828977108, 0.001862885314039886, -0.00237612915225327, 0.04280834645032883, 0.005807260051369667, -0.02807634323835373, -0.02512994222342968, 0.0028584834653884172, 0.013553441502153873, 0.011795105412602425, -0.006287238094955683, -0.07375505566596985, -0.05406169220805168, -0.001552800415083766, -0.00343825900927186, -0.029882201924920082, -0.08820191770792007, 0.012821593321859837, 0.014646460302174091, -0.023324085399508476, -0.09337237477302551, 0.04284636303782463, 0.006353769451379776, -0.004733249545097351, -0.000600269646383822, -0.00884870532900095, -0.01103474386036396, -0.08196695148944855, 0.047256458550691605, -0.035337794572114944, 0.0007080865325406194, -0.012137268669903278, 0.033208783715963364, 0.06554315239191055, -0.03634527325630188, 0.03577500209212303, 0.00563617842271924, 0.05219880864024162, -0.021423181518912315, -0.04406294226646423, 0.0003359252877999097, 0.02936895750463009, 0.029806165024638176, -0.0044433618895709515, 0.05911809578537941, -0.0032315358985215425, -0.00939046312123537, -0.03786599636077881, -0.002483054995536804, -0.014732001349329948, 0.015330785885453224, -0.034159235656261444, -0.013249296694993973, -0.012270331382751465, -0.0039301179349422455, 0.06744405627250671, -0.011719069443643093, -0.00844001118093729, -0.0058832960203289986, 0.004787900485098362, -0.008449515327811241, 0.004531278274953365, 0.06326206773519516, 0.034843560308218, -0.003792302217334509, 0.015967588871717453, 0.008107353001832962, 0.031554996967315674, 0.011633528396487236, -0.03260049223899841, -0.014009658247232437, -0.006130413617938757, 0.03153598681092262, -0.003074711188673973, 0.01706060767173767, 0.0025757241528481245, -0.009380958043038845, -0.04676222428679466, -0.041933927685022354, -0.007480055093765259, -0.006002102512866259, -0.02030164748430252, -0.018391240388154984, 0.044443123042583466, -0.009509269148111343, 0.009827670641243458, -0.010996726341545582, 0.047256458550691605, -0.06793829053640366, 0.03090868890285492, 9.526793292025104e-05, 0.011006230488419533, -0.01697506755590439, -0.009437985718250275, -0.006990572437644005, -0.000895206700079143, -0.005574399139732122, -0.009623323567211628, -0.02431255392730236, -0.013629477471113205, -0.03330382704734802, 0.027068864554166794, 0.007731924764811993, 0.022848859429359436, 0.04569771885871887, 0.02737300843000412, 0.010654564015567303, -0.037580858916044235, -0.02444561757147312, -0.015245245769619942, -0.04470924660563469, -0.006040120497345924, 0.03220130503177643, 0.013591459020972252, -0.049841687083244324, -0.009319178760051727, -0.051058266311883926, -0.0052750068716704845, 0.02444561757147312, -0.038056086748838425, -0.010635554790496826, -0.018818942829966545, 0.01511218212544918, -0.012488935142755508, 0.004132088739424944, 0.014779523946344852, 0.0005263126222416759, 0.007484807167202234, -0.003516671247780323, 0.020491737872362137, -0.021062009036540985, -0.005289263557642698, -0.04151573032140732, -0.038759421557188034, -0.021917415782809258, 0.07044748216867447, 0.04334059730172157, -0.0017595237586647272, -0.015825020149350166, -0.01615767925977707, -0.015739480033516884, -0.13960234820842743, -0.0022775197867304087, 0.0006546236108988523, 0.017250698059797287, -0.019322684034705162, -0.014418352395296097, -5.008434891351499e-05, -0.008069334551692009, -0.0601826012134552, -0.07816515117883682, 0.032885629683732986, 0.022107506170868874, 0.027829226106405258, -0.05124835669994354, -0.004583553411066532, 0.019379710778594017, -0.050069794058799744, -0.02455967292189598, 0.011966186575591564, 0.07101774960756302, -0.003062830539420247, 0.003785173874348402, 0.059650350362062454, 0.02760111726820469, -0.02560516819357872, -0.01803957298398018, -0.029083821922540665, -0.02381831966340542, -0.025985348969697952, -0.010816140100359917, -0.01785898767411709, -0.02807634323835373, 0.025662196800112724, 0.0530732236802578, 0.01921813376247883, 0.012061231769621372, 0.0030010512564331293, -0.009884697385132313, -0.008592083118855953, -0.007912510074675083, 0.03980491682887077, 0.001761899795383215, -0.030167337507009506, 0.02524399757385254, -0.066075399518013, 0.10249671339988708, -0.019037548452615738, 0.0017179414862766862, -0.02385633811354637, 0.007779447361826897, 0.006021111737936735, 0.041819874197244644, -0.02857057750225067, -0.007014333736151457, -0.020111558958888054, 0.027544090524315834, -0.030376436188817024, 0.009157601743936539, 0.01511218212544918, 0.07637830078601837, -0.014418352395296097, 0.011605015024542809, -0.05147646367549896, 0.04094545915722847, -0.02877967804670334, 0.007950528524816036, -0.05653286725282669, -0.01521673146635294, -0.013629477471113205, 0.018790429458022118, -0.011300871148705482, 0.011072762310504913, -0.0005073035717941821, -0.034159235656261444, -0.0007948152488097548, -0.01880943961441517, 0.02372327446937561, -0.014142720960080624, 0.01463695615530014, -0.009523525834083557, -0.03961482644081116, 0.016908535733819008, -0.04531753808259964, -0.017070112749934196, 0.023761292919516563, -0.03784698620438576, -0.010816140100359917, 0.006562869064509869, -0.07132189720869064, -0.00716640567407012, -0.027430037036538124, -0.030129319056868553, -0.01310672890394926, -0.06881270557641983, -0.02454066276550293, 0.03495761379599571, -0.03214427828788757, 0.029654093086719513, 0.025072915479540825, -0.03801806643605232, -0.04269428923726082, 0.02193642593920231, -0.011300871148705482, 0.04987970367074013, -0.0026517603546380997, 0.017801960930228233, 0.01014131959527731, 0.012488935142755508, 0.008363975211977959, 0.028171388432383537, -0.03313274681568146, -0.01840074546635151, 0.0016573501052334905, 0.006667418871074915, -0.0684325248003006, 0.0012142020277678967, -0.011490961536765099, 0.043720778077840805, 0.009219381958246231, 0.004754634574055672, 0.03223932161927223, 0.000978965312242508, 0.02925490401685238, -0.018134618178009987, -0.0007609553867951035, 0.08333560824394226, 0.003661615075543523, 0.005826268810778856, 0.03161202371120453, -0.008131114766001701, -0.00040810019709169865, -0.00096173828933388, -0.003778045531362295, 0.04862510785460472, -0.004455242305994034, 0.020206604152917862, -0.01704159937798977, 0.03385508805513382, -0.0027919518761336803, -0.019360700622200966, -0.046686187386512756, -0.017782950773835182, -0.011015735566616058, -0.018201150000095367, -0.047142405062913895, 0.008753660134971142, 0.03718167170882225, 0.03125085309147835, -0.01357245072722435, -0.0312698595225811, 0.028228415176272392, 0.025871295481920242, 0.052578989416360855, 0.034634459763765335, 0.026346521452069283, 0.029064813628792763, -0.018267681822180748, -0.0010823268676176667, 0.03161202371120453, 0.08516047149896622, 0.032505448907613754, -0.016832498833537102, -0.04227609187364578, -0.04493735730648041, -0.02786724455654621, 0.045963846147060394, 0.012650512158870697, 0.03619319945573807, 0.0038065591361373663, 0.05683701112866402, 0.013239792548120022, 0.006719693541526794, -0.03877842798829079, -0.0013009307440370321, 0.005816764198243618, -0.006358521990478039, 0.021860389038920403, 0.0003356282541062683, 0.0010912374127656221, -0.003366975113749504, 0.03969086334109306, 0.015064659528434277, 0.020928947255015373, 0.013772045262157917, -0.010901681147515774, -0.00611140439286828, -0.01841975376009941, 0.04459519311785698, 0.03915860876441002, -0.01980741322040558, -0.026764720678329468, -0.04649609699845314, 0.03467247635126114, -0.01805858314037323, 0.032619502395391464, 0.028171388432383537, 0.0253770612180233, 0.046686187386512756, -0.0156539399176836, 0.04330258071422577, -0.03957680985331535, 0.024825798347592354, 0.012308349832892418, -0.013638981617987156, -0.01038843672722578, -0.043492671102285385, -0.013173260726034641, -0.03915860876441002, -0.006458319257944822, 0.0030580784659832716, 0.0194652508944273, -0.01887597143650055, -0.007461045868694782, 0.022240569815039635, 0.020130567252635956, -0.022981923073530197, 0.035223741084337234, 0.008112105540931225, 0.025890303775668144, 0.0018783301347866654, 0.014712992124259472, 0.03172607719898224, 0.023019939661026, -0.03531878441572189, -0.046534113585948944, -0.02087191864848137, 0.021328136324882507, 0.02678372897207737, 0.009181363508105278, 0.029007786884903908, 0.0008197645656764507, -0.017935024574398994, -0.002315538004040718, 0.012165782041847706, -0.00044730634544976056, 0.026118412613868713, -0.01937020570039749, -0.0025234492495656013, -0.051514483988285065, 0.005602912977337837, 0.04911934584379196, 0.0005479948013089597, 0.059042058885097504, -0.022449668496847153]', 'distance': 0.6652522087097168}, {'title': 'Large Language Models are Competitive Near Cold-start Recommenders for Language- and Item-based Preferences', 'authors': ['Scott Sanner', 'Krisztian Balog', 'Filip Radlinski', 'Ben Wedin', 'Lucas Dixon'], 'summary': \"Traditional recommender systems leverage users' item preference history to\\nrecommend novel content that users may like. However, modern dialog interfaces\\nthat allow users to express language-based preferences offer a fundamentally\\ndifferent modality for preference input. Inspired by recent successes of\\nprompting paradigms for large language models (LLMs), we study their use for\\nmaking recommendations from both item-based and language-based preferences in\\ncomparison to state-of-the-art item-based collaborative filtering (CF) methods.\\nTo support this investigation, we collect a new dataset consisting of both\\nitem-based and language-based preferences elicited from users along with their\\nratings on a variety of (biased) recommended items and (unbiased) random items.\\nAmong numerous experimental results, we find that LLMs provide competitive\\nrecommendation performance for pure language-based preferences (no item\\npreferences) in the near cold-start case in comparison to item-based CF\\nmethods, despite having no supervised training for this specific task\\n(zero-shot) or only a few labels (few-shot). This is particularly promising as\\nlanguage-based preference representations are more explainable and scrutable\\nthan item-based or vector-based representations.\", 'published': '2023-07-26T14:47:15+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2307.14225v1', 'vector': '[-0.03801114112138748, 0.0031866198405623436, -0.035747215151786804, 0.02336220256984234, -0.03026813082396984, -0.006544461008161306, 0.018701177090406418, 0.009112780913710594, 0.0026634433306753635, -0.02766176126897335, -0.026063695549964905, -0.018587030470371246, -0.027871033176779747, 0.020299242809414864, -0.005493352189660072, 0.01999484933912754, -0.016570422798395157, -0.02659638412296772, -0.007200809195637703, -0.01628505438566208, 0.016532372683286667, 0.03960920870304108, 0.004513585474342108, 0.001580230426043272, 0.01636115275323391, 0.00028239632956683636, -0.03304572403430939, -0.03003983572125435, 0.030572524294257164, 0.04257704317569733, 0.029545197263360023, -0.02473197504878044, 0.04801807925105095, -0.037725772708654404, -0.019062643870711327, -0.0012211411958560348, 0.015971148386597633, -0.030534476041793823, -0.028803236782550812, 0.015067479573190212, -0.04402291402220726, -0.0005695488071069121, 0.013155507855117321, 0.010539625771343708, 0.0006426746258512139, -0.026425162330269814, -0.014087713323533535, -0.024446604773402214, -0.007990330457687378, -0.032817427068948746, -0.007196053396910429, -0.04512634128332138, 0.011633540503680706, -0.03820138797163963, 0.007362518459558487, 0.02216365374624729, 0.0011367195984348655, 0.012061593122780323, -0.08211015164852142, 0.022506097331643105, -0.017397992312908173, -0.024275382980704308, 0.0060307965613901615, -0.023533424362540245, 0.0019975826144218445, -0.0006248390418477356, -0.006639583967626095, 0.05836745724081993, -0.03315987065434456, -0.06658608466386795, -0.005312618333846331, 0.00794276874512434, -0.040484338998794556, -0.031238386407494545, -0.03861992806196213, 0.05250788480043411, -0.04223460331559181, -0.0019678566604852676, -0.04265314340591431, 0.01841580867767334, -0.030211057513952255, 0.004135471303015947, 0.03810626640915871, -0.011966470628976822, 0.002199718961492181, 0.0023317020386457443, -0.0021735599730163813, 0.028270548209547997, 0.028917385265231133, -0.008970096707344055, -0.0035480870865285397, 0.009750105440616608, 0.06768950819969177, -0.06011772155761719, -0.018368247896432877, -0.00520798284560442, -0.036774542182683945, 0.023856842890381813, 0.009374369867146015, -0.017007987946271896, -0.016047246754169464, 0.04501219466328621, 0.007809596601873636, -0.005859575234353542, 0.04314778372645378, -0.034282322973012924, -0.004615842364728451, 0.042310699820518494, 0.000562414585147053, -0.024408556520938873, 0.007899963296949863, 0.04204435646533966, 0.038353584706783295, 0.0462297685444355, -0.004301936831325293, -0.02556905709207058, 0.023229030892252922, -0.01959533430635929, 0.03340718895196915, -0.012622819282114506, 0.0005719268810935318, 0.01355502475053072, 0.05361131206154823, -0.03137155622243881, -0.022220727056264877, -0.001054676016792655, -0.008128258399665356, 0.014173323288559914, -0.020166071131825447, -0.019424112513661385, 0.04219655320048332, -0.0006753731286153197, -0.014725036919116974, 0.01830166019499302, -0.01833971031010151, -0.09968888014554977, -0.008756070397794247, 0.0020713028497993946, 0.010311330668628216, -0.01118646189570427, 0.04299558699131012, 0.03837260976433754, 0.0213455967605114, -0.06327580660581589, 0.05520937591791153, -0.005773964803665876, 0.028803236782550812, 0.03749747946858406, -0.022315850481390953, -0.07457641512155533, 0.02916470542550087, -0.003046313300728798, -0.0006498088478110731, 0.08667605370283127, -0.0017050793394446373, -0.015866512432694435, -0.01456332765519619, 0.04524048790335655, -0.008503993973135948, 0.047675635665655136, -0.010377916507422924, 0.009174611419439316, -0.008318504318594933, -0.0087893633171916, 0.03900042176246643, 0.02699590101838112, 0.03352133557200432, 0.026862729340791702, -0.026539310812950134, -0.021440718322992325, 0.03236083686351776, -0.024522703140974045, 0.01914825476706028, -0.027871033176779747, 0.015267238020896912, -0.002815640065819025, 0.06236262246966362, 0.009440955705940723, 0.011700126342475414, -0.016370665282011032, -0.04132142290472984, -0.028403721749782562, 0.011414756998419762, 0.00019574524776544422, -0.035461846739053726, -0.022106580436229706, 0.013126971200108528, -0.04368047043681145, 0.03449159115552902, -0.01371673308312893, -0.004104556515812874, -0.03315987065434456, 0.00548383966088295, 0.014582352712750435, 0.029773492366075516, -0.1090489849448204, 0.014277959242463112, -0.015200652182102203, -0.0438707172870636, -0.0675753653049469, -0.025550032034516335, 0.05193714424967766, -0.0033768657594919205, 0.019614357501268387, -0.06574900448322296, -0.013507463037967682, -0.03374963253736496, -0.02817542664706707, 0.01932898908853531, -0.020946079865098, 0.038315534591674805, 0.022334875538945198, 0.01760726235806942, 0.01819702610373497, -0.016646521165966988, 0.013269655406475067, -0.00031985100940801203, 0.06670023500919342, 0.018587030470371246, -0.04482194781303406, -0.03243693709373474, 0.04512634128332138, 0.03715503588318825, 0.060460165143013, -0.010149621404707432, 0.016969939693808556, 0.019424112513661385, 0.001891758176498115, 0.008951072581112385, 0.011861835606396198, 0.025911498814821243, 0.04094092920422554, 0.017949705943465233, 0.005997503641992807, -0.052660081535577774, -0.057644523680210114, 0.00305344769731164, 0.01786409504711628, 0.003619429189711809, 0.013488437980413437, 0.0054220096208155155, -0.037839919328689575, 0.008756070397794247, -0.004159252159297466, -0.06171578913927078, 0.017407504841685295, -0.01482016034424305, -0.0019167279824614525, -0.01577138900756836, -0.041892159730196, -0.004275777842849493, -0.011757199652493, -0.06628169119358063, -0.04870296269655228, 0.030953016132116318, -0.008613385260105133, 0.02931690216064453, -0.016637008637189865, -0.04010384902358055, -0.017160184681415558, 0.043414127081632614, -0.005921405274420977, -0.023171957582235336, 0.013269655406475067, 0.03403500095009804, 0.001888191094622016, -0.010083035565912724, -0.03863895311951637, 0.024960270151495934, 0.031790100038051605, -0.025759302079677582, -0.024998318403959274, 0.022429998964071274, 0.004266265779733658, 0.0023519156966358423, 0.03998969867825508, -0.039342865347862244, -0.036298926919698715, 0.060802608728408813, -0.01357404887676239, 0.006706169806420803, 0.04002774879336357, 0.03548087179660797, -0.01845385693013668, 0.02288658916950226, -0.0355759933590889, -0.04402291402220726, 0.006187749560922384, -0.000361467304173857, -0.048512719571590424, 0.024256359785795212, 0.00600701617076993, 0.045354634523391724, -0.009341076016426086, -0.02956422232091427, -0.020889006555080414, -0.021079251542687416, -0.1655900776386261, -0.004304314963519573, -0.023837817832827568, 0.0068250736221671104, 0.0036812592297792435, 0.030496425926685333, -0.023343179374933243, -0.0004075425094924867, 0.0004976120544597507, 0.012889163568615913, 0.011747688055038452, -0.036964789032936096, -0.013402828015387058, -0.0012199522461742163, -0.04436535760760307, 0.054752785712480545, 0.0002959216362796724, 0.015181627124547958, 0.04116922616958618, -0.01278452854603529, -0.01841580867767334, -0.07876182347536087, 0.024636851623654366, -0.008104478009045124, 0.003184241708368063, -0.011129388585686684, -0.016846278682351112, 0.028460795059800148, 0.0024565509520471096, -0.003276986535638571, -0.0018109036609530449, 0.027452491223812103, 0.0006468362407758832, 0.03401597589254379, -0.004268643446266651, -0.0013233984354883432, 0.006468362640589476, -0.015143577940762043, -0.03848675638437271, 0.03540477156639099, 0.014506254345178604, 0.027966154739260674, 0.002156913513317704, 0.028879335150122643, -0.03205644339323044, 0.028308598324656487, -0.0019048376707360148, -0.015761876478791237, 0.027243221178650856, -0.011348171159625053, 0.012736966833472252, -0.015238701365888119, -0.0450502410531044, -0.013983077369630337, -0.0825667455792427, 0.007224590051919222, 0.008836925029754639, 0.03413012623786926, 0.030895942822098732, -0.023457325994968414, -0.019053133204579353, -0.02284853905439377, 0.05125226080417633, -0.007148491684347391, -0.008465944789350033, 0.004808466415852308, 0.06346604973077774, 0.00799508672207594, 0.019024595618247986, -0.04946395009756088, 0.01727433316409588, -0.020603636279702187, 0.028517868369817734, -0.037002839148044586, 0.021745111793279648, 0.06358020007610321, -0.025949548929929733, -0.029450073838233948, 0.01041596569120884, -0.05441034212708473, 0.0004753176181111485, 0.0025992353912442923, 0.0004232972569297999, 0.003995165228843689, -0.019861677661538124, -0.03205644339323044, 0.04124532267451286, 0.009550346992909908, 0.07358713448047638, 0.2220550775527954, 0.031086189672350883, 0.019652407616376877, -0.03919066861271858, 0.024598801508545876, 0.03296962380409241, -0.025226613506674767, 0.018682152032852173, 0.04417511075735092, -0.017483603209257126, 0.04817027598619461, 0.018653616309165955, 0.005269812885671854, 0.022296825423836708, -0.021326571702957153, 0.030648622661828995, -0.035861361771821976, 0.00900338962674141, 0.026387114077806473, 0.010653773322701454, 0.004708587191998959, 0.005436277948319912, 0.05022493377327919, -0.013906979002058506, 0.01110085193067789, 0.010872555896639824, 0.017807021737098694, -0.006078358273953199, 0.04181605949997902, 0.0493878498673439, -0.024256359785795212, 0.036298926919698715, 0.03249400854110718, -0.005469571333378553, 0.016237491741776466, -0.01327916793525219, -0.003051069565117359, -0.0010404075728729367, -0.017949705943465233, 0.04908345639705658, -0.028593966737389565, -0.009536078199744225, -0.008660946972668171, 0.011975983157753944, -0.021516816690564156, -0.026919802650809288, 0.01168110128492117, 0.024636851623654366, 0.006411288864910603, -0.0034862570464611053, 0.005935673601925373, -0.003726442577317357, -0.014477716758847237, 0.021155349910259247, 0.023305129259824753, -0.010149621404707432, -0.010929630137979984, 0.019500210881233215, 0.0018002022989094257, 0.04406096413731575, -0.0026967364829033613, -0.01657993532717228, 0.02010899782180786, 0.0195572841912508, -0.012118667364120483, 0.01580943912267685, 0.02798517979681492, 0.0007580112433061004, 0.02876518853008747, 0.030953016132116318, 0.03972335532307625, 0.044631700962781906, 0.015933098271489143, 0.0027205171063542366, 0.031200336292386055, -0.05193714424967766, 0.052622031420469284, 0.05851965770125389, 0.009165098890662193, 0.029488123953342438, -0.0010368404909968376, 0.03118131123483181, 0.027832983061671257, 0.015305287204682827, 0.001784744905307889, 0.00858484860509634, 0.02509344182908535, 0.0667763277888298, -0.014991381205618382, -0.031048139557242393, -0.013887954875826836, -0.005231763701885939, 0.004563524853438139, 0.019614357501268387, 0.013393315486609936, -0.000411704124417156, 0.013193557038903236, -0.011947445571422577, -0.033806707710027695, 0.020147046074271202, 0.04291948676109314, 0.0012247083941474557, 0.03555696830153465, -0.02623491734266281, -0.016598960384726524, 0.012204278260469437, 0.011823786422610283, -0.007467153947800398, 0.005393472965806723, 0.07522325217723846, -0.05003468692302704, 0.02773785963654518, -0.016922377049922943, -0.06453143060207367, 0.038467731326818466, 0.05422009900212288, 0.008575336076319218, 0.04173996299505234, 0.004109312780201435, -0.03144765645265579, 0.0009672817541286349, 0.053725458681583405, -0.023133907467126846, 0.03344523906707764, 0.006492143031209707, -0.06023186817765236, 0.01153841707855463, 0.012385011650621891, -0.05791086703538895, -0.004482670221477747, 0.029773492366075516, -0.012337449938058853, -0.029183728620409966, 0.051518604159355164, 0.014382594265043736, -0.03976140543818474, 0.00776679115369916, 0.0003100414469372481, -0.010615724138915539, 0.01805434189736843, -0.04223460331559181, 0.010425478219985962, -0.000706288090441376, 0.023685621097683907, 0.011300609447062016, 0.03312182053923607, -0.029849590733647346, -0.01463942602276802, -0.029069582000374794, 0.05631280317902565, -0.0022924637887626886, 0.004354254342615604, 0.01650383695960045, -0.04402291402220726, -0.01647529937326908, -0.056807443499565125, -0.05113811418414116, 0.018663128837943077, 0.01724579557776451, -0.023780744522809982, 0.003873883280903101, 0.06380849331617355, 0.0017371834255754948, 0.03869602829217911, -0.0015255347825586796, 0.014087713323533535, 0.020166071131825447, 0.05745427682995796, -0.050757620483636856, -0.04798002913594246, -0.009569371119141579, -0.01932898908853531, -0.018767762929201126, 0.03458671644330025, 0.004268643446266651, 0.04615366831421852, -0.015010406263172626, 0.012641843408346176, -0.007148491684347391, -0.015219676308333874, -0.009536078199744225, -0.01808287762105465, -0.05140445753931999, 0.058862097561359406, -0.007828621193766594, 0.0007770358351990581, -0.022582195699214935, -0.007219833787530661, 0.029469098895788193, -0.019709480926394463, -0.028042253106832504, 0.09694933891296387, -0.06342799961566925, -0.0013519353233277798, 0.030724721029400826, 0.003103387076407671, 0.07366323471069336, 0.005869087763130665, 0.023305129259824753, -0.008737045340240002, -0.022068530321121216, -0.03251303359866142, -0.026843704283237457, -0.013050872832536697, -0.027871033176779747, 0.017835557460784912, -0.00781435240060091, 0.019576309248805046, -0.00906046386808157, -0.008261431008577347, 0.0013543133391067386, -0.018520444631576538, -0.0478278324007988, 0.006687145214527845, 0.0036883933935314417, -0.011937933973968029, -0.006691901478916407, 0.02905055694282055, -0.030496425926685333, 0.023932941257953644, 0.019243378192186356, -0.0182731244713068, -0.034244272857904434, -0.052812278270721436, -0.06137334555387497, -0.010244744829833508, 0.018472881987690926, 0.02366659604012966, -0.020013874396681786, 0.028156401589512825, -0.0055837188847362995, -0.02311488427221775, -0.004863162059336901, -0.00887972954660654, 0.01335526630282402, 0.03694576397538185, 0.00029146275483071804, 0.011281585320830345, -0.02450367994606495, -0.017084086313843727, -0.03669844567775726, -0.02593052387237549, 0.009364857338368893, 0.03580429032444954, 0.00677751237526536, -0.05102396383881569, -0.061867985874414444, 0.022734392434358597, 0.013193557038903236, 0.016570422798395157, -0.0013412339612841606, -0.013307704590260983, 0.002644418738782406, -0.012613306753337383, 0.028232499957084656, 0.009716812521219254, 0.029297877103090286, -0.011176950298249722, -0.034872084856033325, -0.012689405120909214, -0.011490855365991592, 0.042538996785879135, -0.009307783097028732, -0.021688038483262062, -0.07929451763629913, -0.037402354180812836, 0.017046038061380386, -0.027966154739260674, -0.01651334948837757, -0.07001051306724548, 0.037364304065704346, 0.008180576376616955, -0.01349795050919056, -0.08477360010147095, 0.060840655118227005, -0.019538259133696556, -0.02593052387237549, 0.025264663621783257, -0.03350231423974037, 0.01691286452114582, -0.10532016307115555, 0.057872820645570755, 0.01124353613704443, -0.0010463527869433165, -0.009193635545670986, -0.014924795366823673, 0.03639405220746994, -0.004183033015578985, 0.031010091304779053, -0.015790414065122604, 0.03785894438624382, 0.029450073838233948, -0.0565030500292778, 0.0053649358451366425, 0.02303878590464592, 0.02260121889412403, -0.017397992312908173, 0.060954805463552475, -0.0006634827586822212, -0.006710926070809364, -0.024770023301243782, -0.010111572220921516, -0.00414498383179307, 0.01302233524620533, -0.01573334075510502, 0.03296962380409241, -0.008275698870420456, -0.023057809099555016, 0.024998318403959274, 0.0513664074242115, 0.008470701053738594, -0.02747151628136635, 0.0007443372742272913, 0.03517647832632065, -0.03177107498049736, 0.02564515545964241, 0.05661719664931297, -0.01069182250648737, 0.006634827703237534, -0.004551634658128023, 0.03588038682937622, 0.025987597182393074, -0.005693110171705484, -0.008546799421310425, -0.02189730852842331, 0.006691901478916407, -0.023723671212792397, -0.022791465744376183, 0.026976875960826874, -0.0027133829426020384, -0.015153090469539165, -0.0450502410531044, 0.01570480316877365, -0.007471909746527672, 0.0031366802286356688, -0.02773785963654518, 0.023856842890381813, -0.006097382865846157, 0.03399695083498955, -0.009022414684295654, 0.0766691192984581, -0.033140845596790314, 0.03702186420559883, -0.03110521286725998, 0.021592915058135986, 0.01217574067413807, -0.04078873246908188, -0.018948497250676155, 0.0033602192997932434, -0.019500210881233215, 0.012717941775918007, 0.0003861398436129093, -0.02066071145236492, -0.032227665185928345, 0.02868909016251564, -0.034244272857904434, -0.026615409180521965, 0.06959196925163269, 0.009892789646983147, 0.033692557364702225, -0.027129072695970535, 0.002463685115799308, -0.018663128837943077, -0.022620243951678276, 0.02062266133725643, 0.011643052101135254, -0.01650383695960045, -0.07370128482580185, -0.003545708954334259, -0.0703149065375328, 0.024009039625525475, 0.06791780889034271, -0.015362360514700413, 0.011652564629912376, -0.051556654274463654, 0.031010091304779053, -0.019709480926394463, 0.025911498814821243, 0.023837817832827568, 0.027928106486797333, 0.01735043153166771, -0.0022984088864177465, 0.012546720914542675, -0.01976655423641205, -0.030933992937207222, -0.015019917860627174, -0.04173996299505234, -0.0038619928527623415, 0.015543094836175442, 0.06822220236063004, 0.004152117762714624, 0.01456332765519619, -0.012327938340604305, -0.004951151087880135, -0.13849905133247375, 0.0035076597705483437, -0.0006545649957843125, 0.008737045340240002, -0.014116249978542328, -0.011176950298249722, -0.0119569581001997, -0.03405402600765228, -0.018653616309165955, -0.05749232694506645, 0.01727433316409588, 0.046534162014722824, 0.038429681211709976, -0.03148570656776428, 0.004496938548982143, 0.03673649579286575, -0.08051209151744843, -0.04992054030299187, -0.020337292924523354, 0.01379283145070076, 0.011852323077619076, 0.020375341176986694, 0.03612770512700081, 0.030287155881524086, -0.045468784868717194, -0.00906046386808157, -0.03118131123483181, -0.009835716336965561, -0.02516954019665718, -0.002098650671541691, 0.023019760847091675, -0.03118131123483181, 0.027528589591383934, 0.05517132580280304, -0.007904719561338425, -0.019633382558822632, 0.022258777171373367, -0.04063653573393822, -0.025397835299372673, 0.0016884328797459602, 0.009731080383062363, -0.006016528233885765, -0.01804482936859131, 0.0296403206884861, -0.05041517689824104, 0.06026991829276085, -0.00432096142321825, -0.0119569581001997, -0.045392684638500214, -0.0015160224866122007, -0.008185332641005516, 0.025911498814821243, -0.04398486390709877, 0.009402906522154808, -0.008337529376149178, 0.013945028185844421, -0.030249107629060745, -0.0007782248430885375, -0.014782111160457134, 0.04177801311016083, -0.055704016238451004, 0.027319319546222687, -0.0282515250146389, -0.024446604773402214, 0.003626563586294651, -0.00565981725230813, -0.05969918146729469, -0.014934307895600796, -0.024085137993097305, 0.04010384902358055, -0.03660332038998604, -0.010235232301056385, -0.030020812526345253, 0.0029083851259201765, -0.02311488427221775, -0.04801807925105095, -0.02050851471722126, -0.013583561405539513, 0.012042568996548653, -0.01756921410560608, -0.03759260103106499, -0.008756070397794247, -0.021954383701086044, -0.023019760847091675, 0.02043241634964943, -0.007010563276708126, -0.027052974328398705, -0.015248212963342667, -0.07198906689882278, 0.0015184005023911595, -0.020736809819936752, -0.03717406094074249, -0.03019203245639801, 0.031238386407494545, -0.013621610589325428, 0.014002102427184582, -0.06784170866012573, -0.006901171989738941, 0.02307683415710926, -0.021326571702957153, -0.05539962276816368, 0.0450502410531044, -0.02343830093741417, 0.02003289945423603, 0.010634749196469784, 0.037250157445669174, -0.0024589290842413902, -0.011823786422610283, 0.00970254372805357, 0.043528273701667786, -0.03276035189628601, -0.02604467049241066, 0.01903410814702511, -0.00011652564717223868, -0.05033908039331436, -0.02461782656610012, 0.018862886354327202, 0.036888692528009415, 0.003284120699390769, 0.020128022879362106, 0.024199284613132477, -0.0008459999808110297, 0.008037891238927841, -0.01680823042988777, -0.00388339557684958, 0.08081648498773575, 0.041435569524765015, 0.010159133933484554, 0.0006813183426856995, -0.005745427682995796, 0.024009039625525475, -0.008270942606031895, -0.019471673294901848, 0.041473619639873505, 0.02663443423807621, 0.039418961852788925, -0.031238386407494545, 0.031752049922943115, -0.00042716163443401456, -0.010863044299185276, -0.025816375389695168, 0.008247162215411663, -0.02130754664540291, -0.028555918484926224, -0.024846121668815613, -0.01706506311893463, -0.0019048376707360148, -1.4584285054297652e-05, -0.052660081535577774, -0.0426911935210228, -0.02003289945423603, -0.0077905720099806786, -0.004984444007277489, -0.007800084073096514, -0.008256674744188786, 0.02802322991192341, 0.011291097849607468, -0.03960920870304108, -0.013364777900278568, 0.06342799961566925, 0.015438458882272243, -0.029849590733647346, -0.040294092148542404, -0.04204435646533966, -0.03591843694448471, 0.050757620483636856, 0.0101781589910388, -0.004884564783424139, -0.019538259133696556, 0.0009078299044631422, -0.0031652171164751053, 0.01258477009832859, -0.040332142263650894, -0.025378810241818428, 0.014335032552480698, -0.005693110171705484, 0.009845227934420109, 0.026881754398345947, 0.04272923991084099, -0.023229030892252922, 0.03441549465060234, -0.010425478219985962, -0.0037145523820072412, 0.013964053243398666, 0.0075147151947021484, 0.0019833140540868044, -0.028936410322785378, 0.04017994552850723, 0.06422703713178635, 0.019281428307294846, -0.03304572403430939, -0.0076526436023414135, 0.019348014146089554, -0.0035885144025087357, 0.03837260976433754, 0.0009322051773779094, 0.01911020651459694, 0.021288521587848663, 0.0011295853182673454, 0.012194765731692314, -0.05555181950330734, 0.016779692843556404, -0.00344820786267519, 0.0032294250559061766, -0.006572997663170099, -0.04421316087245941, -0.020812908187508583, -0.03331206738948822, -0.031276434659957886, 0.01302233524620533, 0.0012841601856052876, 0.008042647503316402, 0.022715367376804352, 0.005921405274420977, 0.0549430325627327, -0.023742694407701492, 0.002334080170840025, -0.005773964803665876, 0.0033530849032104015, -0.0005095024243928492, 0.0029155192896723747, 0.01074889674782753, -0.024522703140974045, -0.03673649579286575, -0.06803195178508759, -0.023248055949807167, 0.029925689101219177, 0.013906979002058506, 0.013126971200108528, 0.022353900596499443, 0.0006741841207258403, -0.039418961852788925, -0.011985494755208492, 0.007914232090115547, 0.029107630252838135, 0.04409901425242424, 0.01713164895772934, 0.002125998493283987, -0.07476665824651718, -0.048512719571590424, 0.02418026141822338, -0.023305129259824753, 0.060041625052690506, -0.020736809819936752]', 'distance': 0.665320634841919}, {'title': 'Exploring Prompt Engineering Practices in the Enterprise', 'authors': ['Michael Desmond', 'Michelle Brachman'], 'summary': 'Interaction with Large Language Models (LLMs) is primarily carried out via\\nprompting. A prompt is a natural language instruction designed to elicit\\ncertain behaviour or output from a model. In theory, natural language prompts\\nenable non-experts to interact with and leverage LLMs. However, for complex\\ntasks and tasks with specific requirements, prompt design is not trivial.\\nCreating effective prompts requires skill and knowledge, as well as significant\\niteration in order to determine model behavior, and guide the model to\\naccomplish a particular goal. We hypothesize that the way in which users\\niterate on their prompts can provide insight into how they think prompting and\\nmodels work, as well as the kinds of support needed for more efficient prompt\\nengineering. To better understand prompt engineering practices, we analyzed\\nsessions of prompt editing behavior, categorizing the parts of prompts users\\niterated on and the types of changes they made. We discuss design implications\\nand future directions based on these prompt engineering practices.', 'published': '2024-03-13T20:32:32+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2403.08950v1', 'vector': '[-0.07693386822938919, 0.0008029505261220038, 0.009014520794153214, -0.009406658820807934, -0.04503992572426796, -0.01435040682554245, -0.028962239623069763, -0.04339667782187462, 0.003779002698138356, -0.00011160195572301745, -0.014191684313118458, -0.00740395113825798, -0.02418188378214836, 0.000935414049308747, -0.03786939010024071, 0.005816723220050335, -0.027785824611783028, 0.006451614201068878, -0.008687738329172134, -0.02240792289376259, -0.019700298085808754, 0.003991411067545414, 0.05508614331483841, 0.0028780174907296896, -0.0016829282976686954, 0.017132723703980446, -0.030549466609954834, -0.03493768721818924, -0.002399515127763152, -0.013407407328486443, 0.0201858039945364, 0.006358248181641102, 0.025022180750966072, 0.009420664049685001, -0.02485411986708641, -0.018355822190642357, 0.004292517434805632, -0.004306522663682699, -0.037663981318473816, -0.012277673929929733, -0.042238932102918625, -0.004222492687404156, 0.016087021678686142, -0.030866913497447968, -0.008841793052852154, -0.0036086090840399265, -0.034601565450429916, -0.008444985374808311, 0.021231506019830704, -0.04474114999175072, -0.018318476155400276, 0.00013859066530130804, 0.045301347970962524, -0.05142618343234062, 0.00063372403383255, 0.0026982873678207397, -0.03898978605866432, -0.005830727983266115, -0.06311564892530441, 0.03936325013637543, -0.04179077595472336, 0.030400080606341362, 0.0033915324602276087, -0.05732693523168564, 0.008234911598265171, 0.010531723499298096, -0.02649736776947975, 0.030885586515069008, -0.016917981207370758, -0.06498297303915024, -0.002604920882731676, 0.003802344435825944, -0.03260352835059166, -0.007674713619053364, -0.05885814130306244, 0.04982028156518936, -0.013332713395357132, -0.014406426809728146, -0.03314505144953728, 0.050865985453128815, -0.046944595873355865, -0.01741282269358635, 0.004841045010834932, 0.0021334209013730288, 0.03824285417795181, -0.026590734720230103, -0.037626635283231735, 0.00659633195027709, 0.021343545988202095, -0.029055606573820114, -0.01048504002392292, -0.022706694900989532, 0.03241679444909096, -0.03581532835960388, -0.042201586067676544, 0.017646238207817078, -0.025806456804275513, 0.04332198202610016, -0.0193268321454525, -0.00675972318276763, 0.004609963390976191, 0.02565707080066204, -0.023920457810163498, -0.004689324647188187, 0.018262457102537155, -0.03415340930223465, 0.03936325013637543, 0.018953368067741394, 0.041492003947496414, 0.002649269998073578, -0.010148921050131321, 0.007179871667176485, 0.044666457921266556, 0.03245414048433304, -7.483895751647651e-05, -0.05221045762300491, 0.043060556054115295, 0.0035689284559339285, 0.009051866829395294, -0.021380892023444176, -0.009943515993654728, 0.050007011741399765, 0.04911069571971893, -0.032976992428302765, -0.03854162618517876, -0.023546991869807243, 0.009794129058718681, -0.013136644847691059, -0.04586154595017433, 0.0028079927433282137, 0.019606932997703552, 0.008557025343179703, -0.042201586067676544, 0.020521922037005424, -0.05953038111329079, -0.03801877424120903, -0.01861724816262722, 0.028308674693107605, -0.00139816093724221, -0.005293871741741896, 0.03435881435871124, 0.0420895479619503, -0.013472762890160084, -0.06102424114942551, 0.010344990529119968, -0.055608995258808136, 0.010270297527313232, 0.026908179745078087, -0.028402041643857956, -0.04283647984266281, 0.0030087304767221212, 0.03506840020418167, -0.013818218372762203, 0.03196863457560539, -0.00640026293694973, -0.025283604860305786, -0.04563746973872185, 0.028868872672319412, 0.03153914958238602, 0.059380993247032166, 0.007151861675083637, 0.020577942952513695, -0.02713225968182087, 0.020503249019384384, 0.016563190147280693, -0.031408436596393585, -0.006951124407351017, 0.0279352106153965, -0.03030671551823616, -0.038317546248435974, 0.022744040936231613, -0.0024158540181815624, 0.011829515919089317, 0.011418703943490982, 0.02470473386347294, 0.0067177084274590015, 0.04645909368991852, 0.0451519638299942, 0.024611368775367737, -0.031464457511901855, -0.001291956752538681, -0.02164231799542904, 0.00040760243427939713, -0.024518001824617386, -0.04832641780376434, 0.01045703049749136, -0.016731249168515205, -0.04970823973417282, 0.015890952199697495, -0.014481119811534882, 0.009850149042904377, -0.05142618343234062, 0.05766305327415466, 0.007324589416384697, 0.01819710060954094, -0.0621819831430912, 0.02646002173423767, -0.026217268779873848, -0.004808366764336824, -0.05187433958053589, -0.016880635172128677, 0.03434013947844505, -0.04391952604055405, -0.017991693690419197, -0.0463097058236599, -0.007539332378655672, -0.053106777369976044, -0.04029691219329834, -0.00252555962651968, -0.015209377743303776, 0.018916022032499313, 0.02100742794573307, 0.013799545355141163, -0.005970777478069067, -0.006745718419551849, 0.026833487674593925, -0.03168853744864464, 0.07850242406129837, -0.0101395845413208, -0.04257505387067795, -0.05982915312051773, 0.04743010178208351, -0.0232482198625803, 0.006974465679377317, -0.0034942354541271925, -0.005891416221857071, 0.05639326944947243, 0.005125812254846096, -0.00034428838989697397, 0.02102610096335411, 0.024984832853078842, 0.023845763877034187, -0.008048178628087044, -0.007529995404183865, -0.041379962116479874, -0.03714113309979439, 0.014266377314925194, 0.018019704148173332, 0.025470338761806488, 0.053181469440460205, 0.013239347375929356, -0.03665562719106674, 0.028103269636631012, 0.019606932997703552, -0.04029691219329834, -0.035273805260658264, -0.01548947673290968, 0.03174455836415291, -0.009980862028896809, -0.010242288000881672, -0.0015533824916929007, -0.006661688443273306, -0.04279913008213043, -0.023061485961079597, -0.015676209703087807, 0.009943515993654728, 0.007249896414577961, -0.029167646542191505, -0.045450735837221146, -0.002375006442889571, 0.03977406397461891, 0.016908645629882812, 0.008057515136897564, -0.006470287684351206, -0.004731339402496815, 0.01008356548845768, -0.028121942654252052, -0.022015783935785294, 0.010195604525506496, 0.029746517539024353, -0.05101536959409714, -0.028495408594608307, 0.029317032545804977, 0.0008770601125434041, -0.026572061702609062, 0.0434340238571167, -0.0732739046216011, -0.012072267942130566, 0.07517857849597931, 0.011698802933096886, -0.03984875604510307, 0.00029862640076316893, 0.031427111476659775, -0.027617763727903366, 0.03861631825566292, -0.026870833709836006, -0.045413389801979065, -0.030549466609954834, -0.02406984381377697, -0.045450735837221146, 0.04003548622131348, -0.018029041588306427, 0.07234024256467819, -0.002108912216499448, -0.025993190705776215, 0.0066056689247488976, 0.009093881584703922, -0.15162694454193115, 0.0028640124946832657, -0.008309604600071907, -0.003993745427578688, 0.013706179335713387, 0.008141545578837395, -0.01945754513144493, 0.006012792699038982, -0.03422810137271881, 0.005947436206042767, -0.00021342962281778455, -0.054712675511837006, 0.024312594905495644, 0.019606932997703552, -0.051724955439567566, 0.02031651698052883, -0.0195135660469532, 0.009906169027090073, -0.005289203487336636, -0.03710378333926201, -0.025358298793435097, -0.05501145124435425, 0.05829794332385063, 0.004502591677010059, -0.007319921161979437, -0.04264974594116211, 0.01167079247534275, 0.027431031689047813, -0.006124832201749086, 0.02016713097691536, -0.02716960571706295, 0.050081707537174225, -7.753782847430557e-05, 0.03702909126877785, 0.031408436596393585, 0.03400402143597603, -0.01785164512693882, -0.01471453532576561, 0.01400495134294033, 0.026964200660586357, 0.019812338054180145, 0.06995006650686264, 0.0021369222085922956, 0.01735680364072323, -0.03906447812914848, -0.016637882217764854, 0.008767100051045418, -0.005004435777664185, -0.01505999080836773, -0.027356337755918503, -0.024405961856245995, 0.003062416100874543, -0.04358340799808502, 0.014611832797527313, -0.04567481577396393, -0.03398535028100014, 0.01235236693173647, -0.011138604953885078, -0.009775456041097641, 0.007987490855157375, -0.03185659646987915, -0.0006780730909667909, 0.005956772714853287, -0.06341441720724106, 0.002065730281174183, 0.00623220345005393, 0.07506653666496277, 0.005097802262753248, 0.005139817018061876, -0.0388403981924057, 0.00042890163604170084, -0.03491901233792305, 0.011017228476703167, -0.006376921199262142, 0.02474208176136017, 0.05150087550282478, 0.02393913082778454, 0.017842307686805725, 0.004224827047437429, -0.07521592825651169, -0.02558237873017788, 0.0068250796757638454, 0.010074228048324585, 0.039886102080345154, -0.006722376681864262, -0.0028196636121720076, 0.06550582498311996, -0.009121892042458057, 0.06938986480236053, 0.22392983734607697, 0.015620188787579536, 2.5233530323021114e-06, 0.017618229612708092, 0.057924479246139526, 0.009061203338205814, -0.02251996286213398, 0.03637552633881569, 0.013005931861698627, -0.00775874312967062, 0.038504280149936676, 0.03318239748477936, 0.030829565599560738, 0.04716867581009865, 0.017002010717988014, -0.010671772994101048, -0.05194903165102005, 0.0034778963308781385, 0.07738202810287476, -0.04728071764111519, 0.017020683735609055, 0.04022222012281418, 0.03258485347032547, -0.02800990268588066, -0.04250035807490349, -0.02485411986708641, 0.01872928813099861, -0.009458011016249657, 0.01398627832531929, 0.024461982771754265, -0.03110966645181179, 0.03824285417795181, 0.05060455948114395, 0.026049209758639336, 0.007716728374361992, 0.034564219415187836, -0.01674058474600315, -0.048438459634780884, 0.00621819868683815, 0.011558753438293934, -0.012296346947550774, 0.0005234350683167577, -0.01741282269358635, -0.023640358820557594, 0.026590734720230103, -0.017188744619488716, -0.005027777515351772, 0.022090476006269455, -0.014210357330739498, -0.004897064529359341, -0.004297186154872179, 0.012025585398077965, -0.009868822060525417, 0.005018441006541252, 0.012977921403944492, 0.010979881510138512, -0.01166145596653223, -0.02100742794573307, -0.006082817446440458, 0.03719715029001236, 0.015340089797973633, 0.02406984381377697, 0.02083936706185341, 0.022632000967860222, -0.003867700695991516, 0.02786051668226719, 0.0017109382897615433, 0.006148173473775387, 0.04896131157875061, 0.016917981207370758, 0.06516970694065094, 0.04119323194026947, -0.00974744651466608, -0.015946971252560616, 0.03028804250061512, -0.034452181309461594, 0.053928401321172714, 0.05355493351817131, -0.02477942779660225, -0.011792168952524662, -0.020764674991369247, 0.03041875548660755, 0.012212317436933517, 0.014490456320345402, 0.02638532780110836, 0.0003568344982340932, -0.013024604879319668, 0.062443409115076065, -0.007072500418871641, 0.035385843366384506, 0.018803982064127922, -0.021194159984588623, 0.00847766362130642, -0.009990198537707329, -0.007343262899667025, 0.014191684313118458, 0.01934550702571869, -0.07372206449508667, -0.016619209200143814, 0.025302279740571976, 1.425113259756472e-05, -0.008337614126503468, 0.020409882068634033, 0.00406143581494689, 0.005004435777664185, -0.009943515993654728, -0.027673784643411636, 0.05534756928682327, 0.004278512671589851, 0.0742449164390564, -0.036170121282339096, 0.010410347022116184, 0.013024604879319668, -0.07790488004684448, 0.01861724816262722, 0.052322499454021454, 0.011484060436487198, 0.012977921403944492, 0.03017600253224373, -0.008136876858770847, -0.02341627888381481, 0.06770927459001541, -0.030829565599560738, 0.024966159835457802, -0.048550497740507126, -0.03631950914859772, 0.043023210018873215, 0.02406984381377697, -0.005812054965645075, -0.006190188694745302, 0.020689981058239937, 0.019588258117437363, 0.013248683884739876, 0.0526212714612484, 0.030773546546697617, -0.004574950784444809, 0.028364695608615875, 0.006554317194968462, -0.011250643990933895, 0.008519678376615047, -0.02709491364657879, 0.021997110918164253, -0.012707158923149109, -0.0005021358374506235, 0.019700298085808754, 0.06528174877166748, -0.0662154108285904, 0.010681109502911568, -0.015909625217318535, 0.056281231343746185, 0.024630041792988777, 0.020055091008543968, 0.03639420121908188, -0.017692921683192253, -0.02554503083229065, -0.041604042053222656, -0.01949489302933216, 0.008841793052852154, 0.02255730889737606, -0.028458060696721077, -0.010167594999074936, 0.051575567573308945, 0.02711358666419983, 0.022034456953406334, 0.002583913505077362, 0.017655575647950172, 0.01863592118024826, 0.06296626478433609, 0.016255080699920654, -0.05277065560221672, 0.022053129971027374, 0.001868493971414864, -0.030530793592333794, 0.030754873529076576, -0.007366604637354612, 0.03658093512058258, -0.05512348935008049, -0.008692406117916107, -0.026870833709836006, -0.033705249428749084, 0.006993139162659645, 0.006302228197455406, -0.047728873789310455, 0.06132301315665245, -0.009266609326004982, -0.03658093512058258, 0.005975445732474327, 0.005966109223663807, 0.036842357367277145, -0.004206153564155102, -0.05725224316120148, 0.08679335564374924, -0.029223665595054626, -0.005363896489143372, 0.024574020877480507, 0.027636436745524406, 0.06815742701292038, -0.039101824164390564, 0.007137856911867857, -0.07062230259180069, -0.012940575368702412, -0.023042812943458557, -0.0003944727941416204, -0.0020027081482112408, -0.03030671551823616, 0.025096872821450233, -0.050865985453128815, 0.007030485663563013, -0.03932590410113335, -0.006334906443953514, 0.00252322549931705, 0.009934178553521633, -0.07338594645261765, 0.010354327037930489, 0.026273289695382118, -0.01316465437412262, -0.00034895670250989497, 0.04403156787157059, -0.000671070592943579, 0.008197564631700516, 0.030792219564318657, -0.01679660566151142, -0.016329772770404816, -0.042388319969177246, -0.022295882925391197, 0.003977406304329634, 0.01130666397511959, -0.0263106357306242, -0.006162178702652454, 0.006488960701972246, -0.0023761733900755644, -0.014779891818761826, -0.018888011574745178, 0.016731249168515205, 0.015237387269735336, 0.06404931098222733, 0.007025817409157753, 0.02082069404423237, -0.027524398639798164, -0.015013308264315128, -0.04952150955796242, 0.00042189916712231934, 0.028140615671873093, 0.04130527004599571, 0.004628636408597231, -0.030493447557091713, -0.02795388363301754, -0.012324357405304909, 0.00675972318276763, 0.028943566605448723, 0.03648756816983223, 0.013136644847691059, -0.014378417283296585, -0.02341627888381481, 0.029970595613121986, -0.023640358820557594, 0.0006109660025686026, -0.029765190556645393, -0.04447972774505615, 0.003821017686277628, -0.012417723424732685, 0.008986510336399078, 0.003078755224123597, -0.014070307835936546, -0.06206994503736496, -0.04634705185890198, -0.0015487142372876406, 0.009467347525060177, 0.01118528749793768, -0.06154709309339523, 0.023584337905049324, -0.0034638913348317146, 0.002548901131376624, -0.0793987363576889, 0.004376547411084175, -0.017272774130105972, -0.0068670944310724735, -0.00414779968559742, 0.046869903802871704, -0.001301293377764523, -0.08275992423295975, 0.03344382345676422, 0.0031254382338374853, -0.007025817409157753, 0.005480604246258736, -0.014863922260701656, 0.0401848740875721, -0.014294386841356754, 0.01597498171031475, -0.003772000316530466, 0.04403156787157059, 0.02955978363752365, -0.02014845609664917, -0.04164138808846474, 0.021268852055072784, 0.01564819924533367, -0.02636665478348732, 0.09090147167444229, -0.02573176473379135, 0.01798235811293125, -0.031464457511901855, 0.02550768479704857, 0.029167646542191505, 0.01167079247534275, -0.06464685499668121, 0.03247281536459923, -0.0279352106153965, -0.03891509026288986, 0.07144392281770706, 0.010783812962472439, -0.014135664328932762, -0.023565664887428284, 0.01435974333435297, 0.02332291193306446, 0.02031651698052883, 0.027692457661032677, 0.021903743967413902, -0.005686010234057903, 0.030007943511009216, 0.0066803619265556335, 0.01000887155532837, 0.010513050481677055, -0.030437428504228592, 0.0011810841970145702, -0.03796275332570076, 0.007025817409157753, -4.726671249954961e-05, -0.04104384407401085, 0.017496852204203606, 0.013500773347914219, -0.04940946772694588, -0.043060556054115295, -0.005564634222537279, -0.003309836843982339, 0.0015242055524140596, -0.03473227843642235, 0.034452181309461594, 0.031408436596393585, 0.024648714810609818, -0.03816816210746765, 0.060986895114183426, -0.07163065671920776, 0.021362219005823135, 0.01000887155532837, 0.03114701248705387, 0.0075253271497786045, -0.012707158923149109, 0.02171701192855835, 0.029167646542191505, -0.02556370384991169, 7.914256275398657e-05, -0.02952243760228157, -0.011876198463141918, -0.04959620162844658, 0.006727044936269522, -0.01704869419336319, -0.015302743762731552, 0.013015268370509148, 0.010951871983706951, 0.040745072066783905, -0.010765139013528824, -0.007072500418871641, -0.019868357107043266, -0.03404136747121811, 0.00875309482216835, 0.00243686162866652, 0.009971525520086288, -0.02024182304739952, -0.005854069720953703, -0.06864293664693832, 0.005746698472648859, 0.05654265731573105, -0.002057560719549656, -0.01710471324622631, 0.004159470554441214, 0.0340600423514843, 0.004280847031623125, 0.007319921161979437, 0.030586814507842064, -0.006493628956377506, -0.008029505610466003, -0.010232951492071152, 0.0008648057701066136, -0.03570329025387764, -0.008627050556242466, 0.009140565060079098, -0.027692457661032677, 0.011222634464502335, 0.03097895346581936, 0.019252140074968338, -0.0029363716021180153, 0.027505725622177124, -0.03932590410113335, 0.01938285306096077, -0.12794923782348633, 0.015592179261147976, 0.006624341942369938, 0.032192714512348175, -0.04365810379385948, -0.03036273457109928, 0.0073012481443583965, -0.026945525780320168, 0.003179124090820551, -0.053965747356414795, 0.013706179335713387, -0.022071802988648415, 0.02007376402616501, -0.012053594924509525, -0.013547456823289394, 0.03572196140885353, -0.05908222123980522, -0.018001031130552292, 0.012865882366895676, 0.029690496623516083, 0.0023726720828562975, -0.002364502754062414, 0.029298357665538788, 0.016917981207370758, -0.017618229612708092, -0.023733723908662796, -0.0047593493945896626, -0.034433506429195404, -0.007866114377975464, -0.03667429834604263, -0.03041875548660755, -0.050865985453128815, 0.03977406397461891, 0.050044361501932144, -0.030026616528630257, -0.0017424493562430143, -0.014985297806560993, -0.010709119960665703, -0.028290001675486565, -0.0162644162774086, 0.0334811694920063, -0.00387236918322742, -0.05194903165102005, 0.010391674004495144, -0.021567625924944878, 0.08768966794013977, -0.014565149322152138, 0.0017284444766119123, -0.04511461779475212, 0.0061341687105596066, -0.009355307556688786, 0.061621785163879395, -0.06924048066139221, 0.015265396796166897, 0.013603475876152515, 0.02177303098142147, -0.04328463599085808, 0.017720932140946388, -0.020540595054626465, 0.06968864053487778, -0.03415340930223465, 0.014546476304531097, -0.02638532780110836, -0.006918446160852909, -0.002005042275413871, -0.017991693690419197, -0.03704776614904404, -0.0029783863574266434, -0.06128566712141037, 0.021343545988202095, 0.004474582150578499, 0.013117970898747444, -0.022669348865747452, -0.014574485830962658, 0.029671823605895042, -0.00046683172695338726, -0.0014156671240925789, -0.004920406267046928, -0.022818734869360924, 0.0023656697012484074, -0.032136693596839905, -0.016535179689526558, -0.04261239990592003, 0.008809114806354046, 0.036842357367277145, -0.024592693895101547, -0.011035901494324207, -0.008855797350406647, -0.04552542790770531, -0.015890952199697495, -0.050828635692596436, -0.023696377873420715, -0.04989497363567352, -0.019868357107043266, -0.01080248598009348, 0.028308674693107605, -0.041529349982738495, 0.012884555384516716, 0.00378600531257689, -0.06834416091442108, -0.0365435853600502, 0.04869988560676575, 0.01279118936508894, 0.014294386841356754, 0.010344990529119968, 0.049372121691703796, 0.022781386971473694, 0.006881099659949541, 0.04022222012281418, 0.03420942649245262, -0.04911069571971893, -0.01662854664027691, -0.003312170971184969, -0.0280285757035017, -0.03493768721818924, 0.01201624795794487, 0.036879707127809525, 0.050828635692596436, 0.0063209012150764465, -0.002667943248525262, 0.023808417841792107, -0.03241679444909096, -0.03861631825566292, -0.020447229966521263, -0.015386773273348808, 0.08462725579738617, 0.025769110769033432, 0.01434107031673193, 0.008524347096681595, -0.009467347525060177, 0.014929277822375298, -0.020465902984142303, -0.01741282269358635, 0.01852388307452202, 0.006082817446440458, 0.04253770411014557, -0.03932590410113335, 0.02255730889737606, -0.01247374340891838, -0.005009104497730732, -0.05120210349559784, -0.008057515136897564, -0.0026982873678207397, -0.049260083585977554, -0.013836892321705818, -0.012221653945744038, 0.016152378171682358, -0.012128287926316261, -0.010886515490710735, -0.047056637704372406, 0.00659166369587183, 0.007501985877752304, 0.021436912938952446, 0.029148971661925316, 0.03828020021319389, 0.031408436596393585, -0.04713132977485657, 0.03973671421408653, 0.031203031539916992, 0.07151862233877182, 0.03568461537361145, -0.020746001973748207, -0.031221704557538033, -0.05508614331483841, -0.017207417637109756, 0.021530278027057648, 0.0043952204287052155, 0.004227161407470703, -0.023434951901435852, 0.01010223850607872, 0.02567574381828308, 0.03200598061084747, -0.03204333037137985, -0.0006179685005918145, -0.0193268321454525, -0.020521922037005424, 0.008192896842956543, 0.033798616379499435, -0.011390693485736847, -0.021959763020277023, 0.03342515230178833, 0.02883152663707733, 0.016068346798419952, 0.013080624863505363, 0.005531955976039171, -0.01083983201533556, -0.012305684387683868, 0.03262219950556755, 0.024984832853078842, 0.005784044973552227, -0.018934695050120354, -0.007791421376168728, 0.033070359379053116, -0.01201624795794487, 0.03476962447166443, 0.006881099659949541, 0.04194016009569168, 0.03936325013637543, -0.0021672663278877735, 0.03947528824210167, -0.039960794150829315, 0.016983337700366974, 0.014070307835936546, -0.02713225968182087, -0.001991037279367447, -0.01316465437412262, -0.000505053554661572, -0.03947528824210167, -0.034638911485672, -0.023808417841792107, 0.030680179595947266, 0.0014506796142086387, -0.01781429909169674, 0.042201586067676544, -0.0036179458256810904, 0.006923114415258169, 0.06416135281324387, 0.01681527867913246, 0.03396667540073395, -0.005994119215756655, 0.013584802858531475, 0.019569585099816322, 0.009327298030257225, -0.014835911802947521, -0.05590776726603508, 0.00013910126290284097, -7.826725777704269e-05, -0.01209094189107418, -0.02875683456659317, 0.03346249833703041, -0.022295882925391197, -0.003989077173173428, 0.0233415849506855, 0.03166986256837845, 0.0010608751326799393, 0.021418239921331406, 0.018029041588306427, -0.0171233881264925, -0.009336634539067745, -0.05926895514130592, 0.04589889571070671, -0.0033915324602276087, 0.02476075477898121, 0.0034522204659879208]', 'distance': 0.6670231223106384}, {'title': 'Reasoning with Language Model Prompting: A Survey', 'authors': ['Shuofei Qiao', 'Yixin Ou', 'Ningyu Zhang', 'Xiang Chen', 'Yunzhi Yao', 'Shumin Deng', 'Chuanqi Tan', 'Fei Huang', 'Huajun Chen'], 'summary': 'Reasoning, as an essential ability for complex problem-solving, can provide\\nback-end support for various real-world applications, such as medical\\ndiagnosis, negotiation, etc. This paper provides a comprehensive survey of\\ncutting-edge research on reasoning with language model prompting. We introduce\\nresearch works with comparisons and summaries and provide systematic resources\\nto help beginners. We also discuss the potential reasons for emerging such\\nreasoning abilities and highlight future research directions. Resources are\\navailable at https://github.com/zjunlp/Prompt4ReasoningPapers (updated\\nperiodically).', 'published': '2022-12-19T16:32:42+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2212.09597v8', 'vector': '[-0.04496333375573158, -0.004332015756517649, 0.005083582829684019, -0.021548043936491013, -0.0060732243582606316, -0.052955154329538345, 0.004593430552631617, -0.016553154215216637, 0.007034857291728258, -0.02109990268945694, -0.018924560397863388, 0.00552705442532897, -0.03458143398165703, -0.011744990944862366, -0.016179705038666725, 0.043096087872982025, -0.017580140382051468, -0.006577381398528814, -0.015554176643490791, -0.028102083131670952, -0.02244432270526886, -0.018933895975351334, 0.07454054057598114, 0.006652071140706539, -0.016254395246505737, -0.0018555778078734875, -0.03361046686768532, -0.033479757606983185, 0.01686125062406063, -0.0003781177510973066, 0.02145468071103096, -0.03142578527331352, 0.05900504067540169, 0.01983017474412918, -0.021062558516860008, -0.03469346836209297, -0.012986710295081139, -0.01716000959277153, -0.031239060685038567, 0.020875833928585052, -0.015964971855282784, -0.01945672556757927, 0.015750236809253693, -0.007039525546133518, 0.015936963260173798, -0.020240969955921173, 0.008435293100774288, -0.018635135143995285, 0.005555063020437956, -0.03652337193489075, -0.04089273512363434, 0.007202909328043461, 0.05571868270635605, -0.07715468853712082, -0.017290716990828514, 0.016459792852401733, -0.07136622071266174, 0.030286764726042747, -0.07017118483781815, 0.02377006784081459, -0.04619571939110756, 0.0016653519123792648, -0.004077603109180927, -0.03439471125602722, 0.03962300345301628, 0.0035711119417101145, 0.0016455124132335186, 0.04182635620236397, -0.02011026255786419, -0.056876376271247864, 0.02315387688577175, 0.017272045835852623, -0.05609213188290596, 0.010120484977960587, -0.06400926411151886, 0.010447253473103046, -0.007450319826602936, -0.028512878343462944, -0.0469052717089653, 0.02875562012195587, -0.030585523694753647, 0.00487351743504405, -0.014312456361949444, -0.007240254431962967, -0.0062226043082773685, -0.023247238248586655, -0.011838353238999844, 0.03557107597589493, 0.00016338420391548425, 0.00785644631832838, -0.023545999079942703, 0.01652514562010765, -0.019111284986138344, -0.03788646310567856, -0.02739252895116806, 0.008622017689049244, -0.06968569755554199, 0.018532438203692436, -0.009210200980305672, 0.01848575659096241, 0.0251331590116024, 0.01587160862982273, 0.010288537479937077, -0.007198241539299488, 0.01201574131846428, -0.03471214324235916, -0.016357094049453735, 0.018046952784061432, 0.029502520337700844, -0.010008449666202068, -0.00330502912402153, 0.026851028203964233, 0.022350959479808807, 0.06938693672418594, 0.012221138924360275, -0.029539864510297775, 0.03495488315820694, -0.007272931281477213, 0.01105410885065794, -0.005167609080672264, 0.013098745606839657, 0.027934031561017036, 0.029801279306411743, -0.013509539887309074, -0.05766062065958977, 0.04085538908839226, -0.008388612419366837, -0.032882239669561386, 0.010055131278932095, -0.00474047614261508, 0.026178818196058273, -0.002810208359733224, -0.008309254422783852, 0.005713779013603926, -0.02216423489153385, -0.034133296459913254, -0.016301076859235764, -0.005466368980705738, -0.023844758048653603, -0.06180591136217117, 0.07211311906576157, 0.061171047389507294, -0.014620552770793438, -0.018336376175284386, 0.015666211023926735, -0.08208422362804413, 0.017057310789823532, 0.00252078496851027, -0.0032326732762157917, -0.012902684509754181, 0.01306140050292015, 0.03984707593917847, 0.003484751796349883, 0.07439116388559341, -0.030566850677132607, -0.0008752725552767515, -0.02670164778828621, 0.025674661621451378, 0.013444186188280582, 0.018588455393910408, 0.010428580455482006, 0.032172683626413345, -0.028961017727851868, 0.042722634971141815, 0.01973681151866913, -0.02345263585448265, -0.006381320301443338, 0.03273285925388336, -0.04425378143787384, -0.056577619165182114, 0.02149202488362789, -0.016039662063121796, 0.02278042770922184, 0.022257598116993904, 0.006680080201476812, 0.0015661543002352118, 0.08462368696928024, 0.04683058336377144, 0.03495488315820694, -0.014135068282485008, -0.04126618430018425, 0.020651763305068016, -0.010260527953505516, -0.02283644489943981, -0.023041842505335808, 0.014051041565835476, -0.04081804305315018, -0.059602558612823486, 0.030118711292743683, 0.017374742776155472, 0.008267240598797798, -0.05105056241154671, 0.04376829415559769, 0.025954749435186386, 0.004957543686032295, -0.06042414903640747, 0.0012055420083925128, -0.04989286884665489, -0.013602902181446552, -0.025861386209726334, -0.0016735211247578263, 0.042349185794591904, -0.029297122731804848, 0.000718890514690429, -0.03002534992992878, -0.03639266639947891, -0.04996756091713905, -0.0750260278582573, -0.0014027701690793037, -0.0542248860001564, 0.011913042515516281, 0.03471214324235916, 0.01322011649608612, 0.041415564715862274, -0.017860228195786476, 0.014984666369855404, -0.02836349792778492, 0.013901662081480026, 0.015292761847376823, -0.03230339288711548, -0.03570178523659706, 0.08678968995809555, -0.02016627974808216, 0.013808299787342548, -0.023994138464331627, -0.03691549599170685, 0.02737385779619217, 0.04518740251660347, 0.025880059227347374, 0.030790921300649643, 0.01973681151866913, 0.005415019579231739, -0.0017960593104362488, -0.052282948046922684, 0.0007019685581326485, -0.03224737569689751, 0.008724716491997242, -0.00181473174598068, -0.006357979960739613, 0.03932424634695053, -0.017206691205501556, -0.046718548983335495, 0.01953141577541828, 0.017505450174212456, -0.0647188201546669, -0.016991958022117615, 0.043730951845645905, 0.044104401022195816, -0.04089273512363434, -0.015124709345400333, -0.02907305210828781, 0.011119462549686432, -0.07076870650053024, -0.019886191934347153, 0.0031649854499846697, -0.013182771392166615, 0.00838394369930029, -0.002457765396684408, -0.006329970899969339, -0.0030646210070699453, 0.02149202488362789, 0.01372427400201559, 0.012342509813606739, 0.012071759440004826, 0.023303255438804626, -0.027784651145339012, -0.03277020528912544, 1.5900785001576878e-05, 0.00569510692730546, 0.03398391604423523, -0.03239675611257553, -0.019886191934347153, 0.010633978061378002, -0.022014854475855827, -0.0005741788190789521, 0.030249418690800667, -0.02216423489153385, -0.0336478091776371, 0.045710232108831406, -0.0038465310353785753, -0.027653943747282028, 0.023265911266207695, 0.02673899196088314, -0.0028382171876728535, 0.030100040137767792, -0.02933446690440178, 0.0016151695745065808, -0.03751301392912865, 0.01781354658305645, -0.06841596961021423, 0.010139157064259052, 0.004591096192598343, 0.060274768620729446, -0.012977374717593193, -0.0033587124198675156, 0.0024134181439876556, 0.021510697901248932, -0.15296496450901031, 0.015292761847376823, -0.003370382823050022, 0.025655988603830338, 0.007487664930522442, 0.053963471204042435, -0.03198596090078354, 0.005340329371392727, -0.015759574249386787, 0.02776597999036312, 0.010288537479937077, -0.05385143682360649, -0.014732587151229382, 0.018429739400744438, -0.020035572350025177, 0.04029521346092224, -0.004822168033570051, 0.005475705023854971, -0.006759438198059797, -0.012314501218497753, -0.019307345151901245, -0.05478505790233612, 0.010605969466269016, -0.012127776630222797, -0.004910862538963556, -0.023303255438804626, 0.008916109800338745, -0.004047260154038668, 0.007002180442214012, -0.018215004354715347, -0.005970526020973921, -0.004812831990420818, -0.0029852630104869604, 0.024647675454616547, 0.003288690699264407, 0.06445740163326263, 0.019344691187143326, -0.03461878001689911, -0.0401831790804863, 0.02537590079009533, 0.0006570379482582211, 0.07663185894489288, 0.0018369053723290563, 0.03669142350554466, -0.034151967614889145, -0.0028522214852273464, 1.4268765880842693e-05, -0.043096087872982025, -0.013518876396119595, -0.023602016270160675, -0.019568759948015213, -0.02742987498641014, -0.045747578144073486, -0.0003504007763694972, -0.08118794858455658, -0.02739252895116806, -0.00012355930812191218, -0.00789845921099186, 0.02380741387605667, 0.002583804540336132, 0.022687064483761787, -0.04462723061442375, 0.037083547562360764, -0.0732334703207016, -0.04776420816779137, -0.029577210545539856, 0.07416709512472153, 0.004616770893335342, -0.002975926734507084, -0.009952432475984097, 0.05276843160390854, -0.026925718411803246, -0.01878451555967331, 0.00010007282980950549, 0.05474771559238434, 0.030529506504535675, 0.026533596217632294, 0.034786831587553024, 0.024965107440948486, -0.06957366317510605, -0.009457611478865147, 0.0158342644572258, 0.0023200558498501778, 0.018756506964564323, 0.004411373753100634, -0.0008892769110389054, 0.029297122731804848, 0.012687950395047665, 0.037139564752578735, 0.2295968234539032, 0.02640288881957531, -0.013686928898096085, -0.05881831422448158, 0.04649447649717331, -0.016338421031832695, -0.019270000979304314, 0.03222870081663132, 0.03202330321073532, -0.028774293139576912, 0.02371405065059662, 0.05844486504793167, -0.0022033527493476868, 0.03936158865690231, 0.018093634396791458, 0.037774428725242615, -0.056278858333826065, -0.015964971855282784, 0.07902193814516068, -0.022201579064130783, 0.03430134803056717, 0.015983643010258675, 0.006329970899969339, -0.016058333218097687, -0.043021395802497864, 0.011931715533137321, 0.04739075526595116, -0.005116259679198265, 0.007314944639801979, 0.027653943747282028, -0.032844893634319305, 0.032826222479343414, 0.052320290356874466, -0.0031183043029159307, 0.016795895993709564, -0.01710399240255356, -0.014452500268816948, -0.014751260168850422, -0.04119149222970009, 0.038465309888124466, -0.008435293100774288, -0.013108082115650177, -0.032788876444101334, 0.007982485927641392, -0.026253508403897285, -0.040108490735292435, 0.007370961830019951, 0.019251327961683273, -0.009042148478329182, -0.02042769454419613, -0.025655988603830338, -0.0161330234259367, 0.0008536825189366937, 0.024535639211535454, 0.010465925559401512, 0.019344691187143326, -0.041079457849264145, 0.02414351888000965, 0.001700362772680819, 0.04791358485817909, 0.027261821553111076, 0.0005091168568469584, -0.005648425314575434, 0.006157250609248877, -0.00347074749879539, 0.0009377086535096169, -0.0063206348568201065, 0.01483528595417738, 0.05967725068330765, 0.03835327550768852, 0.030548179522156715, 0.04122883826494217, 0.036822132766246796, -0.01951274275779724, 0.022033527493476868, -0.02446095086634159, -0.00975637137889862, 0.04795093089342117, 0.026346871629357338, 0.0035711119417101145, 0.00976570788770914, 0.0037975157611072063, 0.03906283155083656, -0.02406882867217064, 0.012538570910692215, -0.008906773291528225, 0.01533010695129633, 0.05732451751828194, 0.0006366149173118174, 0.004726471845060587, -0.03530966117978096, -0.022556357085704803, -0.02571200579404831, -0.0032280052546411753, 0.019064603373408318, 0.009028144180774689, 0.0048034959472715855, -0.027523236349225044, -0.014144404791295528, -0.018905887380242348, 0.07095542550086975, 0.003354044398292899, 0.025898730382323265, -0.027280494570732117, 0.04813765734434128, -0.02240697667002678, 0.02012893371284008, 0.01848575659096241, -0.005368338432163, 0.04119149222970009, -0.028979690745472908, 0.004147625062614679, 0.012155785225331783, -0.06061087176203728, 0.01660917140543461, 0.04585961252450943, 0.0012989044189453125, 0.02545059099793434, 0.02638421580195427, 0.009971104562282562, -0.018233677372336388, 0.06568978726863861, -0.03172454610466957, 0.013686928898096085, -0.0017610483337193727, -0.02608545683324337, 0.02574935182929039, 0.027541909366846085, -0.014163076877593994, -0.0025581298395991325, 0.022593701258301735, 0.004623773042112589, 0.01239852700382471, 0.052992500364780426, 0.02074512653052807, -0.0026374878361821175, 0.03196728602051735, 0.0332556888461113, -0.011072780936956406, 0.03243409842252731, -0.027952704578638077, 0.005583071615546942, -0.001832237234339118, -0.029819952324032784, 0.011801008135080338, 0.042349185794591904, -0.03497355803847313, 0.004105611704289913, -0.01951274275779724, 0.028811637312173843, 0.03241542726755142, 0.023209894075989723, 0.044067054986953735, -0.04944473132491112, 0.03439471125602722, -0.020577074959874153, -0.04776420816779137, -0.01767350360751152, 0.03674744442105293, -0.023228567093610764, -0.030529506504535675, 0.05370205640792847, 0.028456861153244972, 0.0441417433321476, -0.037176910787820816, 0.04089273512363434, -0.027299167588353157, 0.053963471204042435, -0.003104300005361438, -0.010531279258430004, 0.0017633824609220028, -0.021249283105134964, -0.04757748171687126, 0.023527326062321663, -0.015068692155182362, 0.005835150368511677, -0.0019547753036022186, 0.029222432523965836, -0.026925718411803246, -0.03558974713087082, -0.02173476852476597, -0.020577074959874153, 0.0006984674837440252, 0.032154012471437454, 0.009663009084761143, -0.013332151807844639, 0.014480508863925934, -0.025525281205773354, 0.019923537969589233, -0.042946707457304, -0.02479705400764942, 0.09283957630395889, 0.006259949412196875, -0.017374742776155472, 0.03573912754654884, 0.016123687848448753, 0.11181081831455231, -0.009616327472031116, 0.017589477822184563, -0.03684080392122269, -0.010288537479937077, -0.029110398143529892, 0.017225364223122597, -0.0024927761405706406, -0.0542248860001564, -0.009971104562282562, 0.024292897433042526, 0.022369632497429848, -0.004999556578695774, -0.024647675454616547, -0.009046817198395729, 0.00457242364063859, -0.028456861153244972, 0.027859341353178024, -0.014228430576622486, -0.02875562012195587, 0.007795760873705149, 0.03689682111144066, 0.013164099305868149, 0.005111591890454292, 0.01549815945327282, -0.021660078316926956, -0.014788605272769928, -0.05415019392967224, -0.022649720311164856, 0.03237808123230934, 0.03458143398165703, 0.018121642991900444, -0.01186636183410883, 0.020259641110897064, -0.005713779013603926, -0.030193401500582695, -0.011810344643890858, -0.018812524154782295, 0.006498023401945829, 0.023994138464331627, 0.003811520291492343, -0.0018334041815251112, -0.03362913802266121, -0.007973149418830872, -0.04974348843097687, 0.037475667893886566, -0.015703557059168816, 0.028139429166913033, -0.019251327961683273, -0.01818699575960636, 0.007025520782917738, 0.002630485687404871, 0.05579337477684021, 0.0047381422482430935, -0.0012160453479737043, -0.04324546456336975, -0.012043749913573265, -0.023826085031032562, 0.044104401022195816, 0.029931986704468727, -0.005060242488980293, 0.004054262302815914, 0.02339661866426468, -0.014219094067811966, 0.025189176201820374, -0.000971552508417517, 0.002938581630587578, -0.028792966157197952, -0.03030543588101864, -0.053963471204042435, 0.03633664920926094, 0.001569655491039157, -0.018775179982185364, -0.06542837619781494, 0.022388305515050888, 0.014153740368783474, -0.019092611968517303, -0.0843249261379242, 0.0142004219815135, -0.02899836376309395, -0.015946298837661743, 0.041079457849264145, 0.01305206399410963, -0.006796783301979303, -0.06908818334341049, 0.023284584283828735, 0.06695951521396637, 0.006857468746602535, 0.01386431697756052, 0.02173476852476597, 0.06128308176994324, -0.014331129379570484, 0.030902955681085587, -0.03426400199532509, 0.04582227021455765, 0.028848983347415924, -0.017524123191833496, 0.03036145493388176, 0.04783889651298523, 0.0006360313855111599, -0.04332015663385391, 0.07655717432498932, -0.012613261118531227, -0.008594009093940258, -0.03293825685977936, -0.002733184490352869, -0.0046214391477406025, 0.007328948937356472, -0.05512116476893425, -0.00441370764747262, -0.005106923636049032, -0.002880230313166976, 0.003319033421576023, 0.008794738911092281, -0.009840397164225578, -0.07345753908157349, 0.028176773339509964, 0.04283467307686806, 0.026514923200011253, 0.015722228214144707, 0.028475534170866013, 0.002716846065595746, 0.0023153875954449177, 0.02670164778828621, 0.030846938490867615, 0.0383906215429306, -0.02149202488362789, 0.0029945990536361933, -0.0018870875937864184, -0.004495400004088879, 0.028923673555254936, -0.033199671655893326, 0.004114948213100433, -0.007328948937356472, -0.01917663775384426, -0.030865611508488655, -0.02841951698064804, -0.01818699575960636, 0.01338816899806261, 0.004558419343084097, 0.04055662825703621, 0.016282403841614723, 0.011978396214544773, -0.009410930797457695, 0.01436847448348999, -0.029110398143529892, 0.05519585311412811, -0.022350959479808807, -2.3477362276480562e-07, -0.004182635806500912, -0.00039066330646164715, 0.008486642502248287, -0.0016280069248750806, -0.03594452515244484, -0.011110126040875912, 0.0012697287602350116, -0.02341529168188572, -0.024591658264398575, 0.012781313620507717, 0.0160303246229887, -0.014564535580575466, 0.04727872088551521, 0.039772383868694305, 0.02406882867217064, -0.015563513152301311, 0.023041842505335808, 0.006712757050991058, -0.01814965158700943, 0.015087364241480827, 0.015806255862116814, 0.01387365348637104, -0.017468106001615524, 0.003925889264792204, -0.03536567836999893, 0.004817500244826078, 0.03835327550768852, 4.700943100033328e-05, -0.04787624254822731, -0.005877163261175156, 0.007543682120740414, -0.012155785225331783, -0.004168631508946419, 0.018280358985066414, 0.006399992853403091, 0.025207849219441414, -0.03275153040885925, -0.008430625312030315, -0.03437603637576103, 0.036093905568122864, -0.03374117240309715, -0.01337883248925209, -0.007207577582448721, 0.017888236790895462, 0.03222870081663132, -0.016413111239671707, -0.017328063026070595, -0.004798827692866325, -0.006946162786334753, -0.13197709619998932, 0.007212245836853981, 0.007016184739768505, 0.006582049652934074, -0.017570804804563522, -0.02139866352081299, -0.033143654465675354, -0.03706487640738487, 0.009046817198395729, -0.06845331192016602, 0.03824124112725258, 0.013593566603958607, 0.03620593994855881, -0.03241542726755142, -0.03222870081663132, 0.033181000500917435, -0.05504647269845009, -0.003886210033670068, -0.0036341315135359764, 0.05784734711050987, 0.00819255132228136, -0.014779268763959408, 0.006161918863654137, 0.029465174302458763, -0.009943095967173576, -0.01983017474412918, -0.014732587151229382, -0.06132042780518532, -0.024685019627213478, -0.004219980910420418, 0.005302984733134508, -0.09410930424928665, 0.04526209458708763, 0.030118711292743683, 0.016795895993709564, -0.022668391466140747, -0.015124709345400333, 0.0004752730019390583, -0.0031649854499846697, -0.02074512653052807, 0.02182812988758087, 0.01832704059779644, -0.01622638665139675, 0.01187569834291935, -0.001177533296868205, 0.03758770599961281, -0.007571691181510687, -0.019363362342119217, -0.04029521346092224, 0.015787582844495773, -0.00709554273635149, 0.04985552281141281, 0.004455721005797386, 0.014723251573741436, -0.028307480737566948, 0.00840261671692133, -0.06288891285657883, -0.0148446224629879, 0.00806651171296835, 0.0461210273206234, -0.010802030563354492, 0.015050020068883896, -0.03803584352135658, -0.02800872176885605, 0.0082158911973238, 0.017738856375217438, 0.0005053240456618369, 0.03586983680725098, -0.03786779195070267, 0.0013817636063322425, -0.04332015663385391, -0.011716981418430805, -0.028923673555254936, -0.0336478091776371, 0.005704442970454693, -0.057922035455703735, 0.02474103681743145, -0.004892189987003803, -0.008734053000807762, -0.02076379954814911, -0.037139564752578735, -0.03572045639157295, -0.038110535591840744, 0.01864447258412838, 0.020875833928585052, -0.01104477234184742, -0.01418174896389246, 0.015376788564026356, -0.07820034772157669, 0.0006797949899919331, -0.04821234568953514, -0.03529099002480507, -0.03689682111144066, -0.01678656041622162, -0.021958837285637856, -0.005998534616082907, -0.030230747535824776, 0.008911442011594772, -0.0007533179013989866, -0.054299574345350266, -0.03333037719130516, -0.0077864243648946285, -0.004768484737724066, 0.021697422489523888, -0.00395856611430645, 0.06468147784471512, 0.014881967566907406, -0.004364692606031895, 0.029801279306411743, 0.013584230095148087, -0.060573529452085495, -0.01611435040831566, -0.008239232003688812, -0.012249147519469261, -0.06957366317510605, 0.006199263501912355, -0.02701907977461815, 0.02214556187391281, 0.01767350360751152, 0.02940915711224079, 0.03736363351345062, -0.04346953704953194, -8.161916775861755e-05, -0.009210200980305672, 0.001455286517739296, 0.07121684402227402, 0.048399072140455246, -0.025338556617498398, -0.01785089261829853, 0.00942026637494564, 0.027859341353178024, -0.037774428725242615, -0.024199536070227623, 0.006862137001007795, 0.020315660163760185, 0.011194152757525444, -0.025581298395991325, 0.023938121274113655, -0.0009044482721947134, -0.016198378056287766, -0.018971240147948265, -0.04003379866480827, -0.016086341813206673, -0.026608284562826157, -0.028400843963027, 0.018280358985066414, 0.05512116476893425, -0.005083582829684019, -0.005774464923888445, -0.011838353238999844, 0.021660078316926956, -0.011735654436051846, 0.006899481639266014, 0.022855116054415703, -0.017244035378098488, -0.007216914091259241, -0.04328281059861183, -0.042759981006383896, 0.007982485927641392, 0.079171322286129, -0.012482553720474243, -0.01953141577541828, -0.01186636183410883, -0.009849733673036098, -0.055419921875, 0.07226250320672989, 0.019232654944062233, -0.008080516010522842, -0.04481395334005356, 0.057249825447797775, 0.028848983347415924, 0.01622638665139675, -0.038091860711574554, -0.031201716512441635, 0.02802739478647709, -0.03594452515244484, 0.004180301912128925, 0.02905438095331192, 0.033479757606983185, -0.022014854475855827, 0.005601744167506695, -0.01710399240255356, -0.006381320301443338, -0.006530700251460075, -0.007352289278060198, 0.020838487893342972, -0.01682390458881855, -0.005914508365094662, 0.02838217094540596, -0.023508653044700623, -0.019008586183190346, -0.031556494534015656, 0.0587809719145298, 0.015096700750291348, 0.05553195998072624, 0.016562489792704582, 0.03568311035633087, 0.0481003113090992, -0.01765483058989048, 0.025170505046844482, -0.03071623109281063, 0.019326018169522285, -0.022668391466140747, -0.05568133667111397, -0.033834535628557205, 0.0271124430000782, -0.007954477332532406, -0.04421643540263176, -0.03895079717040062, -0.016179705038666725, -0.011240833438932896, -0.02244432270526886, -0.012211802415549755, 0.034749485552310944, 0.018177660182118416, 0.01747744157910347, 0.03398391604423523, -0.021006541326642036, 0.035496387630701065, 0.007819101214408875, 0.031537819653749466, -0.005167609080672264, 0.012622597627341747, -0.03850265592336655, -0.02709376998245716, -0.010344554670155048, -0.016833242028951645, 0.03176188841462135, 0.00644667400047183, 0.027635272592306137, 0.015404797159135342, 0.018756506964564323, 0.015526168048381805, 0.027541909366846085, 0.015703557059168816, -0.015582185238599777, 0.024629002436995506, 0.019382035359740257, -0.02074512653052807, -0.026608284562826157, -0.005461700726300478, -0.028139429166913033, 0.07080604881048203, -0.03301294520497322]', 'distance': 0.6687338352203369}, {'title': \"An Empirical Categorization of Prompting Techniques for Large Language Models: A Practitioner's Guide\", 'authors': ['Oluwole Fagbohun', 'Rachel M. Harrison', 'Anton Dereventsov'], 'summary': 'Due to rapid advancements in the development of Large Language Models (LLMs),\\nprogramming these models with prompts has recently gained significant\\nattention. However, the sheer number of available prompt engineering techniques\\ncreates an overwhelming landscape for practitioners looking to utilize these\\ntools. For the most efficient and effective use of LLMs, it is important to\\ncompile a comprehensive list of prompting techniques and establish a\\nstandardized, interdisciplinary categorization framework. In this survey, we\\nexamine some of the most well-known prompting techniques from both academic and\\npractical viewpoints and classify them into seven distinct categories. We\\npresent an overview of each category, aiming to clarify their unique\\ncontributions and showcase their practical applications in real-world examples\\nin order to equip fellow practitioners with a structured framework for\\nunderstanding and categorizing prompting techniques tailored to their specific\\ndomains. We believe that this approach will help simplify the complex landscape\\nof prompt engineering and enable more effective utilization of LLMs in various\\napplications. By providing practitioners with a systematic approach to prompt\\ncategorization, we aim to assist in navigating the intricacies of effective\\nprompt design for conversational pre-trained LLMs and inspire new possibilities\\nin their respective fields.', 'published': '2024-02-18T23:03:56+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2402.14837v1', 'vector': '[-0.0678812637925148, -0.0034048871602863073, 0.012273594737052917, -0.02221294865012169, -0.004449648782610893, -0.023511840030550957, -0.01789271831512451, -0.034241825342178345, -0.0010488793486729264, -0.007289799861609936, -0.02661788836121559, -0.013581899926066399, 0.006150915753096342, 0.017629174515604973, -0.04638364911079407, 0.026429643854498863, -0.04077393561601639, 0.007012138143181801, 0.0034637139178812504, -0.016885604709386826, 0.0038566759321838617, 0.016829131171107292, 0.01667853631079197, 0.004710839129984379, -0.014833731576800346, 0.01497491542249918, -0.02100817859172821, -0.04484003409743309, -0.022401193156838417, -0.011285306885838509, 0.013167760334908962, 0.00506379920989275, 0.039945658296346664, -0.0031413438264280558, -0.029629813507199287, -0.014937266707420349, 0.003684902098029852, 0.0008482804987579584, -0.03761141374707222, -0.0019518686458468437, -0.01800566539168358, -0.039870359003543854, 0.02100817859172821, -0.05172981321811676, 0.01458901260048151, 0.007148616015911102, -0.04747546836733818, -0.02170468494296074, 0.0040190378203988075, -0.025714311748743057, -0.008282793685793877, -0.04009625315666199, 0.03657606616616249, -0.02646729163825512, -0.025846082717180252, 0.011718271300196648, -0.05368756502866745, -0.008668697439134121, -0.06528347730636597, 0.01718679815530777, -0.006485051475465298, 0.0445764921605587, 0.01604791358113289, -0.021309370175004005, 0.006353279575705528, -0.0241518747061491, -0.03482538461685181, 0.043522316962480545, -0.01570907235145569, -0.07507223635911942, 0.013139523565769196, 0.023342419415712357, -0.0011230009840801358, -0.009892291389405727, -0.05846899747848511, 0.04510357975959778, 0.015991440042853355, -0.023455366492271423, -0.03736669570207596, 0.04148926958441734, -0.0472872257232666, 0.008696934208273888, 0.032547615468502045, -0.0024589544627815485, 0.01715856045484543, 0.0055673555471003056, 0.020443441346287727, 0.019954003393650055, 0.02287180721759796, -0.019539864733815193, -0.0391550287604332, 0.009529919363558292, 0.004270815756171942, -0.03761141374707222, -0.042995233088731766, 0.007176852785050869, -0.025469591841101646, 0.030251022428274155, -0.027709711343050003, 0.012706559151411057, 0.006419165525585413, 0.020386967808008194, 0.0017330334521830082, 0.0019918708130717278, 0.017920954152941704, -0.05523117631673813, 0.015746721997857094, 0.008226320147514343, 0.013826618902385235, -0.020443441346287727, -0.016687948256731033, 0.007972189225256443, 0.0303827952593565, 0.05225690081715584, -0.008687521331012249, -0.03431712090969086, 0.035258349031209946, -0.025413118302822113, 0.03275468572974205, -0.02893330529332161, 0.05003560706973076, 0.025996679440140724, 0.07526048272848129, -0.03411005064845085, 0.0051767462864518166, 0.012057112529873848, 0.004099041689187288, -0.03130519762635231, -0.03885383531451225, -0.00493202731013298, 0.020650511607527733, 0.01561494916677475, -0.03217112645506859, 0.016612648963928223, -0.047098979353904724, -0.05221925303339958, -0.028632113710045815, -0.05598415806889534, -0.0073133306577801704, -0.017036201432347298, 0.03181345760822296, 0.031079303473234177, -0.011708859354257584, -0.04133867099881172, 0.00969463400542736, -0.07891243696212769, 0.01123824529349804, 0.015078450553119183, -0.007115673273801804, -0.01846686564385891, 0.0006682708626613021, 0.0246601365506649, 0.009576980955898762, 0.03661371394991875, 0.03845851868391037, -0.008009838871657848, -0.06987666338682175, 0.019633987918496132, 0.009049894288182259, 0.03113577701151371, -0.0238130334764719, 0.01570907235145569, 0.01570907235145569, -0.02049991488456726, 0.014918441884219646, -0.017487989738583565, 0.0038002023939043283, 0.03648194298148155, -0.04532947391271591, -0.04476473852992058, 0.013073637150228024, 0.012819506227970123, 0.0003979623725172132, 0.0069509586319327354, 0.0014553716173395514, 0.0027954429388046265, 0.022664736956357956, 0.029667463153600693, 0.06038909777998924, -0.01955868862569332, -0.0054920571856200695, 0.002929567825049162, 0.00606149947270751, -0.02637317031621933, -0.01812802441418171, 0.02545076794922352, -0.0022930633276700974, -0.06528347730636597, 0.020989352837204933, -0.02825562283396721, -0.014861968345940113, -0.028895657509565353, 0.0164620541036129, 0.03028867207467556, 0.015887904912233353, -0.06599880754947662, 0.024622488766908646, -0.025846082717180252, 0.0004806138458661735, -0.04310818016529083, -0.014381942339241505, 0.04698603227734566, -0.05726422742009163, -0.01923867128789425, -0.05300988256931305, -0.00865457858890295, -0.05978671461343765, -0.04574361443519592, -0.02057521417737007, -0.002087170025333762, 0.015200809575617313, 0.004837904591113329, 0.007586286403238773, -0.03281116113066673, -0.0009794639190658927, 0.03941857069730759, -0.014965503476560116, 0.04698603227734566, -0.0013259529368951917, -0.028632113710045815, -0.04544242098927498, 0.06942487508058548, 0.00041884585516527295, 0.030646339058876038, -0.01381720695644617, 0.004129631910473108, 0.0442376509308815, -0.00501673761755228, 0.006946252193301916, 0.02289063110947609, 0.007609817199409008, 0.020876405760645866, -0.04408705607056618, -0.019765758886933327, -0.02356831356883049, -0.04340936988592148, 0.001233006827533245, 0.015840843319892883, 0.01632086932659149, 0.00744510255753994, -0.00043355251546017826, -0.02646729163825512, 0.001776565215550363, 0.016019677743315697, -0.03352649137377739, -0.06264804303646088, 0.0005053210188634694, 0.022005878388881683, -0.0040284497663378716, -0.004828492645174265, 0.0016165566630661488, -0.0005294399452395737, -0.046421296894550323, -0.019709285348653793, 0.006414459552615881, 0.010278194211423397, 0.020368143916130066, -0.043522316962480545, -0.014513714239001274, -0.015577300451695919, 0.04702368006110191, 0.03471243754029274, 0.011577087454497814, 0.006734476424753666, 0.022100001573562622, 0.015925554558634758, -0.03145579248666763, -0.038665588945150375, 0.020462267100811005, 0.039343271404504776, -0.024791909381747246, -0.03488185629248619, 0.041639864444732666, -0.025281347334384918, -0.005313224159181118, 0.01689501851797104, -0.06464344263076782, -0.02765323780477047, 0.07574991881847382, 0.01672559604048729, -0.03889148309826851, -0.021930579096078873, 0.03587955981492996, -0.008348680101335049, 0.025055453181266785, -0.014099574647843838, -0.043258775025606155, -0.013986627571284771, -0.02023637294769287, -0.029065078124403954, 0.032208774238824844, -0.006894485093653202, 0.06927427649497986, -0.0020377556793391705, -0.029253322631120682, 0.033827684819698334, 0.005609710700809956, -0.1581260710954666, -0.02723909728229046, -0.02219412289559841, -0.005957964342087507, 0.013638373464345932, 0.023775383830070496, -0.026674361899495125, -0.00020530505571514368, 0.0018283326644450426, 0.031022828072309494, 0.028650937601923943, -0.050412096083164215, 0.015502002090215683, 0.036801960319280624, -0.038232624530792236, 0.02866976335644722, -0.031022828072309494, 0.037912607192993164, 0.0030001597478985786, -0.015163160860538483, -0.00974169559776783, -0.04261874035000801, 0.021723510697484016, -0.005520293954759836, 0.022005878388881683, -0.024961329996585846, 0.050562694668769836, 0.023417718708515167, 0.001693031401373446, 0.01361954864114523, -0.05282163619995117, 0.04179046303033829, 0.0006988607347011566, 0.011934753507375717, 0.0178174190223217, 0.018090374767780304, -0.010400554165244102, -0.008160434663295746, -0.023455366492271423, 0.023869507014751434, 0.01883394457399845, 0.0638151615858078, 0.02304122783243656, -0.010569974780082703, -0.0204246174544096, -0.03311235085129738, 0.007934540510177612, -0.022928280755877495, 0.011313543654978275, -0.035013630986213684, -0.03232172131538391, -0.01331835612654686, -0.062233902513980865, 0.021083476021885872, -0.0506756417453289, -0.018946891650557518, -0.02458483912050724, 0.0037602002266794443, 0.005332048516720533, -0.03587955981492996, 0.005703833419829607, -0.02356831356883049, 0.004680249374359846, -0.041225723922252655, -0.03205817937850952, -0.02057521417737007, 0.06475639343261719, 0.03250996768474579, 0.015219634398818016, -0.06031380221247673, 0.015539651736617088, -0.046835437417030334, -0.0034260647371411324, -0.00808513630181551, 0.04484003409743309, 0.044877685606479645, 0.003703726688399911, 0.008296912536025047, -0.01655617542564869, -0.08862590044736862, -0.05617240443825722, 0.006499169860035181, 0.008640460669994354, 0.015596125274896622, -0.007412159349769354, -0.02085758186876774, 0.05146627128124237, -0.031342845410108566, 0.040962181985378265, 0.24155639111995697, 0.028142675757408142, 0.023681260645389557, -0.02731439657509327, 0.034750085324048996, 0.018796294927597046, -0.061970360577106476, 0.04435059800744057, 0.041225723922252655, -0.03616192564368248, 0.029460392892360687, 0.04638364911079407, 0.009266375564038754, 0.03601133078336716, -0.0017200916772708297, 0.018843356519937515, -0.03650076687335968, 0.04243049398064613, 0.07401806116104126, 0.0009653455344960093, 0.01740328036248684, 0.027502641081809998, 0.01711149886250496, -0.04303288087248802, -0.027370870113372803, -0.040284499526023865, 0.034166526049375534, -0.017318569123744965, 0.0019495156593620777, 0.013657198287546635, 0.014429003931581974, 0.06057734414935112, 0.03599250689148903, 0.008626341819763184, 0.0073886290192604065, 0.0036919612903147936, 0.0022307070903480053, -0.0025389587972313166, 0.0035672488156706095, 0.049659114331007004, -0.012668910436332226, -0.01165238581597805, -0.0380067303776741, -0.0035178344696760178, 0.024377768859267235, -0.021817632019519806, 0.025921380147337914, -0.014824318699538708, 0.04107512906193733, -0.008536925539374352, -0.05590885877609253, 0.013365417718887329, -0.021761158481240273, -0.011162947863340378, -0.0031601684167981148, 0.003600191790610552, -0.020123424008488655, 0.0017871540039777756, 0.01224535796791315, 0.007976895198225975, 0.02001047693192959, -0.00044737677671946585, 0.01536081824451685, 0.010268782265484333, 0.04702368006110191, 0.005637947469949722, 0.010475852526724339, 0.003804908599704504, 0.06170681491494179, 0.01743151620030403, 0.053913459181785583, 0.04510357975959778, 0.007991014048457146, -0.04431295022368431, 0.024020103737711906, -0.056887734681367874, 0.05651124566793442, 0.04883083701133728, 0.011162947863340378, 0.012565375305712223, -0.029968654736876488, 0.028010904788970947, 0.019596338272094727, 0.005506175570189953, 0.0029954537749290466, -0.015294932760298252, 0.007595698814839125, 0.02170468494296074, -0.037385519593954086, 0.019332794472575188, -0.024095401167869568, -0.02230706997215748, 0.031004004180431366, 0.012706559151411057, -0.012800682336091995, -0.00213893735781312, 0.04310818016529083, -0.04672249034047127, -0.030326321721076965, -0.0047790780663490295, 0.03062751330435276, 0.015389055013656616, 0.019765758886933327, 0.03145579248666763, 0.03753611817955971, 0.0029130964539945126, -0.030232198536396027, 0.021121125668287277, 0.011162947863340378, 0.06340102106332779, -0.026730835437774658, 0.010824105702340603, 0.007718058302998543, -0.03877853602170944, 0.03388415649533272, 0.04566831514239311, -0.019972829148173332, 0.007002726197242737, -0.010240545496344566, -0.02270238660275936, -0.024189524352550507, 0.0709308385848999, -0.03503245487809181, 0.02560136467218399, -0.00977934431284666, -0.01812802441418171, 0.02202470228075981, 0.01955868862569332, -0.03267938643693924, 0.0035460712388157845, 0.030985180288553238, 0.007478045299649239, -0.005430877674371004, 0.02782265841960907, 0.01263126078993082, 0.01370425894856453, 0.04107512906193733, 0.0066450596787035465, -0.013845443725585938, -0.008113373070955276, -0.04672249034047127, 0.010889992117881775, -0.015031388960778713, -0.00905460026115179, 0.02678730897605419, 0.07386746257543564, -0.03554071485996246, 0.0047390758991241455, 0.016405580565333366, 0.04996030777692795, -0.009261669591069221, 0.017026789486408234, 0.029140375554561615, -0.035258349031209946, 0.005642653442919254, -0.015887904912233353, -0.02509310096502304, 0.02850034087896347, 0.013967802748084068, -0.02023637294769287, 0.00561912264674902, 0.051955707371234894, 0.027540290728211403, 0.01502197701483965, 0.007595698814839125, -0.004532006103545427, 0.013610136695206165, 0.07123202830553055, 0.009816993959248066, -0.026090800762176514, 0.003221348160877824, -0.009351086802780628, -0.026561414822936058, 0.08290323615074158, 0.009638160467147827, -0.004687308333814144, -0.05131567642092705, -0.004115513525903225, -0.019841056317090988, -0.004188458435237408, 0.02279650792479515, -0.005228513851761818, -0.08636695146560669, 0.056963033974170685, 0.0007753354148007929, -0.040886882692575455, 0.0004806138458661735, -0.012170059606432915, 0.021723510697484016, 0.004750841297209263, -0.041037481278181076, 0.0877976194024086, -0.0415269173681736, 0.0102123087272048, 0.011878279969096184, -0.002089523011818528, 0.08087018877267838, -0.03422299772500992, 0.0308910571038723, -0.04340936988592148, -0.03796908259391785, -0.03343236818909645, -0.002927214838564396, -0.0030778110958635807, -0.048454344272613525, 0.026071976870298386, -0.004226107615977526, -0.0006447402411140501, -0.03783730790019035, -0.039267975836992264, 0.00422375462949276, -0.013911329209804535, -0.04047274589538574, 0.0066780028864741325, -0.015163160860538483, 0.009586392901837826, -0.008565162308514118, 0.07217325270175934, -0.011275894939899445, 0.030307495966553688, 0.019210435450077057, -0.02296592853963375, -0.017346806824207306, -0.02765323780477047, 0.009544038213789463, 0.03002512827515602, 0.010955877602100372, -0.004572008270770311, 0.01621733419597149, 0.027013203129172325, -0.004917908925563097, -0.027690887451171875, -0.022852981463074684, 0.006221507675945759, 0.03388415649533272, 0.008819293230772018, -0.004468473140150309, 0.032133474946022034, -0.01846686564385891, -0.0036896083038300276, -0.0381385013461113, -0.012988926842808723, 0.019935179501771927, 0.0857645720243454, 0.003131931647658348, -0.008282793685793877, -0.01905042678117752, -0.008951065130531788, -0.0006447402411140501, 0.02526252157986164, 0.03294293209910393, -0.03386533260345459, -0.012141822837293148, -0.005873254034668207, 0.007195677608251572, -0.05368756502866745, -0.029159199446439743, 0.001613027066923678, -0.030081601813435555, 0.016377342864871025, 0.01861746236681938, 0.021045826375484467, 0.00748745771124959, -0.014946678653359413, -0.022777684032917023, -0.05854429677128792, -0.004823786206543446, -0.021817632019519806, -0.010475852526724339, -0.05492998659610748, 0.0034448893275111914, -0.0005673831910826266, -0.0097887571901083, -0.08696933835744858, 0.04374821111559868, -0.018843356519937515, -0.01745034195482731, -0.004816727247089148, 0.05172981321811676, 0.0025601363740861416, -0.05044974759221077, 0.041225723922252655, 0.0200857762247324, 0.017497403547167778, 0.021987054497003555, 0.013045400381088257, 0.008056899532675743, 0.005280281417071819, 0.02799207903444767, -0.006663884501904249, 0.02823679894208908, 0.04461413994431496, -0.0246601365506649, -0.028782710433006287, 0.0204246174544096, 0.024791909381747246, -0.03616192564368248, 0.07311448454856873, -0.0032072297763079405, -0.024566015228629112, -0.0325852669775486, 0.03651959076523781, 0.011548850685358047, 0.012612436898052692, -0.03584190830588341, 0.009628748521208763, -0.006819186732172966, -0.022231772541999817, 0.04416235163807869, 0.01059821154922247, -0.0162926334887743, -0.022438842803239822, 0.009285200387239456, 0.01902218908071518, 0.02509310096502304, 0.020462267100811005, 0.03546541929244995, -0.03104165382683277, 0.057452473789453506, 0.0050873300060629845, 0.00359313259832561, 0.00861692987382412, -0.05172981321811676, -0.004788490477949381, -0.006423871498554945, 0.01982223242521286, -0.003802555613219738, -0.009581686928868294, -0.012348893098533154, 0.023436542600393295, -0.036293696612119675, -0.055758263915777206, -0.02211882546544075, 0.010240545496344566, 0.003430770942941308, -0.0039860946126282215, 0.02722027339041233, 0.015210222452878952, 0.0017471518367528915, -0.030175724998116493, 0.05526882782578468, -0.05673713982105255, -0.00459318608045578, -0.023361245170235634, 0.05666184052824974, -0.03674548864364624, -0.026222573593258858, 0.026166100054979324, 0.0031225192360579967, 0.01242419146001339, -0.006508581805974245, -0.03710315376520157, -0.023003578186035156, -0.06287393718957901, 0.03930562362074852, -0.038590289652347565, -0.008442802354693413, 0.03505127876996994, 0.01016524713486433, 0.019426917657256126, -0.009920528158545494, 0.009016950614750385, -0.021459966897964478, -0.0004447295796126127, -0.019521038979291916, -0.006555643398314714, 0.004586126655340195, -0.084409199655056, 0.0019601043313741684, -0.06535877287387848, 0.014909029006958008, 0.05944787338376045, -0.03471243754029274, -0.007506282068789005, -0.034994807094335556, 0.01841980405151844, 0.010306430980563164, 0.001609497470781207, 0.043258775025606155, -0.0015965555794537067, -0.008649872615933418, -0.013572487980127335, 0.0054873512126505375, -0.02144114300608635, 0.030514566227793694, -0.011285306885838509, -0.05809250473976135, 0.023248298093676567, 0.021648211404681206, 0.03998330608010292, 0.01501256413757801, 0.02543194219470024, -0.02697555534541607, -0.013026576489210129, -0.12251005321741104, 0.015087862499058247, -0.002550724195316434, 0.018306856974959373, -0.03759258985519409, -0.035616014152765274, -0.02339889295399189, -0.013864267617464066, -0.018815120682120323, -0.06166916713118553, 0.03183228522539139, 0.03601133078336716, 0.0197469349950552, -0.015869081020355225, -0.008607516996562481, 0.01544552855193615, -0.03776201233267784, -0.01600085198879242, 0.0030589865054935217, 0.020688161253929138, 0.028123851865530014, -0.0054449960589408875, 0.017459753900766373, 0.014880792237818241, -0.00999582652002573, -0.025375468656420708, -0.036707837134599686, -0.06806951016187668, -0.02102700248360634, -0.02936626970767975, -0.017855068668723106, -0.0850868821144104, 0.024886030703783035, 0.03452419117093086, -0.02144114300608635, 0.010946465656161308, 0.00899341981858015, -0.010889992117881775, -0.02424599789083004, -0.01585966907441616, -0.0012188884429633617, -0.003889618907123804, -0.02219412289559841, 0.03377120941877365, -0.06712827831506729, 0.0803431048989296, 0.007379216607660055, -0.0100428881123662, -0.046233050525188446, 0.0103817293420434, 0.0003576661110855639, 0.011445315554738045, -0.06456814706325531, -0.005877960007637739, 0.009882879443466663, 0.03260409086942673, -0.049395572394132614, -0.0008706346270628273, -0.017610350623726845, 0.07307683676481247, -0.04476473852992058, 0.02840621955692768, -0.034919507801532745, 0.0066780028864741325, -0.006339161191135645, -0.012603024020791054, -0.04916967824101448, -0.016094975173473358, -0.0359736792743206, 0.03744199499487877, 0.017440928146243095, -0.019859882071614265, -0.027860308066010475, -0.02322947233915329, 0.008631047792732716, -0.019153961911797523, 0.008221614174544811, 0.005463820416480303, 0.022984754294157028, -0.006226214114576578, -0.036030154675245285, 0.005590885877609253, -0.05044974759221077, 0.04047274589538574, 0.015934966504573822, -0.052445147186517715, -0.008423978462815285, 0.020725809037685394, -0.08260204643011093, 0.013158348388969898, -0.027803834527730942, -0.0305522158741951, -0.04013390094041824, -0.016396166756749153, 0.0027648531831800938, 0.020782284438610077, -0.026655538007616997, 0.014024276286363602, 0.010372317396104336, -0.021798808127641678, -0.012791269458830357, 0.05093918368220329, 0.008212202228605747, 0.031154600903391838, 0.02806737832725048, 0.060539696365594864, -0.018193909898400307, -0.000245601317146793, 0.041301023215055466, 0.003186051966622472, -0.020217547193169594, -0.00413433788344264, 0.040698640048503876, 0.0070780240930616856, -0.038326747715473175, 0.012772444635629654, 0.02731439657509327, 0.05191805958747864, 0.017826832830905914, 0.018570400774478912, 0.02789795584976673, -0.03480656072497368, -0.0014094868674874306, -0.015586712397634983, -0.01203828863799572, 0.05139097198843956, 0.01905042678117752, -0.0010900580091401935, 0.013723083771765232, -0.007463926915079355, -0.0017824479145929217, 0.003953151870518923, -0.029723936691880226, 0.00752981286495924, 0.0026872020680457354, 0.007840417325496674, -0.017751533538103104, 0.02706967666745186, -0.01310187391936779, 0.0012506548082455993, -0.05846899747848511, -0.018306856974959373, -0.016396166756749153, -0.02748381718993187, -0.029253322631120682, 0.010871167294681072, -0.00952521339058876, -0.016659710556268692, 0.0039390334859490395, -0.048793189227581024, 0.02772853523492813, 0.004273168742656708, 0.014221933670341969, 0.016038501635193825, 0.03923032432794571, 0.04295758157968521, -0.048793189227581024, 0.013986627571284771, -0.00021662918152287602, 0.05861959233880043, 0.015652598813176155, -0.005962670780718327, -0.015840843319892883, -0.017977427691221237, -0.018193909898400307, 0.03446771949529648, -0.00657446775585413, -0.0006165034137666225, -0.04465179145336151, 0.0305522158741951, 0.028312096372246742, 0.02953569032251835, -0.02221294865012169, 0.024434242397546768, -0.003889618907123804, -0.006941546220332384, -0.000669447414111346, 0.07202266156673431, 0.026429643854498863, -0.008626341819763184, 0.025413118302822113, 0.02723909728229046, 0.034580666571855545, 0.04035979509353638, 0.028274446725845337, -0.02763441391289234, 0.01502197701483965, 0.043183475732803345, 0.05492998659610748, -0.0212717205286026, 0.0006323866546154022, 0.013760733418166637, 0.0038731475360691547, 0.005614416673779488, 0.042166952043771744, -0.01808096282184124, 0.03358296677470207, -0.0036496061366051435, -0.0006017967825755477, 0.028123851865530014, -0.027540290728211403, 0.003195464378222823, 0.012499488890171051, -0.008245144970715046, 0.010155835188925266, -0.0012741854880005121, -0.026316696777939796, -0.05233220010995865, -0.0462706983089447, -0.05910903215408325, 0.0010500559583306313, 0.0015306697459891438, -0.011624149046838284, 0.019210435450077057, 0.02151644043624401, -0.014429003931581974, 0.030100427567958832, 0.014108986593782902, 0.010758220218122005, 0.0033766503911465406, 0.03386533260345459, 0.009769932366907597, 0.018673935905098915, -0.06355161964893341, -0.06701533496379852, -0.01827862113714218, 0.0004209047765471041, 0.0022754152305424213, -0.01561494916677475, 0.011812393553555012, 0.0019930473063141108, -0.004790843464434147, 0.020537564530968666, 0.056021809577941895, 0.014570187777280807, 0.001587143400683999, -0.009464033879339695, -0.013948977924883366, -0.012527726590633392, -0.05078858882188797, 0.013271295465528965, -0.014777258038520813, 0.07544872164726257, 0.0022624735720455647]', 'distance': 0.6689060926437378}, {'title': 'Prompting Strategies for Enabling Large Language Models to Infer Causation from Correlation', 'authors': ['Eleni Sgouritsa', 'Virginia Aglietti', 'Yee Whye Teh', 'Arnaud Doucet', 'Arthur Gretton', 'Silvia Chiappa'], 'summary': \"The reasoning abilities of Large Language Models (LLMs) are attracting\\nincreasing attention. In this work, we focus on causal reasoning and address\\nthe task of establishing causal relationships based on correlation information,\\na highly challenging problem on which several LLMs have shown poor performance.\\nWe introduce a prompting strategy for this problem that breaks the original\\ntask into fixed subquestions, with each subquestion corresponding to one step\\nof a formal causal discovery algorithm, the PC algorithm. The proposed\\nprompting strategy, PC-SubQ, guides the LLM to follow these algorithmic steps,\\nby sequentially prompting it with one subquestion at a time, augmenting the\\nnext subquestion's prompt with the answer to the previous one(s). We evaluate\\nour approach on an existing causal benchmark, Corr2Cause: our experiments\\nindicate a performance improvement across five LLMs when comparing PC-SubQ to\\nbaseline prompting strategies. Results are robust to causal query\\nperturbations, when modifying the variable names or paraphrasing the\\nexpressions.\", 'published': '2024-12-18T15:32:27+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2412.13952v1', 'vector': '[-0.04673827067017555, -0.007954174652695656, -0.012062679044902325, -0.024309802800416946, -0.037350039929151535, -0.02429135888814926, -0.0019366686465218663, -0.018223129212856293, -0.04079915210604668, -0.00998767651617527, -0.030968254432082176, -0.005639394745230675, -0.010375010780990124, 0.006151228677481413, -0.03639092668890953, 0.011241900734603405, -0.017024239525198936, -0.008074063807725906, -0.02436513639986515, -0.03384558856487274, 0.00832306407392025, -0.004145393148064613, 0.057657390832901, 0.02871803008019924, -0.016074350103735924, 0.0045534768141806126, -0.034952256828546524, -0.03297870233654976, -0.006681506987661123, 0.012330124154686928, 0.02314780093729496, -0.01586223766207695, 0.05064849555492401, -0.015474905259907246, -0.01805713027715683, -0.016166571527719498, -0.007940341718494892, -0.020584020763635635, -0.04138937592506409, 0.01359356939792633, -0.03631714731454849, 8.249574602814391e-05, -0.0004507365811150521, -0.01601901650428772, 0.0042537543922662735, -0.00017796024621929973, 0.009415898472070694, -0.029621807858347893, -0.0031217255163937807, -0.038364484906196594, -0.019219130277633667, -0.017494574189186096, 0.025822248309850693, -0.061088062822818756, 0.025988249108195305, -0.02217024564743042, -0.040282707661390305, -0.004034726414829493, -0.03873337432742119, -0.01668301783502102, -0.022225579246878624, -0.015474905259907246, 0.0026721416506916285, -0.03266514465212822, -0.0175499077886343, 0.00938362069427967, -0.018112463876605034, 0.06156761944293976, -0.021063577383756638, -0.09664899110794067, 0.03391936793923378, 0.051718275994062424, -0.0067414515651762486, -0.01286501344293356, -0.06359650939702988, 0.026135804131627083, -0.0033937813714146614, -0.006063617300242186, -0.01687668450176716, 0.00967412069439888, -0.03224092349410057, -0.005561005789786577, 0.014386681839823723, -0.006427895277738571, 0.029972253367304802, 0.03426981344819069, -0.0008432578179053962, 0.004703338257968426, -0.008193952962756157, 0.005371950101107359, -0.013501347042620182, -0.02287113480269909, 0.010780788958072662, -0.04419293254613876, -0.039028484374284744, -0.01214567944407463, -0.04116804152727127, 0.02244691178202629, 0.007645230274647474, 0.01525357086211443, -0.01108512282371521, 0.04194270819425583, 0.01316012442111969, -0.01476479321718216, 0.0252689141780138, -0.039655596017837524, 0.003103280905634165, 0.030341142788529396, -0.009545009583234787, -0.007566841319203377, -0.004445115569978952, 0.0350998155772686, 0.02702113799750805, 0.054816946387290955, -0.018001796677708626, -0.02161691151559353, 0.015557904727756977, -0.02004913240671158, 0.056071169674396515, -0.016240350902080536, 0.025471804663538933, 0.031466253101825714, 0.07510585337877274, -0.01404545921832323, -0.011177345179021358, 0.01202579028904438, 0.010467233136296272, 0.012994124554097652, -0.04223782196640968, -0.01457112655043602, 0.012053457088768482, 0.0076360078528523445, -0.028847141191363335, 0.012496124021708965, -0.06300628930330276, -0.04961560666561127, -0.010974455624818802, -0.010079898871481419, -0.016314128413796425, -0.03338447958230972, 0.01750379614531994, 0.047180935740470886, -0.029603363946080208, -0.031042031943798065, 0.0036981150042265654, -0.0585058368742466, 0.028607362881302834, 0.017024239525198936, -0.027113361284136772, -0.03144780918955803, -0.014737126417458057, 0.026615360751748085, 0.015382682904601097, 0.08499208837747574, 0.007525341119617224, -0.04983694106340408, -0.04707027226686478, 0.03725781664252281, 0.028459807857871056, 0.014525014907121658, 0.0180940181016922, 0.007871175184845924, 0.004938505124300718, 0.01766057312488556, 0.05072227492928505, 0.0014409737195819616, 0.013519791886210442, 0.0406515970826149, -0.021377133205533028, -0.016858238726854324, 0.012717457488179207, 0.006183506455272436, 0.005404227878898382, -0.006676895543932915, 0.007041173987090588, -0.008784175850450993, 0.0648876205086708, 0.038253817707300186, 0.02314780093729496, -0.004274504259228706, 0.006275728810578585, -0.03011980839073658, -0.004071615170687437, -0.031281810253858566, -0.027353139594197273, -0.019569575786590576, -0.016157349571585655, -0.062452953308820724, 0.03596670553088188, 0.00454425485804677, -0.008249286562204361, -0.022981802001595497, 0.013750347308814526, 0.0017545295413583517, 0.021875133737921715, -0.0723760724067688, -0.011463234201073647, -0.045852936804294586, -0.01849057525396347, -0.055407166481018066, 0.018794909119606018, 0.026855139061808586, -0.016553906723856926, -0.03430670127272606, -0.04928360506892204, 0.0109467888250947, -0.05433738976716995, -0.025600915774703026, 0.0041384766809642315, 0.008604342117905617, 0.02938202954828739, 0.018979351967573166, -0.012302457354962826, 0.01488468237221241, -0.008747287094593048, 0.009609565138816833, -0.017808130010962486, 0.03071003220975399, -0.0011862094979733229, -0.038917817175388336, -0.04762360453605652, 0.12143834680318832, -0.0020346548408269882, 0.012284012511372566, 0.029308252036571503, -0.011822900734841824, 0.05703027918934822, 0.01719024032354355, -0.0042952545918524265, 0.006040561944246292, 0.027168694883584976, -0.0021303354296833277, -0.030580921098589897, -0.021709132939577103, -0.04747604951262474, -0.03152158856391907, -0.005095283035188913, 0.00861356407403946, 0.010605566203594208, -0.015106015838682652, -0.02259446680545807, -0.036722928285598755, 0.01410079188644886, 0.012560679577291012, -0.07746674865484238, -0.02080535516142845, 0.028109362348914146, 0.03858581930398941, -0.015419571660459042, 0.019422020763158798, -0.02397780306637287, 0.005671672523021698, -0.0679125115275383, -0.05252983048558235, -0.010098343715071678, 0.00452350452542305, 0.055407166481018066, -0.013584347441792488, -0.013814902864396572, 0.02063935436308384, 0.025545582175254822, 0.03106047585606575, 0.03150314465165138, 0.013851791620254517, 0.008793397806584835, 0.016166571527719498, -0.020786909386515617, -0.027131805196404457, -0.002801252994686365, 0.0607929527759552, -0.029400475323200226, -0.016480127349495888, 0.04773427173495293, -0.01640634983778, -0.01719946227967739, 0.022262467071413994, -0.0763600766658783, -0.03795870393514633, 0.08845964819192886, 0.039544928818941116, -0.005524116568267345, 0.008272341452538967, 0.04072537645697594, -0.015410348773002625, 0.008738064207136631, -0.022944912314414978, -0.039544928818941116, -0.04190582036972046, -0.015640905126929283, -0.05706717073917389, 0.017061129212379456, -0.001563168247230351, 0.06938806921243668, -0.0024876969400793314, -0.02373802475631237, 0.01108512282371521, 0.0037396149709820747, -0.14180102944374084, -0.016747573390603065, -0.037350039929151535, 0.01232090126723051, 0.011703011579811573, 0.021100467070937157, -0.02862580679357052, 0.02095291018486023, 0.013003346510231495, -0.005888395011425018, 0.00537656107917428, -0.05190271884202957, 0.002715947339311242, -0.02338757924735546, -0.07894230633974075, -0.001491695991717279, -0.020104466006159782, 0.011232677847146988, 0.01782657392323017, -0.014506570063531399, -0.024863136932253838, -0.03991381824016571, 0.0018409880576655269, -0.010135232470929623, -0.010522566735744476, -0.005146005190908909, -0.005459561012685299, 0.05894850566983223, -0.008193952962756157, 0.00788961909711361, -0.02812780626118183, 0.009969232603907585, 0.0005588096100836992, 0.024531137198209763, 0.015557904727756977, 0.0411311537027359, 0.0006980076432228088, -0.0018421408021822572, -0.031208032742142677, 0.004060087725520134, 0.014635682106018066, 0.07687652111053467, 0.029898475855588913, 0.01770668476819992, 0.002081918762996793, -0.023442912846803665, 0.007253285031765699, -0.01805713027715683, 0.008604342117905617, -0.03910226374864578, 0.0007297091069631279, 0.023000245913863182, -0.030341142788529396, 0.023553580045700073, -0.049689386039972305, 0.0012207928812131286, 0.022705134004354477, 0.02039957605302334, -0.000613854790572077, -0.017752796411514282, -0.013814902864396572, -0.02111891098320484, 0.003043336560949683, -0.04009826481342316, -0.0025291971396654844, -0.010292010381817818, 0.08285252749919891, 0.02001224271953106, 0.012173346243798733, -0.0025868359953165054, 0.04183204472064972, -0.018398351967334747, 0.006967396009713411, -0.02600669302046299, 0.04039337486028671, 0.05880095064640045, 0.017291683703660965, 0.021395577117800713, 0.0028957808390259743, -0.07012584805488586, -0.010992900468409061, -0.007041173987090588, 0.007299396209418774, 0.02080535516142845, -0.012440790422260761, -0.03545025736093521, 0.04242226481437683, -0.0008202022290788591, 0.054263610392808914, 0.22457978129386902, 0.03011980839073658, -0.0023585858289152384, -0.020104466006159782, 0.048435162752866745, -0.00226751621812582, -0.00463186576962471, 0.01973557658493519, 0.009711010381579399, -0.02060246653854847, 0.059206727892160416, 0.04673827067017555, -0.006437117699533701, 0.04301248863339424, -0.011961234733462334, 0.0411311537027359, -0.05389472097158432, 0.019053131341934204, 0.09082053601741791, -0.017596017569303513, -0.036169592291116714, 0.0045281159691512585, 0.0019689465407282114, -0.006944340653717518, -0.028293807059526443, -0.013538235798478127, 0.0027574473060667515, -0.01722712814807892, 0.04068848490715027, 0.014146903529763222, -0.023203134536743164, 0.06293250620365143, 0.04057781770825386, 0.0038018650375306606, 0.024844693019986153, -0.0018582796910777688, -0.008549008518457413, 0.004585754591971636, -0.0016461684135720134, 0.006695340387523174, -0.0016553906025364995, -0.0014075430808588862, -0.04102048650383949, -0.009365175850689411, 0.005828450433909893, -0.035431813448667526, 0.01934824138879776, 0.00686595169827342, 0.011546233668923378, -0.04164759814739227, -0.04548404738306999, -0.022022688761353493, -0.003984004259109497, 0.0175499077886343, 0.024457357823848724, -0.010799232870340347, 0.021524688228964806, 0.028054028749465942, -0.022391578182578087, 0.005353505723178387, 0.04234848916530609, 0.005039949435740709, -0.0007521882653236389, 0.03502603620290756, 0.0010536399204283953, 0.02471558190882206, -0.03583759069442749, -0.01821390725672245, 0.035468704998493195, 0.038475152105093, 0.046664491295814514, 0.05016893893480301, 0.013086346909403801, 0.01341834757477045, 0.03694425895810127, -0.05323072150349617, 0.02091602236032486, 0.02436513639986515, -0.032019589096307755, 0.005086060613393784, -0.009346731938421726, 0.009000898338854313, 0.004947727080434561, -0.04227470979094505, 0.006266506388783455, 0.018066352233290672, -0.02095291018486023, 0.05573916807770729, -0.027371583506464958, 0.013206236064434052, -0.028017139062285423, -0.013971680775284767, -0.017872685566544533, -0.02890247479081154, -0.01462645921856165, 0.010153677314519882, 0.038917817175388336, -0.05957561731338501, -0.02060246653854847, -0.014174570329487324, 0.029861586168408394, -0.004043948836624622, 0.05514894425868988, -0.007825063541531563, 0.03004603087902069, -0.001178716542199254, -0.03297870233654976, -0.005501061212271452, -0.019680242985486984, 0.04769738391041756, -0.02467869222164154, 0.01746690645813942, -0.012496124021708965, -0.024936914443969727, -0.004286032170057297, 0.05334138870239258, -0.03587448224425316, 0.007197951897978783, 0.03314470127224922, 0.021248022094368935, -0.010292010381817818, 0.07296629995107651, -0.03611426055431366, 0.029824696481227875, 0.0069720069877803326, -0.03698115050792694, 0.019145352765917778, 0.012772791087627411, -0.01657235063612461, 0.012938790954649448, 0.035782258957624435, 0.01739312894642353, 0.011214233934879303, 0.03657536953687668, 0.026486249640583992, 0.0013879458419978619, -0.012818901799619198, -0.006921284832060337, 0.0180940181016922, 0.02851513959467411, -0.047217827290296555, 0.006377173122018576, -0.015124459750950336, 0.025988249108195305, 0.029437363147735596, 0.031982701271772385, -0.08056541532278061, -0.027371583506464958, -0.023369135335087776, 0.04961560666561127, 0.008917897939682007, 0.047217827290296555, 0.03213025629520416, -0.03580070286989212, -0.004804782569408417, -0.01010756567120552, -0.01715335063636303, -0.025988249108195305, 0.008060230873525143, -0.030230475589632988, 0.004461254458874464, 0.049763161689043045, 0.010098343715071678, -0.007603730075061321, 0.022373134270310402, 0.018315352499485016, 0.011776790022850037, 0.08550853282213211, 0.0075991190969944, -0.014165347442030907, 0.017651351168751717, 0.011020567268133163, -0.03936048597097397, 0.05308316648006439, -0.025951359421014786, 0.04142626374959946, -0.018343018367886543, 0.012505345977842808, -0.04489382356405258, -0.023867135867476463, 0.0017948768800124526, -0.012957235798239708, -0.06547784805297852, 0.06916673481464386, -0.005298172123730183, 0.010891455225646496, 0.011666122823953629, -0.0109467888250947, 0.02604358270764351, -0.010974455624818802, -0.04430359974503517, 0.11243744939565659, -0.02816469594836235, -0.004366726614534855, 0.019126908853650093, 0.030101364478468895, 0.06016584113240242, 0.016618462279438972, 0.021358689293265343, -0.05389472097158432, 0.01624957285821438, -7.565112173324451e-05, -0.0297324750572443, 0.012422346509993076, -7.08959050825797e-05, 0.0049154493026435375, 0.025711581110954285, -0.04371337965130806, -0.018582796677947044, -0.049763161689043045, -0.015594793483614922, -0.013796458952128887, -0.066584512591362, 0.0027712807059288025, -0.0012484595645219088, -0.020731577649712563, 0.02373802475631237, 0.05599739030003548, -0.020768465474247932, 0.04142626374959946, 0.018223129212856293, -0.010089121758937836, 0.014183792285621166, -0.021137354895472527, -0.05839516967535019, -0.025674693286418915, 0.01298490259796381, -0.005459561012685299, 0.0178542397916317, 0.010255121625959873, -0.002872725250199437, -0.02287113480269909, -0.0008271189290098846, 0.026209581643342972, 0.016590794548392296, 0.061198730021715164, 0.005307394545525312, 0.022465355694293976, 0.016885906457901, -0.010919122025370598, -0.052861832082271576, 0.01907157525420189, -0.009895454160869122, 0.0518658310174942, -0.032333143055438995, -0.017881907522678375, -0.02596980519592762, 0.01586223766207695, 0.019090019166469574, 0.023018689826130867, 0.017254795879125595, -0.007280951831489801, 0.03371647745370865, -0.020694687962532043, -0.01610201597213745, -0.003405309049412608, -0.0372762605547905, -0.008014119230210781, -0.024236025288701057, -0.009102342650294304, 0.013602791354060173, -0.007184118498116732, -0.012901902198791504, -0.032406922429800034, -0.040946707129478455, -0.0688716247677803, 0.006418673321604729, -0.008327675051987171, 0.004565004725009203, -0.05371027812361717, 0.013399902731180191, 0.019606465473771095, -0.008811842650175095, -0.05182894319295883, 0.05784183740615845, -0.028146252036094666, -0.01114045549184084, 0.007308618631213903, 0.009701787494122982, 0.0007769730291329324, -0.1018134355545044, 0.06732229143381119, 0.005146005190908909, 0.04832449555397034, 0.009014731273055077, 0.012081123888492584, 0.054706279188394547, 0.023018689826130867, 0.028662696480751038, -0.012763569131493568, 0.038917817175388336, 0.013206236064434052, -0.024844693019986153, -0.015687016770243645, 0.007193340919911861, -0.0005902228294871747, 0.004947727080434561, 0.09561609476804733, 0.005828450433909893, 0.010098343715071678, -0.018905574455857277, -0.0028750307392328978, -0.00053978874348104, 0.006377173122018576, -0.05013205111026764, -0.03530270233750343, -0.020860688760876656, -0.0054780058562755585, 0.049099162220954895, 0.010670121759176254, -0.003114808816462755, -0.06603118032217026, -0.003225475549697876, 0.02178291045129299, 0.03214870020747185, 0.052898719906806946, 0.003912531770765781, -0.030580921098589897, 0.008728842251002789, 0.009757121093571186, 0.012403901666402817, -0.010707011446356773, -0.003248531138524413, -0.007290174253284931, -0.0437871553003788, 0.0027966417837888002, -0.012035012245178223, -0.0178542397916317, -0.025287359952926636, 0.00506300525739789, -0.02980625256896019, -0.0773191899061203, -0.010568677447736263, 0.0023136273957788944, 0.0011677651200443506, 0.008415286429226398, 0.03150314465165138, -0.0227973572909832, 0.02252068929374218, -0.019440464675426483, 0.04452493414282799, -0.052861832082271576, 0.026025138795375824, -0.022705134004354477, 0.017955685034394264, -0.004498143680393696, -0.0017038072692230344, -0.0010161746758967638, 0.026615360751748085, -0.010282788425683975, 0.02604358270764351, -0.018656574189662933, -0.0218935776501894, -0.044783156365156174, 0.025748470798134804, -0.010522566735744476, 0.027666695415973663, 0.03670448064804077, -0.00011960081610595807, 0.03631714731454849, -0.016470905393362045, 0.013086346909403801, -0.04924671724438667, -0.03528425842523575, -0.016784461215138435, 0.006086673121899366, -0.006049783900380135, -0.04430359974503517, -0.03469403460621834, -0.05577605590224266, 0.005856117233633995, 0.01411923673003912, -0.009720232337713242, -0.008134008385241032, -0.03500759229063988, 0.028644252568483353, -0.01816779561340809, -0.0033361422829329967, 0.019182242453098297, 0.002272127429023385, -0.004196115303784609, 0.008719620294868946, -0.0025499470066279173, -0.027869584038853645, 0.007004284765571356, -0.01958801969885826, -0.041463155299425125, 0.0033522811718285084, 0.04002448543906212, 0.04518893361091614, -0.012450012378394604, 0.007396230008453131, -0.04736538231372833, 0.019920021295547485, -0.12202856689691544, 0.046443160623311996, -0.013095568865537643, 0.0029003918170928955, -0.01625879481434822, -0.03408536687493324, 0.004613421391695738, 0.010919122025370598, -0.036409370601177216, -0.08086052536964417, 0.02283424511551857, 0.007977230474352837, 0.025527138262987137, -0.03155847638845444, -0.018306130543351173, 0.00918534304946661, -0.042717378586530685, -0.022391578182578087, 0.025213580578565598, 0.0340115912258625, 0.0037995595484972, 0.008355341851711273, 0.02862580679357052, 0.008018730208277702, -0.051017384976148605, 0.003942504059523344, -0.02158002182841301, -0.06772807240486145, -0.00938362069427967, -0.04707027226686478, -0.03266514465212822, -0.026117360219359398, 0.03947115316987038, 0.05533339083194733, -0.010725455358624458, -0.01864735223352909, -0.01214567944407463, -0.005229005590081215, -0.031669143587350845, 0.004177670925855637, 0.013123235665261745, 0.005773116834461689, -0.009877010248601437, -0.001988543663173914, -0.03181669861078262, 0.04496760293841362, 0.006169673055410385, -0.012809679843485355, -0.054853834211826324, -0.002355127362534404, -0.013123235665261745, 0.06817073374986649, -0.01202579028904438, -0.014128458686172962, 0.037626706063747406, 0.05839516967535019, -0.03653848171234131, 0.013114013709127903, 0.004304476547986269, 0.035782258957624435, -0.03604048117995262, -0.00536733865737915, -0.019145352765917778, -0.0065293400548398495, 0.01931135356426239, -0.01660001650452614, -0.016194239258766174, 0.0013637375086545944, -0.04965249449014664, 0.03947115316987038, 0.006128172855824232, -0.007866563275456429, -0.02419913560152054, -0.03408536687493324, -0.02098979987204075, -0.036132704466581345, 0.0009043550817295909, -0.016655350103974342, -0.010919122025370598, 0.0019435853464528918, -0.02314780093729496, 0.004850893747061491, -0.02893936261534691, 0.013197013176977634, 0.027943361550569534, -0.06426051259040833, -0.006847506854683161, 0.008807231672108173, -0.060424063354730606, 0.0005478581879287958, -0.035542480647563934, -0.03423292562365532, -0.021746022626757622, -0.033310700207948685, -0.019108464941382408, 0.010430344380438328, -0.03211181238293648, 0.04758671671152115, 0.004283726681023836, -0.013667347840964794, -0.04518893361091614, 0.016323350369930267, -0.014110014773905277, 0.033273812383413315, -0.022373134270310402, 0.03367958962917328, -0.0034698646049946547, -0.0010196330258622766, 0.0294926967471838, 0.04614804685115814, -0.031152699142694473, -0.028459807857871056, 0.0254533588886261, 0.020547132939100266, -0.052271608263254166, -0.006289562210440636, 0.010365788824856281, 0.03628025949001312, 0.06378095597028732, 0.013021791353821754, 0.04736538231372833, -0.015189015306532383, -0.03545025736093521, -0.009683343581855297, 0.028994696214795113, 0.07916363328695297, 0.054780054837465286, 0.022908024489879608, -0.037110261619091034, -0.029363585636019707, 0.03183514252305031, -0.03019358590245247, -0.033347588032484055, 0.02840447425842285, 0.025527138262987137, 0.0361880362033844, 0.0006928201764822006, 0.030617808923125267, -0.026615360751748085, -0.0020773077849298716, -0.013888681307435036, -0.011979678645730019, -0.00041817055898718536, -0.022373134270310402, -0.008678119629621506, 0.0029142252169549465, -0.0017568351468071342, -0.018536685034632683, -0.009194565005600452, -0.047771159559488297, 0.007456174120306969, 0.03436203673481941, 0.040319595485925674, 0.027390027418732643, 0.027832696214318275, 0.013123235665261745, -0.056071169674396515, -0.013888681307435036, 0.011454012244939804, 0.046480048447847366, 0.0017914185300469398, 0.0012300151865929365, -0.013233902864158154, -0.021764466539025307, -0.03810626268386841, 0.07562229782342911, -0.00034958022297360003, 0.010568677447736263, -0.018748797476291656, 0.0109467888250947, 0.023442912846803665, 0.028810251504182816, -0.025287359952926636, -0.006086673121899366, 0.0035713091492652893, -0.01993846520781517, 0.005182894412428141, 0.023590467870235443, 0.013206236064434052, -0.01586223766207695, 0.026117360219359398, 0.003822615137323737, -0.019256019964814186, -0.007516118697822094, -0.016738351434469223, -0.011988901533186436, -0.03189047798514366, 0.03924981877207756, 0.06544095277786255, -0.016268016770482063, -0.007004284765571356, -0.0429387092590332, 0.06592050939798355, -0.0009971538092941046, 0.06134628504514694, -0.002182210562750697, 0.03439892455935478, 0.002778197405859828, -0.026338694617152214, 0.04109426587820053, -0.03395625576376915, -0.009116175584495068, -0.014838570728898048, -0.02691047266125679, -0.030009141191840172, -0.03450959175825119, 0.001926293596625328, -0.0350998155772686, -0.02844136208295822, 0.0004956949269399047, 0.011675345711410046, -0.018675019964575768, -0.005524116568267345, 0.04175826534628868, 0.00019813388644251972, -0.029160697013139725, 0.016129683703184128, -0.017476128414273262, 0.02980625256896019, -0.007036563009023666, 0.059354282915592194, 0.006331061944365501, 0.010181344114243984, -0.03338447958230972, -0.015410348773002625, 0.005358116701245308, -0.005118338391184807, 0.013169347308576107, 0.00811095256358385, 0.010098343715071678, -0.022502245381474495, -0.019145352765917778, 0.011638456024229527, 0.030691586434841156, 0.02809091843664646, 0.02495535835623741, -0.0006553548155352473, 0.004276809748262167, -0.052419163286685944, -0.02080535516142845, 0.025822248309850693, -0.01802024058997631, 0.09723921120166779, -0.011149678379297256]', 'distance': 0.6690157055854797}, {'title': 'Biological Sequence with Language Model Prompting: A Survey', 'authors': ['Jiyue Jiang', 'Zikang Wang', 'Yuheng Shan', 'Heyan Chai', 'Jiayi Li', 'Zixian Ma', 'Xinrui Zhang', 'Yu Li'], 'summary': 'Large Language models (LLMs) have emerged as powerful tools for addressing\\nchallenges across diverse domains. Notably, recent studies have demonstrated\\nthat large language models significantly enhance the efficiency of biomolecular\\nanalysis and synthesis, attracting widespread attention from academics and\\nmedicine. In this paper, we systematically investigate the application of\\nprompt-based methods with LLMs to biological sequences, including DNA, RNA,\\nproteins, and drug discovery tasks. Specifically, we focus on how prompt\\nengineering enables LLMs to tackle domain-specific problems, such as promoter\\nsequence prediction, protein structure modeling, and drug-target binding\\naffinity prediction, often with limited labeled data. Furthermore, our\\ndiscussion highlights the transformative potential of prompting in\\nbioinformatics while addressing key challenges such as data scarcity,\\nmultimodal fusion, and computational resource limitations. Our aim is for this\\npaper to function both as a foundational primer for newcomers and a catalyst\\nfor continued innovation within this dynamic field of study.', 'published': '2025-03-06T06:28:36+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2503.04135v1', 'vector': '[-0.03366374596953392, -0.023800363764166832, -0.012258507311344147, -0.020632009953260422, -0.024083252996206284, -0.013804965652525425, 0.004396560601890087, -0.008500803261995316, 0.001223493367433548, 0.002350333146750927, -0.05582335591316223, 0.011060945689678192, -0.016850732266902924, 0.010099124163389206, -0.037020690739154816, -0.006652597803622484, -0.03366374596953392, -0.009759658016264439, -0.030476534739136696, -0.02493191882967949, -0.0071523673832416534, 0.03204185143113136, 0.06408370286226273, 0.00012000666174571961, 0.002175885019823909, 0.02896779589354992, -0.02078288421034813, -0.03136292099952698, -0.0248753409832716, 0.01767111010849476, 0.0034040932077914476, 0.0016655068611726165, 0.06302759051322937, -0.009241028688848019, -0.043187662959098816, -0.03766190633177757, 0.02755335345864296, -0.014059565030038357, -0.03355059027671814, 0.0104763088747859, -0.0341918058693409, -0.009217454120516777, -0.014559335075318813, -0.011683300137519836, 0.018406620249152184, -0.015200548805296421, 0.002614362398162484, -0.00943905021995306, 0.024347281083464622, -0.04695951193571091, -0.0222161877900362, -0.03449355438351631, 0.037737343460321426, -0.033965494483709335, -0.0026096475776284933, -0.02248021587729454, -0.05804874747991562, -0.0006506438367068768, -0.06464948505163193, -0.006968489848077297, -0.0009364792495034635, 0.009806806221604347, -0.00611039437353611, -0.0067327492870390415, 0.016388680785894394, 0.0002811205922625959, -0.04824194312095642, 0.04616742581129074, -0.028194567188620567, -0.09090154618024826, 0.03138177841901779, 0.011777596548199654, 0.0025483551435172558, -0.01767111010849476, -0.06495123356580734, 0.037077270448207855, -0.004566294141113758, -0.03147607669234276, -0.0477893203496933, 0.025931457057595253, -0.02836430072784424, -0.025950316339731216, 0.003507819026708603, -0.0111080938950181, 0.007171226665377617, -0.016520695760846138, -0.038114529103040695, 0.012117062695324421, 0.013946409337222576, -0.006704460363835096, -0.007463545072823763, 0.017746547237038612, 0.006916626822203398, -0.064460888504982, -0.05133485794067383, 0.00841593649238348, -0.0581619031727314, 0.02504507265985012, 0.016030356287956238, -0.0015653170412406325, -0.03436153754591942, 0.03164580836892128, 0.014757356606423855, 0.01402184646576643, 0.008015178143978119, -0.04182979837059975, -0.02166926860809326, 0.014351882971823215, -0.005686061456799507, -0.017916280776262283, 0.002283147070556879, 0.01337120309472084, 0.03158923238515854, 0.030759423971176147, -0.005587050691246986, -0.05703034996986389, 0.04620514437556267, -0.027741944417357445, 0.04115086793899536, -0.003927437122911215, 0.014106713235378265, 0.04594111442565918, 0.04194295406341553, -0.04888315498828888, -0.019208138808608055, -0.023649489507079124, 0.0039439392276108265, -0.007878448814153671, -0.044771838933229446, 0.006563016213476658, 0.019726768136024475, -0.007175941485911608, -0.04511130601167679, 0.02066972851753235, -0.0681195855140686, -0.05861452594399452, -0.00828392244875431, 0.0023526905570179224, -0.016152940690517426, -0.007732289377599955, 0.03381462022662163, 0.0367189459502697, 0.02368720807135105, -0.04066052660346031, 0.025893740355968475, -0.06815730035305023, 0.006907197646796703, -0.0013708311598747969, -0.04239557683467865, -0.05472952127456665, 0.0012506034690886736, 0.03760533034801483, 0.02274424582719803, 0.038774602115154266, 0.0023444395046681166, -0.010061405599117279, -0.025667428970336914, 0.023970097303390503, -0.0027180882170796394, 0.039528969675302505, 0.014776215888559818, -0.008533807471394539, -0.01634153351187706, -0.0033475153613835573, 0.018208598718047142, -0.012994017452001572, 0.02280082367360592, 0.0364360548555851, -0.045262180268764496, -0.04058508947491646, 0.012918581254780293, 0.005954805761575699, -0.011947330087423325, 0.011447560042142868, 0.0012093489058315754, -0.0028925363440066576, 0.04043421521782875, 0.03918950632214546, 0.034625567495822906, 0.0028053121641278267, -0.017605101689696312, 0.004596940241754055, 0.011438130401074886, -0.007972744293510914, -0.052240099757909775, 0.0017692326800897717, -0.015049675479531288, -0.05574791878461838, 0.014625342562794685, -0.003578541101887822, -0.0030528397765010595, -0.06921342015266418, 0.03409751132130623, 0.03651149198412895, 0.03253219276666641, -0.06800642609596252, -0.015172259882092476, -0.027628790587186813, 0.019330723211169243, -0.035436514765024185, 0.003373446874320507, 0.03862372785806656, -0.03779391944408417, 0.01718076877295971, -0.06310302764177322, -0.014351882971823215, -0.039906155318021774, -0.06065132096409798, -0.015408000908792019, -0.037680767476558685, 0.03272078558802605, 0.01861407235264778, 0.027270464226603508, -0.0030528397765010595, 0.005002413876354694, 0.003140063723549247, -0.03736015781760216, 0.07042040675878525, 0.02280082367360592, -0.035436514765024185, -0.05865224450826645, 0.0671389028429985, -0.0017609817441552877, 0.0031589230056852102, 0.007850159890949726, -0.0216504093259573, 0.050052426755428314, 0.00945319514721632, 0.00713350810110569, -0.01514397095888853, 0.01980220340192318, 0.0069590602070093155, -0.011164671741425991, -0.039264943450689316, -0.018491486087441444, -0.012871433049440384, 0.02264994941651821, 0.018350042402744293, 0.011004367843270302, 0.010504597797989845, 0.021480677649378777, -0.05631369724869728, 0.014531046152114868, 0.0005121462745591998, -0.04579024016857147, -0.06298986822366714, 0.006331990472972393, 0.01887810043990612, -0.0058510797098279, 0.028835780918598175, 0.004528575576841831, -0.004879829008132219, -0.046997230499982834, -0.018086012452840805, 0.02634636126458645, 0.007878448814153671, 0.010712049901485443, -0.013786106370389462, -0.029175247997045517, -0.0036681226920336485, 0.04213154688477516, 0.04130173847079277, 0.02706301212310791, 0.010561175644397736, 0.04654460772871971, -0.018802665174007416, -0.027760803699493408, -0.024517014622688293, 0.0025837160646915436, 0.04216926544904709, -0.01593605987727642, -0.03536107763648033, 0.03317340835928917, -0.0077040004543960094, -0.018868671730160713, 0.040132466703653336, -0.02842087857425213, -0.023875800892710686, 0.05058048665523529, 0.014465038664638996, -0.025177087634801865, -0.00029600170091725886, 0.008477229624986649, -0.018199168145656586, 0.04213154688477516, -0.03575712442398071, -0.04152804985642433, -0.04401747137308121, -0.031721245497465134, -0.04058508947491646, 0.022631090134382248, -0.010759197175502777, 0.04277276247739792, -0.01792570948600769, -0.04884543642401695, 0.029175247997045517, 0.0013708311598747969, -0.15917199850082397, -0.023328881710767746, -0.02625206485390663, 0.0018965324852615595, 0.008397077210247517, 0.011522997170686722, -0.02683670073747635, 0.02078288421034813, 0.003540822770446539, 0.018896959722042084, -0.014144431799650192, -0.061745159327983856, 0.019387301057577133, 0.021593831479549408, -0.03911406919360161, 0.04224470257759094, -0.028835780918598175, -0.00538902822881937, -0.016096362844109535, -0.015030816197395325, -0.027025293558835983, -0.05401286855340004, 0.04413062706589699, 0.0033050822094082832, 0.016643282026052475, -0.008406506851315498, 0.014738497324287891, 0.024950778111815453, -0.014851653017103672, 0.026478376239538193, -0.013880401849746704, 0.0025672144256532192, -0.00488454382866621, 0.04413062706589699, 0.03677552193403244, 0.040245622396469116, -0.005436176434159279, -0.005643628071993589, -0.023272303864359856, 0.00873182900249958, -0.00041755542042665184, 0.05472952127456665, 0.0075106932781636715, 0.016086934134364128, 0.0029561861883848906, -0.023102572187781334, -0.00837350357323885, -0.02640293911099434, 0.004328195936977863, -0.03338085860013962, 0.004337625578045845, -0.014153861440718174, -0.019839921966195107, 0.02438499964773655, -0.051598887890577316, -0.019151560962200165, -0.0024328422732651234, 0.02421526610851288, -0.00457572378218174, -0.030853720381855965, 0.023724926635622978, 0.0010119162034243345, 0.04273504391312599, -0.06691259145736694, -0.032004132866859436, -0.05386199429631233, 0.039642125368118286, -0.013644661754369736, 0.000506842159666121, -0.052126944065093994, 0.020009655505418777, -0.013324054889380932, 0.020066233351826668, -0.019764484837651253, 0.031136609613895416, 0.03400321304798126, -0.0060915350914001465, -0.00220535253174603, 4.39435061707627e-05, -0.07226861268281937, -0.019113842397928238, -0.0026968715246766806, 0.008642247878015041, 0.03272078558802605, -0.004662947729229927, -0.01691674068570137, 0.03411636874079704, 0.009674791246652603, 0.05665316432714462, 0.24577364325523376, 0.007142937742173672, -0.03796365484595299, -0.029835321009159088, 0.054314617067575455, -0.0070250676944851875, -0.03720928356051445, 0.05084451660513878, 0.016709288582205772, -0.03300367295742035, 0.049901556223630905, 0.03389005735516548, -0.0026049327570945024, 0.01997193694114685, -0.0015641383361071348, 0.025573132559657097, -0.07340016961097717, -0.01708647422492504, 0.06959060579538345, -0.02766650728881359, -0.0057756430469453335, 0.022763105109333992, -0.0007608525338582695, -0.019632471725344658, -0.022404778748750687, -0.00955220591276884, 0.05193835124373436, -0.019462738186120987, 0.010919501073658466, -0.023819223046302795, -0.037190426141023636, 0.038321979343891144, 0.04028334096074104, -0.001533492119051516, -0.006534727290272713, 0.031230904161930084, -0.008128332905471325, 9.705879165267106e-06, -0.03507819026708603, -0.0005713761202059686, -0.007105219643563032, 0.008972284384071827, -0.06359336525201797, 0.011032656766474247, 0.00913730263710022, -0.037077270448207855, 0.021424099802970886, 0.0029561861883848906, 0.015389141626656055, -0.02182014286518097, -0.014889371581375599, 0.01320146955549717, 0.0017232632962986827, -0.0064498609863221645, 0.02389466017484665, 0.02117892913520336, -0.02657267265021801, 0.011475848965346813, 0.019406160339713097, 0.036964114755392075, 0.042206984013319016, 0.012258507311344147, -0.0141915800049901, 0.018048293888568878, -0.009198595769703388, 0.004339983221143484, 0.020990336313843727, -0.01536085270345211, 0.04861912503838539, 0.0399438738822937, 0.051598887890577316, 0.022725386545062065, -0.005964235402643681, 0.018265176564455032, 0.02678012289106846, -0.006402712780982256, 0.08252803981304169, 0.06593190878629684, -0.012230218388140202, 0.020688587799668312, -0.012956298887729645, 0.01565317064523697, 0.015785185620188713, -0.013361773453652859, 0.014408460818231106, -0.0009518023580312729, -0.019274145364761353, 0.07622905820608139, -0.0216504093259573, -0.020971477031707764, -0.03628518059849739, -0.04005702957510948, 0.011211819015443325, 0.021310944110155106, 0.022461356595158577, 0.019010115414857864, 0.019519316032528877, -0.055521607398986816, -0.036756664514541626, 0.01985878124833107, 0.05205150693655014, -0.007562556304037571, 0.030910298228263855, -0.03038223832845688, 0.008364073932170868, 0.0135597949847579, -0.013126032426953316, 0.03558738902211189, 0.0295712910592556, 0.05593651160597801, -0.045149024575948715, 0.02925068512558937, -0.009740798734128475, -0.033211126923561096, 0.009863384068012238, 0.032928235828876495, 0.006515868008136749, 0.010212279856204987, 0.036530353128910065, -0.006421572063118219, -0.01844433881342411, 0.07366420328617096, -0.05231553688645363, 0.04118858650326729, -0.02847745642066002, -0.029005514457821846, 0.009391902945935726, -0.0014898801455274224, -0.01925528608262539, 0.024252984672784805, 0.04239557683467865, 0.014059565030038357, -0.0063508497551083565, 0.04088683798909187, 0.00982566550374031, -0.00031618698267266154, 0.030193647369742393, -0.008595099672675133, -0.030966876074671745, 0.012692269869148731, -0.027572212740778923, 0.023102572187781334, 0.0030292656738311052, 0.019349582493305206, 0.005115569569170475, 0.025497695431113243, -0.03404093161225319, -0.004955265671014786, -0.008298066444694996, 0.03245675563812256, 0.021744705736637115, -0.009401332587003708, 0.06023642048239708, -0.03326770290732384, 0.019330723211169243, -0.010353723540902138, -0.05887855216860771, 0.016756435856223106, -0.01041973102837801, -0.016548985615372658, 0.010353723540902138, 0.06778011471033096, 0.03775620087981224, -0.013870972208678722, -0.00022336414258461446, 0.008514948189258575, 0.004667662549763918, 0.09225941449403763, -0.007873733527958393, -0.026063472032546997, 0.00908072479069233, -0.027968255802989006, -0.016002066433429718, 0.04496043175458908, -0.015549445524811745, 0.032268162816762924, -0.0597083605825901, 0.04447009414434433, -0.025573132559657097, -0.03300367295742035, 0.021141210570931435, -0.0015311347087845206, -0.026591531932353973, 0.05789787322282791, 0.020066233351826668, -0.013295765966176987, 0.008670536801218987, -0.009010002948343754, 0.041226305067539215, -0.011343833990395069, -0.020613152533769608, 0.10116097331047058, -0.023762645199894905, -0.01599263772368431, 0.03622860461473465, 0.016152940690517426, 0.06721433997154236, -0.015983207151293755, 0.04507358744740486, -0.03519134595990181, 0.02112235128879547, -0.05107082799077034, -0.007175941485911608, -0.0013342914171516895, -0.012616832740604877, 0.009274031966924667, 0.013248617760837078, 0.025177087634801865, -0.03230588138103485, -0.008397077210247517, -0.01896296814084053, -0.023611770942807198, -0.049298059195280075, 0.031249763444066048, -0.004594583064317703, -0.012616832740604877, 0.0002615246339701116, 0.029005514457821846, -0.0019743270240724087, 0.045526210218667984, 0.01904783397912979, -0.022876260802149773, -0.010495168156921864, -0.032173868268728256, -0.04620514437556267, -0.004622871521860361, 0.010891212150454521, -0.018755516037344933, 0.009665361605584621, 0.036700084805488586, -0.0021134137641638517, -0.012381092645227909, -0.017878562211990356, 0.025384539738297462, 0.03060854971408844, 0.03496503457427025, 0.014248157851397991, -0.010721479542553425, -0.026176627725362778, -0.018774375319480896, -0.05084451660513878, 0.05910486355423927, 0.017821984365582466, 0.04824194312095642, -0.016596132889389992, -0.011522997170686722, 0.016379252076148987, -0.009797376580536366, 0.021235506981611252, -0.0012128850212320685, 0.03443697467446327, -0.025007355958223343, -0.016784725710749626, 0.01662442274391651, 0.06219778209924698, -0.014465038664638996, 0.01879323460161686, -0.029986195266246796, -0.03869916498661041, -0.00950977299362421, -0.01331462524831295, -0.008967570029199123, 0.015672029927372932, -0.0002874561178032309, -0.07592730969190598, -0.03121204487979412, -0.009132588282227516, 0.006728034466505051, -0.0019389658700674772, -0.051485732197761536, 0.028986655175685883, 0.0011462883558124304, -0.022084172815084457, -0.09716281294822693, 0.03043881617486477, -0.01651126705110073, -0.008510232903063297, 0.027345901355147362, 0.03460671007633209, 0.011390982195734978, -0.06227321922779083, 0.03255105018615723, 0.02628978341817856, -0.03490845859050751, 0.013342914171516895, 0.03247561678290367, 0.027685366570949554, -0.0058982279151678085, 0.016756435856223106, -0.016332102939486504, 0.02842087857425213, 0.03692639619112015, -0.023988956585526466, -0.022668808698654175, 0.056615445762872696, 0.02985418029129505, 0.002350333146750927, 0.07015638053417206, -0.02481876313686371, -0.014257587492465973, -0.033361997455358505, -0.001261211815290153, 0.025195946916937828, 0.019877640530467033, -0.03322998434305191, 0.026440657675266266, -0.02564856968820095, -0.0251582283526659, 0.06095306947827339, 0.01867065019905567, -0.01390869077295065, -0.01536085270345211, 0.030816001817584038, 0.031061172485351562, 0.04643145576119423, 0.04213154688477516, 0.007562556304037571, -0.013163750991225243, 0.02881692163646221, 0.00534659530967474, 0.03766190633177757, 0.030797142535448074, 0.000607621215749532, -0.02749677561223507, 0.00274873455055058, -0.013672950677573681, 0.04352713003754616, -0.002979760291054845, -0.007836014963686466, -0.014587623998522758, -0.04371572285890579, -0.06427229940891266, -0.018114302307367325, -0.00564834289252758, -0.00805761106312275, 0.0015028459019958973, 0.010504597797989845, -0.0124376704916358, 0.0334940142929554, -0.0038614298682659864, 0.04741213470697403, -0.04348941147327423, 0.027892818674445152, -0.024517014622688293, 0.023159150034189224, -0.03775620087981224, 0.008401792496442795, -0.0013142534298822284, 0.029231825843453407, -0.01784084364771843, 0.01304116565734148, -0.0433008186519146, -0.00118164939340204, -0.040509652346372604, 0.028986655175685883, 0.0036775521002709866, -0.006176401861011982, 0.03983071818947792, 0.05669088289141655, 0.023988956585526466, -0.02400781586766243, -0.011268396861851215, -0.029703306034207344, -0.032324742525815964, 0.01418215036392212, -0.021348662674427032, -0.000305873341858387, -0.020217107608914375, -0.01767111010849476, -0.07430541515350342, 0.00016722844156902283, 0.026120049878954887, -0.0005813950556330383, -0.00497883977368474, -0.020499996840953827, 0.021839002147316933, -0.029778743162751198, 0.010382012464106083, 0.023875800892710686, -0.0024328422732651234, 0.014644201844930649, -0.031777821481227875, -0.005124999210238457, -0.03590799868106842, 0.0023208654019981623, -0.02793053723871708, -0.03654921054840088, 0.025177087634801865, 0.044394657015800476, 0.07023181766271591, -0.0006029063952155411, 0.010495168156921864, -0.033418577164411545, -0.0012517821742221713, -0.13284450769424438, 0.011192959733307362, -0.0006382674910128117, -0.007581415120512247, -0.02161269076168537, -0.019330723211169243, 0.01298458781093359, -0.03109889104962349, -0.04005702957510948, -0.07140108942985535, 0.018576353788375854, -0.005016558337956667, 0.01683187298476696, -0.02625206485390663, -0.006138683296740055, 0.021461818367242813, -0.03888775780797005, -0.01339006144553423, 0.0052570137195289135, 0.007114649284631014, 0.010957219637930393, -0.01931186392903328, 0.0227065272629261, -0.02564856968820095, -0.01459705363959074, -0.008288636803627014, -0.008246203884482384, -0.05899170786142349, -0.00668560154736042, 0.0037482744082808495, -0.008967570029199123, -0.05963292345404625, 0.021046914160251617, 0.03028794191777706, 0.01082520466297865, -0.013578654266893864, -0.024479296058416367, -0.010514027439057827, 0.02576172538101673, 0.012673410587012768, 0.03449355438351631, -0.0020344406366348267, -0.03262648731470108, 0.0124376704916358, -0.02832658216357231, 0.06257496774196625, -0.02602575346827507, -0.026648109778761864, -0.04518674314022064, 0.01191904116421938, 0.029552431777119637, 0.06038729473948479, -0.0159077700227499, -0.017529666423797607, 0.0050307027995586395, -0.006784612312912941, -0.04567708447575569, -0.00326029141433537, -0.026817841455340385, 0.07619133591651917, -0.03211728855967522, 0.02951471321284771, -0.0266292504966259, -0.01620008982717991, -0.008906276896595955, 0.0029184676241129637, -0.04801563173532486, -0.028175707906484604, -0.05861452594399452, 0.011843604035675526, -0.011862463317811489, -0.0045120734721422195, -0.03802023082971573, -0.03526678308844566, 0.0012494247639551759, -0.040622808039188385, 0.013880401849746704, -0.023819223046302795, -0.007076930720359087, -0.008524377830326557, -0.008123618550598621, 0.006086820270866156, -0.04265960678458214, -0.012654551304876804, 0.013012876734137535, -0.0641968622803688, -0.02182014286518097, 0.025893740355968475, -0.07411681860685349, 0.005681346636265516, -0.014738497324287891, -0.023819223046302795, -0.015690889209508896, -0.04529989883303642, -0.02029254473745823, 0.03089143894612789, -0.025893740355968475, -0.006034957244992256, -0.00010866164666367695, -0.07407910376787186, -0.017425939440727234, 0.056011948734521866, 0.016558414325118065, 0.03869916498661041, 0.02117892913520336, 0.039378099143505096, 0.01337120309472084, -0.0032037138007581234, 0.04450781270861626, 0.033361997455358505, -0.03158923238515854, -0.03632289916276932, -0.00028347797342576087, -0.004639373626559973, -0.0351724848151207, -0.006718604825437069, 0.009316465817391872, 0.05374883860349655, 0.00043729867320507765, 0.004988269414752722, 0.04296135529875755, 0.015238267369568348, -0.018944108858704567, -0.017972858622670174, 0.008397077210247517, 0.06204690784215927, 0.025233665481209755, -0.00688833836466074, -0.017048755660653114, -0.009938820265233517, 0.041603486984968185, -0.007581415120512247, -0.02461131103336811, -0.0010808702791109681, 0.049071747809648514, 0.01530427485704422, 0.011711589060723782, 0.03541765734553337, -0.03109889104962349, 2.729433253989555e-05, -0.045262180268764496, -0.011390982195734978, -0.012899721972644329, -0.019113842397928238, -0.02132980339229107, -0.001830525230616331, 0.047713883221149445, -0.010646042414009571, 0.007307956460863352, -0.025026213377714157, 0.011428700760006905, -0.02449815534055233, -0.0028477455489337444, 0.014757356606423855, 0.04145261272788048, 0.00795388501137495, -0.04066052660346031, -0.032155007123947144, -0.005535187665373087, 0.06095306947827339, 0.012192499823868275, -0.05035417526960373, -0.009816235862672329, -0.03711498901247978, -0.03645491600036621, 0.03971756249666214, 0.012135921977460384, -0.01792570948600769, -0.02700643427670002, 0.03277736157178879, 0.018802665174007416, 0.009217454120516777, -0.025629710406064987, -0.009806806221604347, 0.005577621050179005, -0.011362693272531033, -0.010278287343680859, 0.041603486984968185, 0.048468250781297684, 0.0046087270602583885, 0.026553813368082047, -0.005407887510955334, 0.023819223046302795, 0.03986843675374985, 0.01181531511247158, 0.020047374069690704, 0.027949396520853043, 0.03775620087981224, 0.07313614338636398, -0.027628790587186813, -0.007086360361427069, -0.0141915800049901, 0.02985418029129505, 0.018406620249152184, 0.03632289916276932, 0.037737343460321426, 0.04265960678458214, 0.011428700760006905, 0.008797836489975452, 0.032324742525815964, -0.02870376594364643, 0.004245686810463667, -0.031513795256614685, -0.0552198626101017, -0.024422718212008476, 0.023649489507079124, -0.004752528853714466, -0.05680403858423233, -0.037303581833839417, -0.03272078558802605, 0.01922699809074402, 0.023234587162733078, -0.013031736016273499, 0.009500343352556229, 0.024705607444047928, -0.00478317542001605, 0.016039784997701645, -0.0016820086166262627, 0.011419271118938923, -0.001994364894926548, 0.0341918058693409, 0.014776215888559818, 0.01210763305425644, 0.006374423857778311, -0.061292536556720734, -0.02193329855799675, -0.006176401861011982, 0.007157082203775644, -0.009721939451992512, 0.003918007481843233, -0.03473872318863869, -0.014634772203862667, 0.023555193096399307, 0.026987574994564056, 0.024102112278342247, 0.002996262162923813, 0.030212506651878357, 0.03187211975455284, -0.005686061456799507, -0.020877180621027946, 0.009835095144808292, 0.01974562741816044, 0.056125104427337646, 0.009443765506148338]', 'distance': 0.670582115650177}, {'title': 'Large Language Models Perform Diagnostic Reasoning', 'authors': ['Cheng-Kuang Wu', 'Wei-Lin Chen', 'Hsin-Hsi Chen'], 'summary': \"We explore the extension of chain-of-thought (CoT) prompting to medical\\nreasoning for the task of automatic diagnosis. Motivated by doctors' underlying\\nreasoning process, we present Diagnostic-Reasoning CoT (DR-CoT). Empirical\\nresults demonstrate that by simply prompting large language models trained only\\non general text corpus with two DR-CoT exemplars, the diagnostic accuracy\\nimproves by 15% comparing to standard prompting. Moreover, the gap reaches a\\npronounced 18% in out-domain settings. Our findings suggest expert-knowledge\\nreasoning in large language models can be elicited through proper promptings.\", 'published': '2023-07-18T01:43:00+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2307.08922v1', 'vector': '[-0.02140681818127632, -0.03161130100488663, -0.01141885295510292, 0.004217350389808416, -0.027921119704842567, -0.03720305487513542, 0.012501431629061699, -0.02400500699877739, -0.015542065724730492, -0.021180888637900352, -0.018159082159399986, -0.0010619862005114555, -0.02191516011953354, 0.04194757342338562, -0.05444900691509247, -0.015278481878340244, -0.04480934888124466, -0.009686727076768875, -0.020898478105664253, -0.022442329674959183, -0.02549237757921219, 0.013772284612059593, 0.039725933223962784, 0.04503527656197548, -0.022442329674959183, 0.015645617619156837, -0.04270067438483238, -0.055842239409685135, 0.03407769650220871, 0.018667424097657204, -0.024513348937034607, -0.03264680877327919, 0.06397569924592972, 0.010806960053741932, 0.017848430201411247, -0.03379528596997261, -0.02234819158911705, -0.0202583447098732, -0.037843190133571625, 0.03479314222931862, 0.009634951129555702, -0.016144543886184692, 0.008618268184363842, -0.011654196307063103, 0.02681029960513115, -0.03901049122214317, -0.0014967828756198287, -0.028636561706662178, -0.023251909762620926, -0.0262078195810318, -0.014911346137523651, 0.005714133381843567, 0.03149833530187607, -0.03562154993414879, 0.012228433042764664, 0.0009284289553761482, -0.04812297970056534, -0.011993090622127056, -0.026998573914170265, -0.0004933382151648402, 0.012002503499388695, 0.015975097194314003, -0.009540813975036144, -0.03797497972846031, -0.014892518520355225, 0.010166826657950878, 0.014412418007850647, 0.02257412113249302, -0.01780136115849018, -0.032496191561222076, 0.0034383642487227917, 0.010863442905247211, -0.011936607770621777, -0.03275977447628975, -0.04804766923189163, 0.031178269535303116, 0.028222357854247093, 0.008519424125552177, 0.010195068083703518, 0.01483603660017252, -0.0076439473778009415, 0.02735629491508007, -0.0010613978374749422, -0.036355819553136826, -0.0023040100932121277, 0.018243806436657906, -0.03445424512028694, 0.020766684785485268, 0.03885987028479576, -0.011729505844414234, -0.019354626536369324, -0.018064945936203003, 0.021482128649950027, -0.030688755214214325, -0.012011917307972908, 0.02846711501479149, -0.0745190754532814, 0.001954525476321578, -0.012002503499388695, 0.015061965212225914, -0.004464460536837578, 0.01753777638077736, 0.004730398766696453, 0.012331984005868435, 0.03863394260406494, -0.04518589749932289, 0.010176240466535091, 0.03125358000397682, 0.014986655674874783, -0.010778719559311867, 0.02014537900686264, 0.04966683313250542, -0.007695722859352827, 0.030312206596136093, -0.005238739773631096, -0.016775263473391533, -8.24436719994992e-05, -0.03445424512028694, 0.005864752922207117, -0.01937345415353775, 0.034096524119377136, 0.03341873735189438, 0.06638561189174652, -0.0241556279361248, -0.060360826551914215, 0.022197572514414787, 0.043190184980630875, 0.008481769822537899, -0.02212226204574108, -0.006001251749694347, 0.018347356468439102, -0.005596461705863476, -0.03897283598780632, -0.006589610129594803, -0.03968827798962593, -0.07689133286476135, -0.02729981206357479, 0.012557913549244404, -0.016003338620066643, -0.0262078195810318, 0.061452820897102356, 0.04563775658607483, -0.02972855418920517, -0.02912607602775097, 0.053319357335567474, -0.05516444891691208, 0.0019486418459564447, -0.026716161519289017, 0.012171951122581959, 0.0016297517577186227, 0.0069002630189061165, 0.025247620418667793, -0.02355314791202545, 0.06175405904650688, -0.019561728462576866, -0.05015634745359421, -0.036468785256147385, 0.03885987028479576, 0.009314884431660175, 0.02129385434091091, -0.003666647244244814, 0.02317659929394722, 0.02185867726802826, 0.010082103312015533, -0.004753932822495699, -0.0019780597649514675, -0.009465504437685013, 0.07579934597015381, -0.03983889892697334, -0.07711726427078247, 0.019062800332903862, -0.03227026015520096, 0.03528265282511711, 0.005492910742759705, 0.00740389758720994, -0.0018286167178303003, 0.019825313240289688, 0.018234392628073692, 0.018592113628983498, 0.008232305757701397, -0.00022916545276530087, 0.0034430711530148983, 0.017867255955934525, -0.014920759946107864, -0.005026930943131447, -0.0003768433234654367, -0.01379111222922802, -0.024569831788539886, 0.015400859527289867, -0.03885987028479576, -0.010392756201326847, -0.030651099979877472, 0.03153599053621292, 0.0015826830640435219, -0.0036078114062547684, -0.0541854202747345, 0.00794048048555851, -0.04898904263973236, -0.006961452309042215, -0.02119971625506878, 0.02477693371474743, 0.03270329162478447, 0.009037179872393608, -0.04804766923189163, -0.04989276081323624, -0.0009919716976583004, -0.04684271290898323, -0.05776263773441315, 0.01885569840669632, -0.0058929938822984695, 0.006260129623115063, 0.0009637304465286434, 0.012859153561294079, 0.048424217849969864, -0.015457342378795147, 0.017584845423698425, -0.022385846823453903, -0.0023628459312021732, 0.011673022992908955, -0.05203909054398537, -0.013113323599100113, 0.06996282935142517, 0.013226288370788097, 0.005996545311063528, 0.031291235238313675, -0.022272881120443344, -0.0033065720926970243, -0.008632388897240162, -0.012482604011893272, -0.014930173754692078, 0.015664443373680115, 0.022385846823453903, -0.035207346081733704, -0.021595092490315437, 0.010119758546352386, -0.028448287397623062, 0.0020886710844933987, -0.0014508909080177546, -0.018705079331994057, -0.018949836492538452, -0.047256916761398315, -0.03087702952325344, 0.04925262928009033, 0.004918673075735569, -0.039387039840221405, -0.02042779140174389, -0.004603313282132149, 0.018328528851270676, -0.025059346109628677, -0.014723071828484535, -0.03021806851029396, -0.01412059273570776, -0.07079123705625534, 0.01679409109055996, 0.01260498259216547, -0.0295026246458292, -0.0022863594349473715, -0.0065943170338869095, 0.01885569840669632, 0.004768053535372019, 0.028975456953048706, 0.0014202962629497051, 0.0070320554077625275, 0.009969138540327549, -0.005478790029883385, 0.015240826644003391, -0.00522932643070817, -0.008759474381804466, 0.006942624691873789, 0.040930893272161484, 0.0020345421507954597, -0.005008103791624308, -0.032176125794649124, 0.007738084997981787, 0.018319115042686462, 0.04575072228908539, -0.03302336111664772, -0.022875361144542694, 0.062017641961574554, -0.024174455553293228, -0.007789860479533672, -0.01948641799390316, 0.02867421694099903, -0.020503100007772446, 0.007474500685930252, -0.03644995763897896, -0.03221377730369568, -0.01194602157920599, -0.010063275694847107, -0.03388942405581474, -0.004476227797567844, -0.011221164837479591, 0.08095806837081909, -0.03733484819531441, -0.019185177981853485, 0.011531817726790905, -0.0028170582372695208, -0.1584518849849701, 0.038558632135391235, -0.013405149802565575, 0.025944236665964127, 0.013809939846396446, 0.03761725872755051, -0.029408488422632217, -0.01164478249847889, -0.04597664996981621, -0.01673761010169983, 0.0006277780048549175, -0.06005958840250969, 0.014901932328939438, -0.024456866085529327, -0.04838656634092331, 0.007446259260177612, -0.023571975529193878, -0.013772284612059593, -0.010251550003886223, -0.024814588949084282, -0.014233557507395744, -0.043190184980630875, -0.0226306039839983, 0.0009472564561292529, -0.009526693262159824, 0.032176125794649124, 0.016690541058778763, 0.04315253347158432, -0.027770498767495155, -0.008232305757701397, 0.011936607770621777, -0.004619787447154522, -0.0014450072776526213, 0.006128337234258652, 0.00012186366075184196, 0.017933152616024017, 0.0030547548085451126, -0.004045549780130386, -0.04861249402165413, 0.006128337234258652, -0.004951621405780315, 0.07056530565023422, 0.007272105198353529, 0.05147426947951317, -0.03225143253803253, -0.02775167115032673, 0.0703393816947937, -0.008943041786551476, -0.02763870730996132, -0.056896574795246124, -0.0036454664077609777, -0.0030194534920156, -0.02069137617945671, 0.021538611501455307, -0.0506458580493927, -0.011955435387790203, 0.003996127750724554, -0.027073882520198822, -0.011597713455557823, -0.018385011702775955, -0.013772284612059593, -0.05170019716024399, 0.0276951901614666, -0.004603313282132149, -0.042474742978811264, 0.0076439473778009415, 0.08238895237445831, 0.0016309284837916493, 0.022536465898156166, -0.04959152266383171, 0.03424714505672455, -0.04409390315413475, -0.027073882520198822, -0.006584903225302696, 0.033927079290151596, 0.03673236817121506, 0.006787298247218132, 0.03671354055404663, -0.019128696992993355, -0.07824691385030746, -0.029973311349749565, 0.02718684822320938, 0.03362583741545677, 0.06066206842660904, 0.010326860472559929, -0.05301811918616295, 0.013941732235252857, 0.029257867485284805, 0.008590027689933777, 0.2358703762292862, 0.046880368143320084, -0.04789705201983452, -0.03733484819531441, 0.08562727272510529, 0.01244494877755642, 0.026283130049705505, 0.013216874562203884, -0.0020274817943573, -0.01997593231499195, 0.02355314791202545, 0.05301811918616295, 0.006189526524394751, 0.0211432334035635, -0.013772284612059593, 0.02483341656625271, -0.028598906472325325, 0.0021145588252693415, 0.0857778936624527, -0.03682650625705719, 0.005817684344947338, 0.03501906991004944, 0.015570307150483131, 0.004005541559308767, -0.055126793682575226, -0.03644995763897896, 0.013659319840371609, 0.004290306940674782, 0.02119971625506878, 0.027714017778635025, -0.049855105578899384, 0.03748546540737152, 0.03132888674736023, -0.00738977687433362, -0.00756863784044981, -0.003730189986526966, -0.04270067438483238, -0.02328956499695778, -0.004102032165974379, 0.03554623946547508, -0.025793615728616714, -0.002019244711846113, -0.05090944468975067, 0.008180529810488224, -0.008345270529389381, -0.021387990564107895, -0.0009207803523167968, 0.023251909762620926, 0.03584747761487961, -0.03070758283138275, -0.01613513007760048, -0.01293446309864521, -0.017330674454569817, -0.0019533487502485514, 0.025002863258123398, 0.010609271936118603, -0.022159917280077934, 0.04266301915049553, -0.0384644940495491, 0.029916828498244286, 0.006085975561290979, 0.02543589472770691, -0.014713658019900322, 0.0354144461452961, -0.026885608211159706, 0.03739133104681969, 0.006952038500458002, 0.004737459123134613, 0.021162061020731926, 0.040591996163129807, 0.06122688949108124, 0.05908055976033211, 0.0062836636789143085, -0.03274094685912132, 0.028598906472325325, -0.00874064676463604, 0.026057200506329536, 0.04865014925599098, 0.018375597894191742, -0.012595568783581257, -0.03727836534380913, -0.010750478133559227, 0.018695665523409843, -0.020729029551148415, 0.005756495054811239, -0.013894663192331791, -0.003899637144058943, 0.07489562779664993, -0.014798381365835667, -0.02472045086324215, -0.04206053912639618, -0.03565920516848564, -0.029257867485284805, -0.019185177981853485, -0.05561630800366402, 0.03225143253803253, 0.009046592749655247, -0.03539561852812767, -0.005897700786590576, -0.03516969084739685, 0.046202581375837326, -0.005535272415727377, 0.022649431601166725, 0.001481485553085804, 0.0512859933078289, -0.061076272279024124, -0.01513727568089962, 0.0030971167143434286, -0.02758222445845604, 0.029634417966008186, -0.0031324182637035847, 0.027036229148507118, 0.024212108924984932, -0.07165729999542236, -0.0017768412362784147, 0.0631849467754364, 0.0008972460054792464, 0.03953766077756882, 0.019938277080655098, 0.019938277080655098, -0.01911928318440914, 0.058214496821165085, -0.05708485096693039, 0.003174779936671257, -0.020239517092704773, -0.03543327376246452, 0.011371783912181854, 0.016012752428650856, -0.041796956211328506, 0.025454722344875336, -0.010684581473469734, 0.014205316081643105, 0.004059670493006706, 0.039876554161310196, -0.007483914028853178, -0.009121903218328953, 0.019750002771615982, 0.011343542486429214, 0.007305053528398275, 0.03793732449412346, -0.03837035596370697, -0.019561728462576866, -0.022272881120443344, 0.01078813336789608, 0.05885463207960129, 0.0471062958240509, -0.04360438883304596, -0.002325190929695964, -0.021783368661999702, 0.028147049248218536, 0.006481352262198925, -0.008985403925180435, 0.0306699275970459, -0.05531506985425949, 0.01348987314850092, -0.021500956267118454, -0.047859396785497665, 0.03078289143741131, 0.040930893272161484, -0.021933987736701965, -0.006533127743750811, 0.04017779231071472, 0.024136800318956375, 0.029521452262997627, 0.01263322401791811, 0.011964849196374416, 0.022385846823453903, 0.07308819144964218, 0.05693423002958298, -0.022423502057790756, 0.03268446400761604, -0.017810774967074394, -0.04175930097699165, 0.020408963784575462, -0.007126192562282085, -0.01409235130995512, -0.07346473634243011, 0.0241556279361248, -0.017302433028817177, 0.030368687584996223, 0.001833323622122407, 0.0069473315961658955, -0.014553624205291271, 0.03419066220521927, 0.010505720973014832, -0.008632388897240162, 0.013875835575163364, -0.015005483292043209, -0.02037130855023861, -0.02080434001982212, -0.037240710109472275, 0.08645568042993546, -0.03076406568288803, 0.012294329702854156, 0.02978503704071045, -0.025360584259033203, 0.07907532155513763, -0.011710678227245808, 0.014581865631043911, -0.0171424001455307, 0.0029111956246197224, 0.00453506363555789, -0.02020186185836792, 0.02895662933588028, -0.03882221505045891, -0.0032265554182231426, 0.014713658019900322, -0.016718782484531403, 0.015777409076690674, -0.044771693646907806, -0.054637279361486435, -0.0030123931355774403, -0.054900866001844406, 0.018846284598112106, -0.024626314640045166, 0.02647140435874462, 0.0007613353081978858, 0.01442183181643486, -0.004297367297112942, 0.005944769363850355, 0.01316039264202118, -0.0065095932222902775, -0.01164478249847889, -0.08035558462142944, -0.032891567796468735, 0.026452576741576195, -0.0024758107028901577, -0.016182199120521545, 0.0238355603069067, 0.0055870478972792625, -0.006754350382834673, -0.047143951058387756, -0.02515348233282566, -0.028918974101543427, -0.003332459833472967, -0.0006695514312013984, -0.021821022033691406, 0.044508107006549835, 0.012812084518373013, -0.020898478105664253, -0.026377268135547638, 0.009512572549283504, 0.0032406761310994625, 0.06510534882545471, 0.011964849196374416, -0.04386797547340393, 0.017349502071738243, -0.016088062897324562, 0.027921119704842567, 0.017575431615114212, -0.0023546088486909866, 0.0053752390667796135, 0.015269068069756031, -0.0031088837422430515, 0.02069137617945671, 0.011701264418661594, -0.0015344377607107162, 0.004928086884319782, -0.015513824298977852, -0.004615080542862415, 0.03306101635098457, -0.021651575341820717, 0.001244965591467917, -0.018620355054736137, -0.05972069501876831, -0.036129891872406006, 0.0162198543548584, 0.017528362572193146, -0.007709843572229147, -0.06924738734960556, 0.004918673075735569, 0.013339253142476082, -0.006872022058814764, -0.038125600665807724, 0.05256626009941101, 0.0005030461470596492, 0.026565542444586754, 0.019185177981853485, 0.024212108924984932, 0.012492017820477486, -0.012435534968972206, 0.04661678522825241, 0.041194476187229156, 0.009616123512387276, 0.02520996518433094, 0.04040372371673584, 0.053695905953645706, 0.015316136181354523, 0.0226306039839983, -0.003148892195895314, 0.006175405811518431, 0.019298143684864044, -0.023741424083709717, -0.051549576222896576, 0.048198290169239044, 0.017208294942975044, -0.016323404386639595, 0.08359391242265701, 0.004419745411723852, -0.003878456074744463, -0.020446619018912315, -0.0028217651415616274, -0.01843208074569702, 0.0003924347984138876, -0.014826622791588306, -0.0375419482588768, 0.002915902528911829, -0.021538611501455307, 0.07207150757312775, 0.021557439118623734, 0.016040993854403496, -0.021482128649950027, -0.0036101648584008217, 0.027789326384663582, 0.046277888119220734, 0.03870925307273865, 0.022969497367739677, -0.02129385434091091, 0.008335856720805168, 0.022310536354780197, 0.00725327804684639, 0.011296474374830723, -0.02735629491508007, -0.004648028407245874, 0.031291235238313675, 0.008001669310033321, -0.002285182708874345, 0.0022039893083274364, 0.013094495981931686, -0.021990470588207245, -0.03430362790822983, -0.024871069937944412, -0.016088062897324562, 0.023138944059610367, 0.034868448972702026, -0.015043138526380062, 0.020653720945119858, 0.03919876739382744, 0.024400385096669197, -0.05923118069767952, 0.049629177898168564, -0.040704961866140366, 0.03765491396188736, -0.013113323599100113, 0.017923738807439804, -0.04721926152706146, -0.01763191446661949, -0.027450433000922203, 0.00673081586137414, -0.024682795628905296, 0.0003853745001833886, 0.015419687144458294, -0.04789705201983452, -0.04017779231071472, 0.028768355026841164, -0.009234867990016937, 0.010025620460510254, 0.022969497367739677, 0.0325150191783905, 0.023684941232204437, -0.02758222445845604, -0.014591279439628124, -0.04740753769874573, 0.0036784145049750805, 0.02400500699877739, 0.04970448836684227, 0.02349666692316532, -0.041872262954711914, 0.034755486994981766, -0.05113537237048149, -0.005106947850435972, 0.0483112558722496, 0.001621514791622758, -0.029163731262087822, -0.056444715708494186, 0.01078813336789608, 0.008246426470577717, 0.03176192194223404, 0.01565503142774105, -0.0024028541520237923, -0.013282771222293377, -0.021011441946029663, -0.02157626673579216, 0.006307198200374842, 0.02906959317624569, -0.046541474759578705, 0.022498810663819313, 0.016982365399599075, 0.024758106097579002, 0.017490707337856293, -0.0046409680508077145, 0.028636561706662178, -0.008015790022909641, -0.011569472029805183, -0.14632700383663177, 0.026226647198200226, 0.000880771956872195, -0.00572354719042778, -0.02615133859217167, -0.040479034185409546, -0.039650626480579376, 0.006434283684939146, -0.03164895623922348, -0.04578837752342224, 0.006429576780647039, 0.01882745698094368, 0.08321736007928848, -0.03808794543147087, -0.0471062958240509, 0.047859396785497665, -0.04078027233481407, -0.007121485657989979, 0.025247620418667793, 0.007436845451593399, -0.012840325944125652, 0.006966159213334322, 0.03507555276155472, 0.013179220259189606, 0.0054223076440393925, -0.04529886320233345, -0.03516969084739685, -0.08856435865163803, -0.008175822906196117, -0.007578051649034023, 0.01092933863401413, -0.02697974629700184, 0.018874526023864746, 0.03149833530187607, -0.008651216514408588, -0.029973311349749565, -0.0008048737654462457, 0.032609157264232635, -0.0024381557013839483, -0.023214254528284073, 0.0062836636789143085, 0.010552790015935898, -0.011108200065791607, 0.024042662233114243, -0.022856533527374268, 0.018046118319034576, 0.04865014925599098, 0.027280984446406364, -0.030199240893125534, 0.03413417935371399, -0.025680651888251305, 0.07587464898824692, -0.0321572981774807, -0.012557913549244404, 0.01379111222922802, -0.019354626536369324, -0.04865014925599098, -0.003558389376848936, -0.009338418953120708, 0.02850477024912834, -0.0009819695260375738, -0.016295164823532104, -0.04398094117641449, -0.006293077487498522, -0.037184227257966995, 0.014854863286018372, -0.03319280594587326, 0.023967353627085686, -0.0366194024682045, 0.03313632309436798, -0.014374763704836369, 0.003805499756708741, -0.020936131477355957, -0.05968303978443146, -0.017151813954114914, -0.053206395357847214, 0.047972362488508224, -0.009333712048828602, 0.008044031448662281, -0.002661731792613864, -0.029634417966008186, -0.01450655609369278, -0.05015634745359421, 0.03677002340555191, 0.02944614365696907, -0.01948641799390316, -0.01571151241660118, 4.879694461124018e-05, -0.053695905953645706, 0.016201026737689972, -0.04085558280348778, -0.022611776366829872, -0.02229170873761177, -0.044997621327638626, -0.003951412625610828, -0.002338134916499257, -0.01988179422914982, -0.000973732559941709, -0.028278840705752373, -0.020936131477355957, -0.018808629363775253, 0.027375122532248497, 0.006340146064758301, 0.06081268563866615, -0.025944236665964127, 0.07598761469125748, 0.001330865896306932, 0.011607127264142036, 0.03842683881521225, 0.002751162275671959, 0.03620519861578941, -0.03236439824104309, 0.002094554714858532, -0.0202583447098732, -0.01991944946348667, -0.009206626564264297, -0.008844197727739811, 0.047972362488508224, 0.047859396785497665, 0.028580080717802048, 0.043190184980630875, 0.014657175168395042, 0.015061965212225914, -0.00905600655823946, -0.011993090622127056, 0.038125600665807724, 0.045600101351737976, -0.01508079282939434, -0.00035154391662217677, 0.025831270962953568, 0.04567541182041168, -0.049742139875888824, -0.029973311349749565, 0.014883104711771011, 0.01174833346158266, 0.05192612484097481, -0.03321163356304169, 0.03253384679555893, 0.019279316067695618, 0.006354266777634621, -0.00570942647755146, -0.010722236707806587, -0.0013614605413749814, 0.0028358856216073036, -0.022818878293037415, 0.01040217000991106, 0.007116778753697872, 0.01260498259216547, -0.0011937784729525447, -0.009818518534302711, 0.03390825167298317, 0.009686727076768875, 0.03050048090517521, 0.05181316286325455, -0.004918673075735569, 0.005563513375818729, -0.016954125836491585, -0.0014250031672418118, 0.01948641799390316, 0.06156578287482262, -0.007662774994969368, -0.021425645798444748, -0.039500005543231964, -0.004749225918203592, -0.019015731289982796, 0.06887083500623703, 0.009677313268184662, -0.02020186185836792, -0.011550645343959332, 0.05576692894101143, 0.05859104543924332, -0.02163274772465229, -0.027055056765675545, -0.02389204315841198, 0.03050048090517521, -0.022988324984908104, 0.011720092035830021, 0.0019615855999290943, 0.020183034241199493, -0.03667588531970978, 0.01668112725019455, -0.02200929820537567, -0.03601692616939545, 0.00771925738081336, -0.019015731289982796, -0.008792422711849213, -0.0008684164495207369, 0.04906435310840607, 0.043303150683641434, -0.036129891872406006, -0.03934938460588455, -0.013602837920188904, 0.03769256919622421, -0.017405984923243523, 0.07124309986829758, -0.013969972729682922, 0.013499286957085133, -0.005521151702851057, 0.009987966157495975, 0.02784580923616886, -0.05422307550907135, -0.0070650032721459866, 0.02658437006175518, -0.03398355841636658, -0.005224619526416063, -0.012670878320932388, -0.018535630777478218, -0.04006483033299446, -0.04898904263973236, -0.017180055379867554, 0.012200192548334599, -0.03385176882147789, 0.007610999513417482, 0.04770877584815025, 0.023251909762620926, -0.019580556079745293, 0.032778602093458176, -0.0034383642487227917, 0.01247319020330906, 0.0021180890034884214, 0.036242853850126266, -0.017161227762699127, -0.0047657000832259655, -0.07636416703462601, -0.01794256642460823, 0.008062858134508133, 0.005770615767687559, 0.010025620460510254, 0.011437680572271347, 0.023214254528284073, -0.00553056551143527, -0.026132510975003242, 0.03038751520216465, 0.04157102480530739, -0.00385962869040668, 0.019693519920110703, -0.024739278480410576, -0.0036901815328747034, -0.025473549962043762, -0.02564299665391445, 0.01633281819522381, -0.036958299577236176, 0.042361777275800705, -0.02961559034883976]', 'distance': 0.6708325147628784}, {'title': 'Investigating Symbolic Capabilities of Large Language Models', 'authors': ['Neisarg Dave', 'Daniel Kifer', 'C. Lee Giles', 'Ankur Mali'], 'summary': \"Prompting techniques have significantly enhanced the capabilities of Large\\nLanguage Models (LLMs) across various complex tasks, including reasoning,\\nplanning, and solving math word problems. However, most research has\\npredominantly focused on language-based reasoning and word problems, often\\noverlooking the potential of LLMs in handling symbol-based calculations and\\nreasoning. This study aims to bridge this gap by rigorously evaluating LLMs on\\na series of symbolic tasks, such as addition, multiplication, modulus\\narithmetic, numerical precision, and symbolic counting. Our analysis\\nencompasses eight LLMs, including four enterprise-grade and four open-source\\nmodels, of which three have been pre-trained on mathematical tasks. The\\nassessment framework is anchored in Chomsky's Hierarchy, providing a robust\\nmeasure of the computational abilities of these models. The evaluation employs\\nminimally explained prompts alongside the zero-shot Chain of Thoughts\\ntechnique, allowing models to navigate the solution process autonomously. The\\nfindings reveal a significant decline in LLMs' performance on context-free and\\ncontext-sensitive symbolic tasks as the complexity, represented by the number\\nof symbols, increases. Notably, even the fine-tuned GPT3.5 exhibits only\\nmarginal improvements, mirroring the performance trends observed in other\\nmodels. Across the board, all models demonstrated a limited generalization\\nability on these symbol-intensive tasks. This research underscores LLMs'\\nchallenges with increasing symbolic complexity and highlights the need for\\nspecialized training, memory and architectural adjustments to enhance their\\nproficiency in symbol-based reasoning tasks.\", 'published': '2024-05-21T21:24:34+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2405.13209v1', 'vector': '[-0.026966629549860954, -0.02274954691529274, -0.007720285095274448, -0.022428065538406372, -0.009516799822449684, -0.03214815631508827, 0.010003749281167984, -0.012065013870596886, -0.004429826512932777, -0.04530998691916466, -0.04334327578544617, -0.008892746642231941, -0.020347891375422478, 0.0018603381467983127, 0.0076399147510528564, -0.009450611658394337, -0.018296081572771072, -0.014844883233308792, -0.00933242030441761, -0.0035244778264313936, -0.02112322859466076, 0.022900832816958427, 0.025359220802783966, 0.026474950835108757, -0.030313819646835327, 0.0131240114569664, -0.041376568377017975, -0.04099835455417633, 0.0010117213241755962, 0.044856131076812744, 0.01487324945628643, -0.039334215223789215, 0.05733717978000641, -0.004850589204579592, -0.04822223260998726, -0.009086581878364086, 0.009025122039020061, -0.008032310754060745, -0.04198170825839043, 0.005252441391348839, -0.008665818721055984, -0.012358129024505615, 0.01120457798242569, -0.009067670442163944, -0.009611353278160095, -0.01978057064116001, -0.01406954601407051, -0.03424724191427231, -0.004789129365235567, -0.04712541401386261, -0.02394092082977295, -0.018749939277768135, 0.02651277184486389, -0.00041308015352115035, 0.01023067720234394, -0.014674687758088112, -0.06467452645301819, 0.00964917428791523, -0.08290442079305649, 0.002229096367955208, -0.009682267904281616, 0.023071028292179108, -0.029689766466617584, -0.0022574623581022024, -0.015478391200304031, 0.02165272831916809, -0.019336169585585594, 0.04897866025567055, -0.019213249906897545, -0.046898484230041504, -0.0125188697129488, 0.06652776896953583, -0.05121012032032013, -0.012953815050423145, -0.06773805618286133, 0.04931905120611191, -0.017123620957136154, -0.010429239831864834, -0.03929639235138893, 0.027458306401968002, -0.002836601808667183, -0.0007398803136311471, 0.030408373102545738, 0.017397824674844742, -0.010296865366399288, 0.0273448433727026, -0.017747672274708748, -0.0029997064266353846, 0.005597561132162809, -0.021425800397992134, -0.022938653826713562, 0.0032313624396920204, -0.0029737043660134077, -0.06701944768428802, -0.0398637130856514, 0.003186449408531189, -0.05230693891644478, 0.018286626785993576, -0.0049545979127287865, 0.02413002774119377, 0.002434750087559223, 0.00975791085511446, -0.022030942142009735, 0.0333017073571682, 0.0036379420198500156, -0.03144846111536026, 0.0065950993448495865, 0.013029458001255989, -0.008741461671888828, -0.015204186551272869, 0.007365709636360407, 0.0029831596184521914, 0.016130808740854263, 0.065620057284832, -0.00972008891403675, -0.019364535808563232, 0.01645229198038578, -0.014665232039988041, 0.03398248925805092, -0.01620645262300968, 0.021539263427257538, 0.037102751433849335, 0.08343391865491867, -0.03869124874472618, -0.015336561016738415, -0.003593028988689184, 0.013199654407799244, -0.004373094532638788, -0.031486280262470245, 0.008949479088187218, 0.001659412169829011, 0.020820658653974533, -0.02350597456097603, 0.012859262526035309, -0.03142954781651497, -0.0627078115940094, -0.03175102919340134, -0.02777978777885437, 0.011043837293982506, -0.03971242532134056, 0.026645148172974586, -0.001638137619011104, -0.03490911424160004, -0.05805578455328941, 0.019080875441432, -0.06996951252222061, 0.02394092082977295, 0.03884253650903702, -0.008949479088187218, -0.023543795570731163, 0.016594121232628822, 0.015809327363967896, 0.029273731634020805, 0.06312384456396103, 0.040014997124671936, -0.03381229564547539, -0.04020410403609276, 0.02651277184486389, -0.01363459974527359, 0.012452682480216026, -0.005427365191280842, 0.013379305601119995, -0.007956668734550476, -0.01300109177827835, 0.010192856192588806, -0.030030159279704094, -0.00941751804202795, 0.03782135993242264, -0.006155426148325205, -0.0386345200240612, 0.018551375716924667, -0.0017468740697950125, 0.013029458001255989, -0.01669812947511673, -0.023335779085755348, 0.003226634580641985, 0.05624036118388176, 0.034209419041872025, 0.05438711494207382, -0.022295691072940826, -0.026058916002511978, -0.012140655890107155, -0.012802530080080032, -0.005696841981261969, -0.0145801343023777, 0.01509072259068489, -0.012934904545545578, -0.05094537138938904, 0.017066888511180878, -0.03785917907953262, -0.0020364439114928246, -0.04549909383058548, 0.0260021835565567, 0.03952331840991974, 0.029103536158800125, -0.06758677214384079, 0.004458192735910416, -0.02420566976070404, -0.025415953248739243, -0.07159583270549774, -0.02838492952287197, 0.054765328764915466, -0.029292643070220947, -0.026815343648195267, -0.050151120871305466, -0.02250370942056179, -0.01951582171022892, -0.030446194112300873, 0.00549828028306365, -0.012953815050423145, 0.010221222415566444, 0.02505665086209774, 0.026040006428956985, 0.021369067952036858, 0.004869500175118446, -0.0046591185964643955, -0.018173163756728172, 0.06145970895886421, -0.0032077240757644176, -0.039069462567567825, -0.01128967572003603, 0.09765475243330002, 0.005460458807647228, 0.021104319021105766, -0.008169413544237614, 0.006254707463085651, 0.03715948387980461, 0.0024772989563643932, 0.013114556670188904, 0.010552159510552883, 0.00605141744017601, -0.01929834857583046, -0.019818391650915146, -0.007758106105029583, -0.0253970418125391, -0.02308993972837925, -0.03615722060203552, -0.001289471983909607, 0.011100568808615208, -0.016679219901561737, 0.002671133494004607, -0.022220049053430557, -0.01562967710196972, 0.009909195825457573, -0.035154953598976135, -0.02095303311944008, -0.0011115933302789927, 0.03519277274608612, 0.003361373208463192, -0.007204968947917223, 0.014391027390956879, -0.0002486458979547024, -0.04357020556926727, -0.022711725905537605, -0.003595392918214202, 0.007034773007035255, 0.042057350277900696, -0.02887660823762417, -0.05521918460726738, 0.007446079980581999, 0.0702342614531517, 0.02863076888024807, 0.047087591141462326, 0.022787367925047874, 0.018192073330283165, 0.011024925857782364, -0.04209517315030098, -0.016660308465361595, 0.004195807036012411, 0.07057465612888336, -0.01487324945628643, -0.037972643971443176, 0.046671558171510696, 4.4912863813806325e-05, -0.021425800397992134, 0.02420566976070404, -0.06138406693935394, -8.317744504893199e-05, 0.07318432629108429, 0.014807062223553658, -0.04160349443554878, 0.009587714448571205, 0.02717464603483677, -0.046482451260089874, 0.029595213010907173, -0.005257168784737587, -0.04209517315030098, -0.04795748367905617, -0.016754861921072006, -0.027042271569371223, 0.015194730833172798, 0.015790417790412903, 0.06894833594560623, -0.019723838195204735, -0.03152410313487053, 0.0030328002758324146, 0.00558337802067399, -0.16157284379005432, -0.019090330228209496, -0.05408454313874245, 0.01598897948861122, 0.013417127542197704, 0.0011175029212608933, -0.05442493408918381, 0.0253970418125391, -0.012755253352224827, 0.029595213010907173, 0.020593730732798576, -0.04065795987844467, 0.015752596780657768, -0.004912049043923616, -0.04629334434866905, 0.03602484613656998, -0.019090330228209496, 0.02248479798436165, -0.003999608568847179, -0.022541530430316925, 0.011734076775610447, -0.05998467653989792, 0.05052933469414711, 0.017000701278448105, -0.0020057139918208122, -0.017076343297958374, 0.020007498562335968, 0.031391728669404984, -0.012197388336062431, -0.003616667352616787, -0.042775958776474, 0.03653543442487717, 0.0013462039642035961, 0.02180401422083378, 0.017539655789732933, 0.04190606623888016, 0.029254822060465813, -0.011280220001935959, -0.02813909202814102, -0.02046135440468788, 0.016272639855742455, 0.04863826930522919, 0.02802562713623047, 0.03543861210346222, -0.02768523432314396, -0.03322606533765793, -0.0024891181383281946, -0.011223488487303257, 0.016178086400032043, -0.039069462567567825, -0.005673203617334366, -0.008661091327667236, -0.04258685186505318, -0.016414469107985497, -0.027912162244319916, -0.004207625985145569, -0.008391614072024822, -0.003609576029703021, 0.0031580834183841944, -0.011214032769203186, 0.020518086850643158, -0.021520353853702545, 0.0007215606165118515, -0.04977290704846382, -0.03589246794581413, 0.0027065910398960114, 0.06251870840787888, 0.010069936513900757, -0.0021321792155504227, -0.023638349026441574, 0.052874259650707245, -0.018967412412166595, 0.026720790192484856, -0.00851926114410162, 0.0384075902402401, 0.03903164342045784, -0.007337343879044056, -0.004439282231032848, -0.011440960690379143, -0.08260184526443481, -0.025945452973246574, -0.00543209258466959, -0.013322574086487293, 0.02921699918806553, -0.011790808290243149, -0.036421969532966614, 0.03188340738415718, 0.005034968256950378, 0.06709509342908859, 0.22223830223083496, 0.022276779636740685, -0.0020057139918208122, -0.050567157566547394, 0.01517582032829523, 0.013530591502785683, -0.0016369556542485952, 0.046898484230041504, 0.026285843923687935, -0.028952250257134438, 0.039598964154720306, 0.04394841939210892, 0.026909897103905678, 0.0432676337659359, -0.005361177492886782, 0.03948549926280975, -0.05208001285791397, 0.014523401856422424, 0.08940969407558441, -0.0036237589083611965, -0.00964917428791523, 0.00972008891403675, 0.03067312203347683, -0.016783228144049644, -0.031486280262470245, -0.017974600195884705, 0.0033495540264993906, 0.006027779076248407, 0.029254822060465813, 0.034058135002851486, -0.019553642719984055, 0.05071844160556793, 0.0410739965736866, 0.017898958176374435, 0.007398803252726793, -0.003420469118282199, -0.013530591502785683, -0.025415953248739243, 0.0142775634303689, 0.02583198808133602, -0.030219266191124916, -0.020423533394932747, -0.058169249445199966, 0.015043445862829685, 0.0038955998606979847, -0.04372148960828781, 0.02413002774119377, 0.029954515397548676, 0.0456882007420063, -0.022541530430316925, -0.003990153316408396, -0.029973426833748817, -0.0035575716756284237, -0.014703053049743176, 0.001895795576274395, 0.029122445732355118, -0.007209696341305971, 0.036762360483407974, -0.0070253172889351845, 0.018314993008971214, 0.013606233522295952, -0.005677931476384401, -0.021539263427257538, 0.0015211277641355991, 0.029292643070220947, 0.014428848400712013, -0.002609673887491226, -0.018920134752988815, 0.03016253374516964, 0.03282893821597099, 0.06017378345131874, 0.051512692123651505, 0.030049068853259087, -0.0202911589294672, 0.02787434123456478, -0.05983338877558708, 0.0583205372095108, 0.047844018787145615, 0.020669372752308846, 0.008732005953788757, -0.009507344104349613, 0.004420371260493994, -0.006703835912048817, -0.028592947870492935, 0.013644055463373661, -0.011440960690379143, 0.010296865366399288, 0.0033566455822438, -0.012348673306405544, -0.0060419621877372265, -0.016726495698094368, -0.018333904445171356, -0.010410329326987267, -0.018258260563015938, -0.02666405774652958, -0.004531471524387598, 0.03458763286471367, -0.05506789684295654, -0.05102101340889931, -0.012291941791772842, 0.037424236536026, -0.001894613727927208, 0.02097194269299507, -0.0028176913037896156, 0.037878092378377914, -0.026361487805843353, -0.03313151001930237, 0.02991669438779354, 0.02189856767654419, 0.06845665723085403, -0.04345674067735672, 0.006363443564623594, 0.027117913588881493, -0.0260021835565567, 0.02547268569469452, 0.03526841849088669, -0.013814250938594341, 0.019071420654654503, 0.030975693836808205, 0.01601734571158886, -0.006434358656406403, 0.06353988498449326, -0.06864576786756516, 0.025264667347073555, 0.006623465567827225, -0.016272639855742455, 0.03057856857776642, 0.021936388686299324, -0.05139922723174095, 0.010514337569475174, 0.022182226181030273, 0.012178477831184864, 0.028271466493606567, 0.023146672174334526, 0.029273731634020805, -0.005153160076588392, 0.031145889312028885, -0.007578454911708832, 0.004046885296702385, 0.035154953598976135, -0.05624036118388176, 0.013388761319220066, 0.0032833667937666178, 0.0001644342701183632, 0.02853621542453766, 0.032072514295578, -0.046482451260089874, 0.0004432190617080778, -0.007687191013246775, 0.029292643070220947, -0.023978741839528084, 0.01730327121913433, 0.009086581878364086, -0.03246963769197464, -0.008509805426001549, -0.061043672263622284, -0.021841835230588913, -0.006609282456338406, 0.0017539655091241002, -0.04720105603337288, -0.014022269286215305, 0.046142056584358215, 0.0066896528005599976, 0.024054383859038353, 0.007271156180649996, 0.01686832681298256, 0.014220830984413624, 0.03167538717389107, -0.017246540635824203, -0.02583198808133602, -0.025775255635380745, -0.01995076611638069, -0.012178477831184864, 0.05378197133541107, 0.0021321792155504227, -0.01232030801475048, -0.025245757773518562, 0.030465105548501015, -0.047692734748125076, 0.01363459974527359, 0.01722762919962406, -0.029330464079976082, -0.04905430227518082, 0.06475016474723816, 0.0037774082738906145, -0.006623465567827225, -0.017927324399352074, -0.010003749281167984, -0.0176247525960207, -0.03944767639040947, -0.03120262175798416, 0.1232219859957695, -0.017076343297958374, -0.004096525721251965, 0.022938653826713562, -0.006940219551324844, 0.061043672263622284, -0.008386886678636074, 0.05937953293323517, -0.03237508237361908, -0.010287409648299217, -0.027042271569371223, 0.005096428096294403, 0.018390635028481483, -0.03439852595329285, 0.010495427064597607, -0.020423533394932747, -0.03375556319952011, -0.019194340333342552, -0.037707895040512085, -0.019014688208699226, -0.03042728267610073, -0.04364584758877754, -0.0008267512894235551, -0.02257935144007206, -0.01406954601407051, -0.011838085018098354, 0.06614955514669418, 0.020347891375422478, 0.03895600140094757, 0.06111931800842285, -0.020139873027801514, 0.001270561246201396, -0.04073360189795494, -0.02189856767654419, 0.023808546364307404, 0.004912049043923616, 0.019260527566075325, 0.021369067952036858, 0.0410739965736866, -0.001915888162329793, -0.02163381688296795, 0.021539263427257538, 0.012764709070324898, 0.010344142094254494, 0.019723838195204735, 0.0020269884262233973, -8.130299647746142e-06, -0.04897866025567055, -0.018712118268013, -0.017284361645579338, -0.012859262526035309, -0.0032573645003139973, 0.03628959506750107, -0.027080092579126358, -0.012509414926171303, -0.02163381688296795, 0.022617172449827194, 0.021425800397992134, 0.05136140435934067, -0.01632937230169773, -0.019459089264273643, -0.00722387945279479, -0.019553642719984055, 0.01209337916225195, -0.014986713416874409, -0.00832542683929205, 0.008486167527735233, -0.02726919949054718, 0.01784222573041916, 0.014627411030232906, -0.0009177589090541005, -0.00519570941105485, -0.012566146440804005, -0.03993935510516167, -0.04807094857096672, 0.004120164085179567, -0.012074468657374382, -0.01200828142464161, -0.03154301270842552, -0.011242398992180824, 0.019061964005231857, 0.01649956777691841, -0.06838101893663406, 0.05608907714486122, -0.006316166836768389, -0.0013911168789491057, -0.010675078257918358, 0.008429435081779957, 0.004103617276996374, -0.05325247347354889, 0.03146737068891525, 0.029878873378038406, 0.0010483608348295093, 0.0022881922777742147, -0.030559659004211426, 0.04175478219985962, -0.01716144196689129, 0.027288110926747322, -0.01116675604134798, 0.028914429247379303, 0.01005102600902319, -0.014031724072992802, -0.0003788045432884246, -0.0014691234100610018, 0.02974649891257286, -0.0031675389036536217, 0.04527216777205467, -0.007635186892002821, -0.006089238915592432, -0.03678127005696297, -0.0003161629138048738, -0.013530591502785683, 0.008041766472160816, -0.046066414564847946, 0.002815327374264598, -0.013398216105997562, -0.003550480119884014, 0.02802562713623047, 0.03577900677919388, -0.02889551781117916, -0.008802921511232853, -0.021369067952036858, 0.029519570991396904, 0.0038790530525147915, 0.04780619591474533, 0.022049851715564728, -0.01363459974527359, 0.009776821359992027, -0.0048340423963963985, 0.023922009393572807, 0.01648065634071827, -0.04977290704846382, -0.021539263427257538, 0.0016180450329557061, 0.02863076888024807, -0.008150503039360046, 0.0033448264002799988, -0.013785885646939278, -0.04481831192970276, -0.03774571791291237, -0.052533868700265884, 0.002125087659806013, -0.02046135440468788, 0.008561810478568077, -0.015563488937914371, 0.027723055332899094, 0.007020589895546436, 0.014428848400712013, -0.008401069790124893, 0.07469718158245087, -0.07492411136627197, 0.03545752540230751, -0.03630850464105606, 0.027080092579126358, 0.006935491692274809, -0.0062594348564744, -0.009280416183173656, 0.010826364159584045, -0.02428131178021431, 0.02853621542453766, -0.007129325997084379, -0.04561255872249603, -0.03579791635274887, 0.0015494937542825937, -0.019629284739494324, 0.013899349607527256, 0.06981822848320007, 0.030313819646835327, 0.012707976624369621, -0.0023130124900490046, -0.0061223325319588184, -0.008386886678636074, -0.03093787096440792, -0.009578258730471134, 0.011942094191908836, 0.012112290598452091, -0.03398248925805092, 0.0019844393245875835, -0.05170179903507233, -0.005214619915932417, 0.03194013610482216, -0.05170179903507233, 0.0008746189414523542, -0.01669812947511673, 0.013785885646939278, 0.008987300097942352, 0.00862799771130085, 0.042813777923583984, -0.02420566976070404, -0.0037750443443655968, -0.020858479663729668, 0.010069936513900757, -0.027552859857678413, 0.019496910274028778, -0.022352423518896103, -0.015431114472448826, -0.0035173865035176277, 0.02702336013317108, 0.0291602686047554, -0.0056117442436516285, 0.008339609950780869, -0.031032424420118332, -0.0015920428559184074, -0.14296473562717438, -0.002635675948113203, 0.01891067996621132, 0.029500659555196762, -0.02095303311944008, -0.014012813568115234, -0.007379892747849226, -0.0032124517019838095, -0.02464061602950096, -0.047427985817193985, 0.041792601346969604, 0.022352423518896103, 0.054916612803936005, -0.04833569750189781, -0.022825190797448158, 0.028271466493606567, -0.06437195092439651, -0.009786277078092098, 0.01874048262834549, 0.02829037606716156, 0.00456220144405961, -0.00014183009625412524, 0.03188340738415718, 0.03519277274608612, 0.0002415543858660385, -0.007621003780514002, -0.002386291278526187, -0.03371774032711983, -0.02299538627266884, -0.0036001205444335938, 0.005493552424013615, -0.07110415399074554, 0.02532139979302883, 0.03462545573711395, 0.018683752045035362, -0.018816126510500908, 0.006462724879384041, -0.011847540736198425, -0.03971242532134056, 0.0007948395214043558, -0.015364927239716053, 0.0007191968034021556, -0.02785543166100979, 0.010485971346497536, -0.06229177862405777, 0.06648994982242584, 0.007412986364215612, 0.011403139680624008, -0.04319199174642563, -0.001893431763164699, -0.012112290598452091, 0.03585464879870415, -0.026947718113660812, 0.0172559954226017, 0.00574884656816721, 0.00016000207688193768, -0.026437129825353622, -0.010334686376154423, -0.0019111605361104012, 0.07624785602092743, -0.024564972147345543, 0.03226161748170853, -0.008197779767215252, -0.00554082915186882, 0.01478815171867609, -0.011771897785365582, -0.03551425784826279, 0.0010117213241755962, -0.03494693711400032, 0.04576384276151657, 0.0004591749457176775, -0.006065600551664829, -0.01820152811706066, -0.04595294967293739, -0.0032124517019838095, -0.018192073330283165, 0.017615297809243202, -0.000195016385987401, -0.009852464310824871, 0.004044521600008011, -0.0090534882619977, 0.014797606505453587, -0.03381229564547539, 0.0023153764195740223, 0.04285160079598427, -0.059682104736566544, -0.013180743902921677, 0.026985539123415947, -0.0891827642917633, -4.916037869406864e-05, -0.021747281774878502, -0.030710943043231964, -0.0027018634136766195, -0.03789700195193291, -0.002323649823665619, 0.020045319572091103, -0.06739766150712967, 0.015194730833172798, -0.01351168006658554, -0.002456024521961808, -0.024224581196904182, 0.05627818405628204, -0.011393683962523937, 0.01477869600057602, 0.009010938927531242, 0.07492411136627197, 0.009285143576562405, -0.017927324399352074, 0.027817608788609505, 0.019383447244763374, -0.02982214093208313, -0.0241867583245039, 0.0393720343708992, 0.033774472773075104, -0.07004515826702118, 0.0014868521830067039, 0.04319199174642563, 0.05506789684295654, 0.016726495698094368, 0.01759638637304306, 0.032810028642416, -0.011119479313492775, -0.005852855276316404, -0.0285551268607378, 0.0003598938637878746, 0.1013612449169159, 0.033358439803123474, 0.00390032771974802, -0.008547627367079258, -0.029330464079976082, 0.029273731634020805, -0.02310885116457939, -0.027042271569371223, 0.04016628488898277, 0.00574884656816721, -0.01759638637304306, 0.006443813908845186, 0.04096053168177605, -0.024735169485211372, -0.0066471039317548275, -0.045801665633916855, 0.0023898370563983917, -0.01098710484802723, -0.018768848851323128, -0.025340311229228973, -0.03627068176865578, -0.02566179260611534, 0.01108165830373764, 0.0052098920568823814, -0.04440227523446083, 0.018627019599080086, 0.011195122264325619, 0.04183042421936989, 0.043078526854515076, 0.025945452973246574, 0.0291602686047554, -0.06217831373214722, 0.004032702185213566, -0.0016109534772112966, 0.07065030187368393, 0.017407281324267387, -0.008883291855454445, -0.042889419943094254, -0.011989370919764042, -0.01985621266067028, 0.057034607976675034, 0.009190590120851994, 0.004930959548801184, -0.027836520224809647, 0.02505665086209774, -0.0005995276151224971, 0.004202898591756821, -0.04345674067735672, -0.0007115143234841526, 0.014164098538458347, -0.03623286262154579, 0.0134265823289752, 0.039750248193740845, 0.03536297008395195, -0.02982214093208313, 0.032677654176950455, -0.004470011685043573, -0.006746384780853987, 0.029538480564951897, 0.026626236736774445, -0.01035359688103199, -0.017000701278448105, 0.04633116349577904, 0.0473901629447937, -0.026909897103905678, -0.004845861811190844, -0.029633034020662308, 0.03220488876104355, 0.006325622089207172, 0.0330558679997921, -0.03343408182263374, 0.047163233160972595, 0.03059748001396656, -0.012046102434396744, 0.05869875103235245, -0.06142188608646393, 0.016055166721343994, 0.0009928107028827071, 0.00743189686909318, -0.008680001832544804, -0.023732902482151985, -0.036100488156080246, -0.07223879545927048, -0.015667498111724854, -0.011838085018098354, 0.0007853841525502503, -0.020745014771819115, 0.0008208416984416544, 0.010429239831864834, 0.0439862422645092, -0.01150714885443449, 0.040847066789865494, 0.0207828376442194, 0.05238258093595505, 0.02394092082977295, 0.025548327714204788, -0.0010690443450585008, 0.051588334143161774, -0.05113447830080986, -0.06554441154003143, 5.765541209257208e-05, 0.02369508147239685, -0.00289569771848619, 0.01269852090626955, 0.02974649891257286, 0.045536916702985764, -0.02864968031644821, -0.013379305601119995, 0.029973426833748817, -0.0247919000685215, 0.03674345090985298, -0.04330545663833618, 0.004694576375186443, -0.03691364824771881, -0.044515740126371384, 0.03545752540230751, -0.01026849914342165, 0.07965178042650223, 0.016877781599760056]', 'distance': 0.671021580696106}, {'title': 'Aligning Large Language Models for Controllable Recommendations', 'authors': ['Wensheng Lu', 'Jianxun Lian', 'Wei Zhang', 'Guanghua Li', 'Mingyang Zhou', 'Hao Liao', 'Xing Xie'], 'summary': \"Inspired by the exceptional general intelligence of Large Language Models\\n(LLMs), researchers have begun to explore their application in pioneering the\\nnext generation of recommender systems - systems that are conversational,\\nexplainable, and controllable. However, existing literature primarily\\nconcentrates on integrating domain-specific knowledge into LLMs to enhance\\naccuracy, often neglecting the ability to follow instructions. To address this\\ngap, we initially introduce a collection of supervised learning tasks,\\naugmented with labels derived from a conventional recommender model, aimed at\\nexplicitly improving LLMs' proficiency in adhering to recommendation-specific\\ninstructions. Subsequently, we develop a reinforcement learning-based alignment\\nprocedure to further strengthen LLMs' aptitude in responding to users'\\nintentions and mitigating formatting errors. Through extensive experiments on\\ntwo real-world datasets, our method markedly advances the capability of LLMs to\\ncomply with instructions within recommender systems, while sustaining a high\\nlevel of accuracy performance.\", 'published': '2024-03-08T05:23:27+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2403.05063v2', 'vector': '[-0.030107101425528526, -0.009808904491364956, -0.012171357870101929, 0.0015485879266634583, -0.030919784680008888, -0.02532549574971199, 0.027650149539113045, -0.03519110009074211, -0.015904033556580544, -0.014420412480831146, -0.03201596066355705, -0.020487191155552864, -0.012974591925740242, 0.007574024144560099, -0.030239397659897804, 0.014675557613372803, -0.009856154210865498, -0.001527325832284987, -0.018946873024106026, -0.03753465414047241, 0.005083998665213585, 0.010092399083077908, 0.04528350010514259, -0.0031727743335068226, 0.0009107256191782653, 0.012332004494965076, -0.021810166537761688, -0.013409283012151718, 0.0038956848438829184, 0.027574550360441208, 0.01563943922519684, -0.04237295687198639, 0.045510292053222656, 0.012851743958890438, 0.011831164360046387, -0.022906344383955002, 0.015960732474923134, -0.012048509903252125, -0.02638387493789196, 0.023133140057325363, -0.02156447060406208, -0.006945611909031868, 0.011925662867724895, -0.010404243133962154, -0.003472805954515934, -0.02284964546561241, -0.010687736794352531, -0.022188158705830574, -0.002127388957887888, -0.005740760825574398, 0.0031160754151642323, -0.029634609818458557, 0.05046199634671211, 0.0034869806841015816, -0.00589195778593421, 0.031014282256364822, -0.046152882277965546, -0.001279268297366798, -0.04195716604590416, 0.017434902489185333, -0.013069089502096176, -0.013106889091432095, -0.03927341848611832, -0.021318774670362473, -0.004082318861037493, 0.019126418977975845, -0.0008191805682145059, 0.03789374604821205, -0.019674507901072502, -0.06395632773637772, 0.024456113576889038, 0.03783704712986946, -0.02808484062552452, 0.0015166947850957513, -0.06497690826654434, 0.04203276336193085, -0.02649727277457714, -0.016612770035862923, -0.019523311406373978, 0.07136497646570206, -0.02950231172144413, -0.007588198874145746, 0.009147417731583118, -0.004861928056925535, -0.01133977435529232, -0.02409701980650425, 0.005570664070546627, -0.0018722439417615533, 0.027895845472812653, 0.005977006163448095, 0.002164006931707263, 0.01528034545481205, 0.03260185196995735, -0.05390172824263573, -0.005433641839772463, -0.006109303329139948, -0.035758089274168015, 0.013182487338781357, -0.010253045707941055, 0.01593238301575184, -0.014184167608618736, 0.03972700983285904, -0.0029318041633814573, 0.028595130890607834, 0.017888493835926056, -0.0399538055062294, -0.01786014437675476, 0.03271524980664253, 0.0252687968313694, -0.0012143008643761277, -0.005358043126761913, 0.04354473203420639, 0.021129779517650604, 0.03460521250963211, -0.0026081481482833624, -0.018351534381508827, 0.020071400329470634, -0.03220495954155922, 0.06312474608421326, -0.000569941767025739, 0.00884974841028452, 0.01798299141228199, 0.05201176553964615, -0.02902982197701931, -0.034057121723890305, -0.03178916871547699, -0.006737716030329466, 0.002228974364697933, -0.013012390583753586, 0.005802184343338013, 0.01816253922879696, 0.0698908120393753, -0.03377362713217735, 0.011982361786067486, -0.009100168943405151, -0.05598068609833717, 0.01061213854700327, -0.03804494068026543, 0.00383426109328866, -0.0008498924435116351, 0.04097438231110573, 0.02162116952240467, -0.041805967688560486, -0.02596808224916458, 0.03539899364113808, -0.06490130722522736, 0.01419361773878336, 0.036230579018592834, -0.010876733809709549, -0.04619067907333374, 0.0029010921716690063, 0.015459892340004444, -0.006265225354582071, 0.06486351042985916, 0.037345655262470245, -0.03279084712266922, -0.024834105744957924, 0.03900882229208946, 0.009586834348738194, 0.034416213631629944, 0.02156447060406208, 0.03950021415948868, -0.007403927389532328, 0.0417303703725338, 0.03271524980664253, 0.007399202790111303, 0.027593450620770454, 0.078773632645607, -0.01897522248327732, -0.06081898882985115, -0.0027876945678144693, -0.031052080914378166, 0.0023943460546433926, -0.009109619073569775, 0.01533704437315464, -0.002414426999166608, 0.07469131052494049, 0.012464301660656929, 0.010196346789598465, 0.0032200233545154333, 0.009360038675367832, -0.03502100333571434, 0.0017871956806629896, -0.008934796787798405, -0.022963043302297592, 0.007720496505498886, -0.012180807068943977, -0.06554389744997025, 0.029483413323760033, -0.011481521651148796, 0.010886183008551598, -0.04029399901628494, 0.03713776171207428, 0.020052500069141388, 0.026516171172261238, -0.07892482727766037, -0.016423773020505905, -0.016830114647746086, -0.03212935850024223, -0.056736670434474945, -0.004807591903954744, 0.049743808805942535, -0.022585051134228706, -3.2631382055114955e-05, -0.04596388339996338, 0.0011570113711059093, -0.022358255460858345, -0.029483413323760033, 0.023548930883407593, 0.0059439316391944885, 0.0025703487917780876, 0.0346808098256588, -0.004122480284422636, 0.025306595489382744, -0.0033594081178307533, -0.003227110719308257, -0.018257036805152893, 0.03589038550853729, 0.0007193669443950057, -0.0411255806684494, -0.05530029907822609, 0.0834607407450676, -0.007630723062902689, 0.03337673470377922, 0.027763547375798225, -0.0020033600740134716, 0.052162960171699524, 0.008141012862324715, -0.013891223818063736, -0.007068459410220385, 0.033887024968862534, 0.008126838132739067, -0.020487191155552864, -0.00589195778593421, -0.044111721217632294, -0.031940363347530365, -0.009412012994289398, 0.011160227470099926, 0.018663378432393074, 0.022225957363843918, -0.04853423312306404, -0.05261655151844025, 0.011264176107943058, -0.013258085586130619, -0.056434277445077896, 0.017642797902226448, -0.017368754372000694, 0.022585051134228706, -0.029464513063430786, 0.0018344447016716003, -0.008764700032770634, 0.011387023143470287, -0.057568252086639404, -0.02404032088816166, 0.005740760825574398, 0.009884502738714218, 0.038744229823350906, -0.018946873024106026, -0.03430281579494476, -0.014439312741160393, 0.01887127384543419, 0.00752205029129982, -0.001848619431257248, 0.005674612242728472, 0.04513230174779892, 0.014269215986132622, 0.027007563039660454, -0.041163381189107895, -0.009034019894897938, 0.02022259682416916, -0.009856154210865498, -0.061726171523332596, 0.020732887089252472, -0.009345863945782185, 0.0033594081178307533, 0.033641330897808075, -0.056207481771707535, -0.02192356437444687, 0.08013440668582916, -0.012048509903252125, -0.012360353954136372, 0.005830533802509308, 0.02638387493789196, -0.0528811477124691, 0.033811427652835846, -0.01622532680630684, -0.05261655151844025, -0.014770056121051311, -0.014259765855967999, -0.030598491430282593, 0.031694669276475906, -0.026251576840877533, 0.06947501748800278, -0.019409913569688797, -0.04078538715839386, -0.007829168811440468, -0.008703276515007019, -0.16132718324661255, -0.004382350016385317, -0.034113820642232895, 0.0015485879266634583, 0.008386707864701748, 0.02039269357919693, -0.01675451546907425, -0.010810584761202335, -0.014968502335250378, 0.010385342873632908, 0.007956741377711296, -0.05722806230187416, 0.01838933303952217, -0.0017954641953110695, -0.026346076279878616, 0.022755146026611328, -0.008117388002574444, 0.025665689259767532, -0.019013021141290665, -0.021356575191020966, -0.019901303574442863, -0.07472911477088928, 0.05458211153745651, -0.03418941795825958, -0.010886183008551598, 0.025079799816012383, 0.01903192140161991, 0.014278666116297245, 0.00340901967138052, -0.009364763274788857, 0.006789689883589745, 0.040520794689655304, 0.0013087989063933492, 0.00020346626115497202, 0.009605733677744865, 0.02451281249523163, 0.014911803416907787, 0.008604053407907486, -0.024966401979327202, 0.0012910804944112897, 0.047097861766815186, 0.03228055685758591, 0.016830114647746086, 0.04384712874889374, -0.03426501899957657, -0.03031499683856964, 0.013182487338781357, 0.005783285014331341, 0.015526040457189083, -0.004682381637394428, -0.006435321643948555, -0.0016135553596541286, -0.03078748658299446, 0.005414742045104504, -0.06282234936952591, -0.00937421340495348, -0.01016799733042717, -0.014770056121051311, 0.012785595841705799, -0.009350588545203209, -0.022963043302297592, -0.045926086604595184, -0.01061213854700327, -0.0481940396130085, -0.03073078766465187, -0.019580010324716568, 0.08716506510972977, 0.007408652454614639, 0.019353214651346207, -0.0505375936627388, 0.05099118500947952, -0.011065729893743992, 0.03303654119372368, -0.024342715740203857, 0.05322134122252464, 0.0699286088347435, 0.004618595354259014, -0.01380617544054985, -0.012228056788444519, -0.05469550937414169, -0.019428813830018044, 0.036230579018592834, -0.006822763942182064, 0.006718816235661507, -0.013144688680768013, -0.031070981174707413, 0.045510292053222656, 0.0035035177133977413, 0.07707266509532928, 0.2280050665140152, 0.0422973595559597, -0.002801869297400117, -0.01886182464659214, 0.0019407550571486354, 0.004240603186190128, 0.01815308816730976, 0.0029010921716690063, 0.023303236812353134, -0.005622637923806906, 0.05688786879181862, 0.06021420285105705, 0.00796619150787592, 0.02696976251900196, -0.029766907915472984, 0.03689206391572952, -0.07779084891080856, 0.004876102786511183, 0.05794624611735344, 0.00031302502611652017, 0.0036736144684255123, -0.019958002492785454, 0.033414535224437714, -0.02443721331655979, -0.04660647362470627, -0.004439048934727907, 0.004821766633540392, -0.022055860608816147, 0.04006720334291458, 0.028065940365195274, -0.03655187040567398, 0.030163798481225967, 0.065846286714077, 0.003264910075813532, 0.015384294092655182, -0.010744435712695122, -0.022074760869145393, -0.03197816386818886, 0.0007488976116292179, 0.05431751906871796, -0.016178077086806297, -0.012974591925740242, -0.0032814471051096916, 0.022018061950802803, 0.008060689084231853, -0.033943723887205124, 0.020071400329470634, 0.02632717601954937, 0.02691306360065937, -0.009506510570645332, 0.010177447460591793, -0.03538009524345398, -0.006057329475879669, 0.0019714669324457645, 0.002593973418697715, 0.026988662779331207, -0.0021380200050771236, 0.04070978984236717, 0.022358255460858345, 0.03139227628707886, 0.07454011589288712, -0.0029601536225527525, -0.0008924166322685778, 0.036646369844675064, -0.010035700164735317, 0.015818985179066658, 0.01745380274951458, -0.02668626792728901, 0.02462621033191681, 0.017132509499788284, 0.05824863910675049, 0.04089878499507904, 0.020770685747265816, -0.00299086538143456, 0.03961361199617386, -0.019079169258475304, 0.04588828608393669, 0.060440994799137115, -0.0045122853480279446, 0.010734986513853073, -0.030579591169953346, 0.0005265317158773541, 0.01992020383477211, -0.02732885628938675, 0.005410017445683479, -0.017548300325870514, 0.008835573680698872, 0.0576060526072979, -0.0118973134085536, 0.03297984227538109, -0.026421673595905304, -0.012766695581376553, 0.00012616974709089845, -0.01909806951880455, -0.02515539899468422, 0.015242546796798706, -0.004025619942694902, -0.048987824469804764, -0.0464174747467041, 0.008122113533318043, -0.0027971442323178053, -0.0018734252080321312, 0.019882403314113617, 0.004994225688278675, 0.03602268174290657, -0.009922302328050137, -0.027291055768728256, 0.009638807736337185, -0.007214931305497885, 0.06977741420269012, -0.020430494099855423, 0.007347228936851025, -0.02080848626792431, -0.062444355338811874, 0.029937004670500755, 0.054808907210826874, -0.009903402999043465, 0.03383032605051994, 0.03209156170487404, -0.029540112242102623, 0.008618228137493134, 0.09903402626514435, -0.044640909880399704, 0.031826965510845184, -0.018143638968467712, -0.04849643260240555, 0.0417303703725338, 0.013324234634637833, -0.025514492765069008, -3.635962639236823e-05, 0.028009241446852684, 0.005060374271124601, 0.03037169575691223, 0.04426291957497597, 0.02215035818517208, -0.018833475187420845, -0.010829484090209007, -0.002799506764858961, -0.014089669100940228, 0.03624947741627693, -0.067018061876297, -0.0023978897370398045, -0.024456113576889038, 0.004354001022875309, 0.02543889358639717, 0.004824128933250904, -0.015894584357738495, -0.0003425556933507323, -0.0199769027531147, 0.04399832338094711, -0.0032530976459383965, 0.013853424228727818, 0.03343343362212181, -0.03613607957959175, -0.029596811160445213, -0.07900042831897736, -0.026175979524850845, -0.04301554337143898, 0.0364195741713047, -0.0434691347181797, -0.018757876008749008, 0.05488450825214386, -0.020071400329470634, 0.022244857624173164, -0.01022469624876976, 0.03367912769317627, -0.01196346152573824, 0.0658084899187088, -0.022887444123625755, -0.028632929548621178, -0.006019530352205038, 0.007838618941605091, -0.020487191155552864, 0.025401094928383827, -0.027177657932043076, 0.03273414820432663, -0.05635867640376091, 0.0025396370328962803, -0.02679966576397419, -0.04955481365323067, 0.001280449447222054, -0.002312841359525919, -0.029181018471717834, 0.015204747207462788, 0.0028845551423728466, -0.005887232720851898, -0.02498530223965645, -0.014288115315139294, 0.02415371872484684, -0.00813628826290369, -0.042864345014095306, 0.08897943049669266, -0.05178496986627579, 0.006336099002510309, 0.013617178425192833, 0.008533179759979248, 0.06165057420730591, -0.004708368796855211, 0.02562789060175419, -0.05847543478012085, -0.03037169575691223, -0.006487295962870121, -0.011254725977778435, -0.0017718396848067641, -0.021772366017103195, 0.0039665587246418, -0.04021839797496796, 0.022944143041968346, -0.016782864928245544, -0.03672196716070175, -0.015620538964867592, -0.005282444879412651, -0.10311634838581085, 0.003021577373147011, -0.007843343541026115, -0.026232678443193436, -0.014552710577845573, 0.044640909880399704, -0.02755565196275711, 0.014288115315139294, 0.002662484534084797, -0.011330324225127697, -0.003477530786767602, -0.038177240639925, -0.04044519364833832, 0.02555229142308235, 0.009567934088408947, 0.02080848626792431, -0.014184167608618736, 0.016310375183820724, -0.005154872313141823, -0.04089878499507904, -0.0028278562240302563, 0.011585469357669353, 0.009345863945782185, 0.03900882229208946, 0.017661698162555695, 0.021356575191020966, -0.04978160932660103, -0.015025201253592968, -0.011916212737560272, 0.025344396010041237, -0.01116967760026455, 0.020260397344827652, -0.009005670435726643, -0.05087778717279434, -0.0276123508810997, -0.01680176518857479, 0.024304915219545364, 0.008273310028016567, 0.035002101212739944, -0.027120960876345634, 0.01744435355067253, -0.014864553697407246, 0.03127887845039368, -0.039046622812747955, 0.0018001891439780593, -0.016253676265478134, -0.008074863813817501, -0.008396157994866371, -0.008509555831551552, 0.004956426098942757, -0.010536540299654007, -0.02010919898748398, -0.05083998665213585, -0.04895002394914627, 0.029596811160445213, 0.017302606254816055, -0.00813628826290369, -0.08096598833799362, -0.014344814233481884, 0.020468292757868767, 0.008556804619729519, -0.06236875802278519, 0.05613188073039055, -0.02267954871058464, -0.010290845297276974, 0.007356678601354361, -0.01862557977437973, -0.019958002492785454, -0.06248215585947037, 0.028897523880004883, -0.02443721331655979, 0.008448131382465363, -0.0376291498541832, 0.014533810317516327, 0.06357833743095398, -0.022414954379200935, 0.033168841153383255, -0.04494330659508705, 0.07438892126083374, 0.011056279763579369, -0.04653087258338928, -0.00416972953826189, 0.009856154210865498, -0.006354998331516981, -0.039878204464912415, 0.07854683697223663, -0.011198027059435844, -0.009846704080700874, -0.01638597436249256, 0.015015751123428345, -0.015261446125805378, 0.006633767858147621, -0.007474801037460566, -0.008589878678321838, -0.03197816386818886, -0.003794099437072873, 0.07370853424072266, 0.01489290315657854, -0.01869172789156437, -0.03830953687429428, 0.019769005477428436, 0.04959261417388916, -0.044640909880399704, 0.04630407691001892, 0.03222385793924332, -0.024890804663300514, 0.005906132515519857, 0.012908442877233028, 0.034113820642232895, 0.014845654368400574, -0.02914321981370449, 0.005750210490077734, -0.03139227628707886, -0.004998950287699699, -0.008736351504921913, 0.009893952868878841, 0.010451491922140121, -0.003198761260136962, -0.031108779832720757, -0.0523141585290432, -0.011594919487833977, -0.011245275847613811, 0.005135972518473864, -0.03677866607904434, 0.023341035470366478, 0.0130785396322608, 0.030069300904870033, 0.002516012405976653, 0.05393952503800392, -0.02885972522199154, 0.029577910900115967, -0.02774464711546898, -0.008967871777713299, -0.001140474108979106, -0.004866653122007847, -0.006090403534471989, 0.0041130306199193, -0.030296096578240395, 0.012360353954136372, -0.005424192175269127, -0.013532130979001522, -0.04717345908284187, 0.02426711656153202, -0.02003360167145729, -0.020657287910580635, 0.0452456995844841, 0.005627362988889217, 0.028236037120223045, -0.03286644443869591, -0.004904452245682478, -0.03744015470147133, -0.013333684764802456, 0.0340949222445488, 0.026119280606508255, 0.009034019894897938, -0.08141957968473434, -0.02509870007634163, -0.05805964395403862, -0.009029295295476913, 0.02128097601234913, -0.017529400065541267, -0.0012650935677811503, -0.03214826062321663, 0.01922091655433178, -0.028632929548621178, 0.024475011974573135, 0.01950441114604473, 0.031826965510845184, 0.008424507454037666, -0.0024333265610039234, -0.007711046375334263, -0.051444776356220245, -0.01022469624876976, -0.010734986513853073, -0.030579591169953346, 0.010196346789598465, 0.01465665828436613, 0.02538219466805458, -0.0028916425071656704, 0.02874632738530636, -0.01757664978504181, 0.0039547462947666645, -0.13796725869178772, -0.009884502738714218, -0.0008416238706558943, 0.018323184922337532, -0.016489921137690544, -0.03691096603870392, -0.0070779090747237206, -0.032526250928640366, -0.03713776171207428, -0.05976061150431633, -0.011670517735183239, 0.00744645157828927, 0.03742125630378723, -0.03764805197715759, -0.0042075286619365215, 0.030239397659897804, -0.05242755636572838, -0.030863085761666298, 0.008807224221527576, 0.03197816386818886, 0.03160016983747482, 0.013494331389665604, 0.03103318251669407, 0.006737716030329466, -0.018559429794549942, -0.014609409496188164, 0.0016300925053656101, -0.02861403115093708, -0.010527090169489384, -0.051444776356220245, 0.02404032088816166, -0.05204956233501434, 0.038290638476610184, 0.033055443316698074, -0.012379253283143044, -0.009260815568268299, -0.023133140057325363, 0.009941201657056808, -0.02396472357213497, -0.009912852197885513, 0.001775383367203176, -0.006255775224417448, -0.0029955904465168715, 0.032053761184215546, -0.040936585515737534, 0.051747169345617294, -0.027952542528510094, -0.025703487917780876, -0.03795044496655464, 0.021961363032460213, -0.014259765855967999, 0.02490970492362976, -0.03804494068026543, -0.0008959602564573288, -0.001098540611565113, 0.012606048956513405, -0.0085237305611372, 0.03020159900188446, -0.019655609503388405, 0.05787064880132675, -0.0243805143982172, 0.0062416004948318005, -0.03796934336423874, 0.021243177354335785, -0.005306069273501635, 0.030050402507185936, -0.04947921633720398, -0.01628202572464943, -0.041692569851875305, 0.03483200818300247, 0.00362164038233459, 0.01980680599808693, 0.030579591169953346, -0.040105000138282776, -0.014571609906852245, -0.0229252427816391, -0.007923667319118977, -0.023170938715338707, 0.03061738982796669, -0.029351115226745605, -0.021810166537761688, -0.0170474611222744, 0.007696871645748615, -0.006836938671767712, 0.06062999367713928, -0.03377362713217735, -0.012322554364800453, -0.012039060704410076, -0.052389755845069885, 0.002440413925796747, -0.022414954379200935, -0.034057121723890305, -0.04414952173829079, -0.027631249278783798, -0.013154137879610062, 0.03607938066124916, -0.030863085761666298, 0.01140592247247696, 0.009331689216196537, 0.00825913529843092, -0.05337253585457802, 0.03991600498557091, -0.0025349119678139687, 0.03392482548952103, 0.009997900575399399, 0.05412852391600609, -0.01574338600039482, 0.011679967865347862, 0.0032365606166422367, 0.03709996119141579, -0.057379256933927536, -0.02490970492362976, 0.0033972072415053844, 0.009142693132162094, -0.05322134122252464, -0.0021770005114376545, 0.017935743555426598, 0.04237295687198639, 0.037874847650527954, 0.024966401979327202, 0.06784965097904205, -0.004488660488277674, 0.00998845137655735, -0.02226375602185726, -0.023057540878653526, 0.0687568336725235, 0.07076019048690796, 0.03254515305161476, 0.0034208318684250116, -0.008117388002574444, 0.02662956900894642, -0.021810166537761688, -0.03492650389671326, 0.026459472253918648, 0.02003360167145729, -0.019135868176817894, -0.014788955450057983, 0.01650882139801979, 0.02122427709400654, -0.008358358405530453, -0.0487610287964344, -0.008788324892520905, -0.014770056121051311, -0.034000422805547714, -0.025193197652697563, 0.003262547543272376, -0.026837466284632683, -0.012757246382534504, -0.04479210823774338, -0.03190256655216217, -0.008358358405530453, 0.006477845832705498, 0.04002940282225609, 0.019712308421730995, 0.017945192754268646, 0.01539374329149723, 0.009950651787221432, -0.01374947652220726, 0.00715350778773427, 0.07711046189069748, 0.039462413638830185, -0.0004745577462017536, -0.03097648359835148, -0.051558174192905426, -0.03889542445540428, 0.049743808805942535, 0.021526671946048737, -0.015289795584976673, 0.0013276985846459866, 0.013503781519830227, 0.04437631741166115, 0.0246829092502594, -0.027650149539113045, -0.0080795893445611, 0.011065729893743992, -0.03515329957008362, 0.0006473120884038508, 0.022830745205283165, -0.0022419679444283247, -0.03785594552755356, 0.03471861034631729, 0.009624633006751537, 0.012823394499719143, 0.01816253922879696, 0.0007359040901064873, -0.03014490008354187, -0.017888493835926056, 0.024664008989930153, 0.05254095420241356, -0.014288115315139294, -0.01692461222410202, -0.010309744626283646, 0.0593448169529438, 0.005920307245105505, 0.06546829640865326, 0.03133557736873627, 0.02768794819712639, 0.020241497084498405, -0.007729946170002222, 0.01603632979094982, -0.03961361199617386, -0.0017151408828794956, 0.03020159900188446, 0.002016353653743863, -0.027820246294140816, 0.00566988717764616, 0.02808484062552452, -0.04312894120812416, -0.02092188410460949, -0.021469973027706146, -0.009454537183046341, 0.003841348458081484, -0.0056557124480605125, 0.05276774987578392, 0.015998531132936478, -0.025722388178110123, 0.04365812987089157, -0.02780134603381157, 0.0010129016591235995, 0.004498110618442297, 0.008273310028016567, 0.0013725851895287633, 0.011547669768333435, -0.02738555520772934, -0.027706848457455635, -0.020260397344827652, 0.02708316035568714, 0.030655190348625183, 0.005164321977645159, 0.028349434956908226, -0.0023565469309687614, -0.01775619573891163, -0.018824025988578796, 0.04978160932660103, -0.0016726166941225529, 0.016196977347135544, 0.012180807068943977, 0.010801134631037712, -0.04335573688149452, -0.01146262139081955, 0.04970601201057434, -0.03214826062321663, 0.05337253585457802, -0.004294939339160919]', 'distance': 0.6715814471244812}, {'title': 'LLMPC: Large Language Model Predictive Control', 'authors': ['Gabriel Maher'], 'summary': 'Recent advancements in prompting techniques for Large Language Models (LLMs)\\nhave improved their reasoning, planning, and action abilities. This paper\\nexamines these prompting techniques through the lens of model predictive\\ncontrol (MPC). We show that LLMs act as implicit planning cost function\\nminimizers when planning prompts are used. We propose a unified MPC framework\\nfor planning with LLMs and demonstrate improved performance over few shot\\nprompting on several planning benchmarks.', 'published': '2025-01-05T09:37:23+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2501.02486v2', 'vector': '[-0.047432780265808105, 0.008889035321772099, -0.01288172509521246, -0.008220513351261616, -0.027165040373802185, -0.03483690321445465, -0.0016102156369015574, 0.008981245569884777, -0.012789514847099781, 0.0014868847792968154, -0.05927253141999245, -0.030539922416210175, -0.016763761639595032, 0.033527523279190063, -0.04938763007521629, 0.014855016954243183, -0.0029599382542073727, 0.0017612093361094594, 0.000496204593218863, -0.02260987088084221, 0.021060744300484657, -0.026906851679086685, 0.0412362739443779, 0.0061227381229400635, 0.0029115278739482164, 0.004543643444776535, -0.07089097797870636, -0.023089362308382988, 0.009211770258843899, 0.05373993515968323, 0.012909387238323689, -0.006546903867274523, 0.04776473343372345, 0.012263918295502663, -0.01186741515994072, -0.008727667853236198, 0.018607961013913155, -0.0071600996889173985, -0.033084917813539505, -0.004555169492959976, -0.005518763326108456, -0.0023525054566562176, 0.013554857112467289, 0.007376792840659618, 0.011747542768716812, 0.00493784062564373, -0.022407008334994316, -0.027902718633413315, -0.0020309232641011477, -0.027478553354740143, 0.011747542768716812, -0.04156822711229324, 0.03972402960062027, -0.04798603802919388, 0.0006437404663302004, 0.0013566382694989443, -0.05709637701511383, 0.00046508375089615583, -0.02788427658379078, 0.02209349535405636, -0.02096853405237198, -0.0008454496273770928, 0.023107804358005524, -0.03236567974090576, 0.007883947342634201, 0.022241029888391495, -0.007519718259572983, 0.03618317097425461, -0.02329222299158573, -0.07398923486471176, -0.015233077108860016, 0.023661063984036446, -0.0031466633081436157, -0.03210749104619026, -0.055694784969091415, 0.02762608975172043, -0.014227989129722118, -0.017234032973647118, -0.0143478624522686, 0.02939651906490326, -0.04654756188392639, -0.00644086254760623, 0.0535186342895031, -0.010696349665522575, 0.027994928881525993, 0.012402232736349106, -0.008792215026915073, -0.014984110370278358, -0.008146746084094048, -0.0023029425647109747, -0.01808236353099346, 0.029470287263393402, 0.0414944589138031, -0.06207571178674698, -0.017418451607227325, 0.014532282017171383, -0.04370749741792679, 0.025818774476647377, 0.005076155532151461, 0.0011255372082814574, 0.02096853405237198, 0.02605852112174034, 0.013490309938788414, 0.015445159748196602, 0.03939207270741463, -0.012724967673420906, -0.005440384615212679, 0.0105672562494874, 0.02347664348781109, -0.014993331395089626, 0.000810294586699456, 0.011987288482487202, 0.029322752729058266, 0.046658214181661606, -0.000280519830994308, -0.045883651822805405, 0.03297426551580429, -0.0394289568066597, 0.01553736999630928, -0.0014327114913612604, 0.033177126199007034, -0.000989527557976544, 0.054698918014764786, -0.004267013631761074, -0.00913339201360941, 0.02104230225086212, 0.012134823948144913, 0.008391101844608784, -0.024583162739872932, -0.007215425372123718, -0.008022262714803219, 0.015528148971498013, -0.04721147567033768, 0.04580988362431526, -0.04440829157829285, -0.06672309339046478, -0.03328777849674225, -0.029968220740556717, -0.009774250909686089, -0.011664553545415401, 0.04053547605872154, 0.03928142413496971, -0.017796512693166733, -0.034947555512189865, 0.03708682581782341, -0.07100163400173187, 0.020581252872943878, 0.0016689994372427464, -0.013112249784171581, -0.011655332520604134, -0.03155423328280449, 0.024214323610067368, -0.013434983789920807, 0.05108429118990898, 0.01869094930589199, -0.011858195066452026, -0.058497969061136246, 0.04938763007521629, 0.009128781035542488, 0.04533039405941963, -0.0014523060526698828, -0.0012586653465405107, -0.006145790684968233, 0.02438030019402504, 0.036459799855947495, 0.006491577718406916, 0.01681908778846264, 0.035980306565761566, -0.02071034535765648, -0.01865406520664692, 0.00358696561306715, -0.04119938984513283, -0.013379658572375774, -0.018349772319197655, 0.01940096542239189, -5.590225919149816e-05, 0.04931386187672615, 0.02554214559495449, 0.03173865005373955, 0.002971464302390814, 0.0030682848300784826, -0.022702081128954887, 0.02338443323969841, -0.03234723582863808, -0.037492550909519196, -0.025357725098729134, -0.011535460129380226, -0.0683828741312027, 0.026445802301168442, -0.024454068392515182, -0.014689038507640362, -0.03207060694694519, 0.026648664847016335, 0.009078065864741802, 0.035463932901620865, -0.06391990929841995, -0.004140225239098072, -0.061264265328645706, -8.3277074736543e-05, -0.018027037382125854, 0.022849615663290024, 0.0719975009560585, -0.03489223122596741, -0.01871861144900322, -0.03773229569196701, 0.014181884005665779, -0.047174591571092606, -0.05458826944231987, -0.026353592053055763, -0.013683950528502464, 0.023716390132904053, 0.0215217936784029, 0.056506235152482986, -0.012632757425308228, 0.03690240532159805, 0.025357725098729134, -0.009105728939175606, 0.050199076533317566, -0.006297937128692865, -0.0740630030632019, -0.0798906683921814, 0.09154599905014038, 0.009234822355210781, 0.02329222299158573, 0.0011168925557285547, 0.007782516535371542, 0.05676442012190819, 0.013056923635303974, -0.012826398946344852, 0.003888953011482954, 0.04296981915831566, 0.01238379068672657, -0.032052166759967804, -0.005057713482528925, -0.039097003638744354, -0.01878315955400467, -0.0016885939985513687, 0.032052166759967804, 0.018128467723727226, 0.019954225048422813, -0.013554857112467289, 0.004758031573146582, 0.0047027054242789745, 0.023956134915351868, -0.038875699043273926, -0.020470600575208664, 0.005546426400542259, 0.03065057471394539, 0.004396107513457537, -0.002757076406851411, -0.0027916550170630217, -0.008953582495450974, -0.07089097797870636, -0.03284516930580139, -0.017713524401187897, 0.006035138852894306, 0.02065502107143402, -0.02316313050687313, -0.03262386843562126, -0.022702081128954887, 0.021189836785197258, -0.0021819169633090496, 0.005016219336539507, 0.002201511524617672, 0.03454183414578438, 0.01510398369282484, 0.014772027730941772, -0.04938763007521629, 0.025689681991934776, 0.02865884080529213, -0.02329222299158573, -0.038912583142519, 0.047875385731458664, 0.001618283917196095, 0.009829576127231121, 0.007003342732787132, -0.056985724717378616, -0.027736742049455643, 0.05503087490797043, -0.008552469313144684, -0.03478157892823219, -0.03902323544025421, 0.017224811017513275, -0.014661376364529133, 0.023624179884791374, -0.01927187107503414, -0.05525217950344086, -0.01290016621351242, -0.014191105030477047, -0.05658000335097313, 0.006247221492230892, -0.01975136250257492, 0.06952627003192902, 0.0022856532596051693, -0.033269334584474564, 0.005168365314602852, 0.02657489664852619, -0.14163441956043243, 0.028160907328128815, -0.03631226345896721, -0.01675454154610634, 0.017473777756094933, 0.014993331395089626, -0.0013612487819045782, 0.05178508535027504, 0.012669641524553299, 0.01730780117213726, 0.004983945749700069, -0.04319112375378609, 0.041605111211538315, -0.013103028759360313, -0.057465218007564545, 0.023919250816106796, 0.019161218777298927, 0.00813752505928278, -0.02498888596892357, -0.017833396792411804, -0.010465824976563454, -0.04695328697562218, 0.01971447840332985, -0.045404162257909775, -0.01104674767702818, -0.004776473622769117, 0.023568853735923767, 0.017999375239014626, 0.0016932045109570026, 0.023808598518371582, 0.007256919983774424, 0.01988045684993267, -0.00741367693990469, 0.025560587644577026, -0.01627505011856556, 0.005140702705830336, 0.004854851868003607, 0.018414318561553955, -0.023531969636678696, -0.0011215030681341887, 0.014246431179344654, 0.03548237308859825, 0.043891917914152145, 0.034984439611434937, -0.007980767637491226, -0.02519174851477146, 0.007828621193766594, -0.01927187107503414, 0.018939916044473648, -0.016062967479228973, -0.010133869014680386, 0.034431181848049164, -0.01988045684993267, 0.023236896842718124, -0.03791671618819237, -0.020544368773698807, 0.006353262811899185, -0.013296669349074364, -0.0414944589138031, -0.0012897861888632178, -0.014513839967548847, -0.0035385554656386375, -0.0034693980123847723, -0.03551925718784332, -0.027386343106627464, -0.009783471934497356, 0.08571833372116089, 0.011489355005323887, 0.0004696942341979593, -0.03363817557692528, 0.02506265416741371, -0.021669330075383186, 0.02177998051047325, -0.027644531801342964, 0.06720258295536041, 0.058940574526786804, 0.019345639273524284, 0.008354217745363712, -0.013370437547564507, -0.07140735536813736, -0.042637862265110016, -0.002455089008435607, -0.007109384052455425, 0.02576345019042492, -0.004592053592205048, -0.0409780852496624, 0.05967825651168823, 0.004887125454843044, 0.034117668867111206, 0.22617247700691223, 0.028345325961709023, -0.022665197029709816, -0.006302547641098499, 0.025081096217036247, 0.025615913793444633, -0.050457265228033066, 0.034947555512189865, 0.018746275454759598, -0.012236255221068859, 0.05123182758688927, 0.044334527105093, 0.018903031945228577, 0.039834681898355484, -0.0013647066662088037, 0.016671551391482353, -0.06923120468854904, 0.001282870420254767, 0.07487444579601288, 0.024656930938363075, -0.002568046096712351, 0.01493800524622202, 0.03828555345535278, -0.03478157892823219, -0.04820734262466431, -0.0435599610209465, 0.004868683405220509, -0.0035800498444586992, 0.023052478209137917, -0.02013864554464817, -0.01971447840332985, 0.040609244257211685, 0.04370749741792679, -0.004790304694324732, 0.025431493297219276, 0.011360260657966137, -0.005892213433980942, 0.006758986506611109, -0.036939289420843124, 0.03406234085559845, -0.014467734843492508, -0.014006685465574265, -0.05086298659443855, -0.021374257281422615, 0.001561805373057723, -0.024822907522320747, 0.03004198893904686, 0.04625249281525612, 0.027958044782280922, -0.02657489664852619, -0.03773229569196701, -0.0004936112090945244, -0.0014523060526698828, -0.003497060853987932, 0.01598919928073883, -0.004329255316406488, 0.0025957089383155107, 0.03752943500876427, -0.005841497797518969, 0.02074722945690155, 0.05886680632829666, 0.0053573958575725555, 0.031664881855249405, 0.021189836785197258, 0.017741186544299126, 0.020728787407279015, -0.025708124041557312, 0.00509920809417963, 0.05023596063256264, 0.019917340949177742, 0.06524773687124252, 0.045145973563194275, 0.0018660981440916657, -0.0070540583692491055, 0.03527951240539551, -0.029765360057353973, 0.04912944138050079, 0.038875699043273926, -0.007777906022965908, 0.010456603951752186, -0.05005154013633728, 0.031111624091863632, 0.023624179884791374, -0.015878546983003616, 0.0004146564460825175, -0.023329107090830803, -0.022720521315932274, 0.044924668967723846, -0.012180929072201252, -0.01750144176185131, -0.008713836781680584, -0.00976502988487482, 0.02209349535405636, -0.018165351822972298, -0.020507484674453735, 0.009552947245538235, 0.047395896166563034, -0.0655428022146225, -0.01923498697578907, 0.016321154311299324, 0.05060479789972305, -6.555548316100612e-05, 0.03802736848592758, -0.0060166968032717705, -0.005043881945312023, 0.011221946217119694, -0.0013877591118216515, 0.02714659832417965, -0.012263918295502663, 0.07893168181180954, -0.0396871455013752, 0.0048133572563529015, -0.02506265416741371, -0.0548095703125, 0.010558035224676132, 0.0440763384103775, -0.005408111494034529, 0.030964087694883347, 0.013527194038033485, -0.0002780705108307302, -0.01104674767702818, 0.09198860824108124, -0.024749141186475754, 0.017160264775156975, -0.0069434065371751785, -0.016330376267433167, 0.013407320715487003, 0.020157087594270706, -0.02917521633207798, -0.012697304598987103, 0.007851674221456051, 0.02402990311384201, 0.019954225048422813, 0.04772784933447838, 0.010124647989869118, -0.0020793334115296602, 0.006219558417797089, -0.008160577155649662, 0.026095405220985413, 0.010834665037691593, -0.0430435873568058, 0.04182641580700874, -0.025099538266658783, -0.0019191188039258122, 0.034431181848049164, 0.05425631254911423, -0.03846997395157814, -0.0035569972824305296, -0.021558677777647972, 0.041900184005498886, 0.00024017800751607865, 0.005472658202052116, 0.042859166860580444, -0.044371411204338074, 0.017298579216003418, -0.039613377302885056, -0.031572673469781876, -0.013517973013222218, 0.03583277016878128, -0.03210749104619026, 0.006265663541853428, 0.056506235152482986, 0.023107804358005524, 0.002338673919439316, 0.01988045684993267, 0.0440763384103775, 0.015786336734890938, 0.06288716197013855, -0.009801913984119892, -0.011415586806833744, -0.019511617720127106, 0.006892690900713205, -0.024103671312332153, 0.05624804645776749, -0.01605374552309513, 0.009543726220726967, -0.037750739604234695, -0.006265663541853428, -0.011304935440421104, -0.04558857902884483, 0.031277600675821304, 0.0027271080762147903, -0.05281783640384674, 0.05034661293029785, -0.015841662883758545, -0.03747410699725151, -0.016579343006014824, -0.006971069145947695, 0.02347664348781109, -0.02692529372870922, -0.04794915392994881, 0.08431674540042877, -0.022794289514422417, -0.011351040564477444, 0.0033472198992967606, 0.012162487022578716, 0.04333866015076637, -0.011618448421359062, 0.01836821436882019, -0.03743722289800644, 0.003121305489912629, -0.00017836854385677725, -0.028806377202272415, 0.014375525526702404, -0.03059524856507778, 0.019548501819372177, -0.018995242193341255, -0.008695394732058048, -0.021927516907453537, -0.05230146273970604, -0.0212636049836874, -0.01586010493338108, -0.07550147920846939, -0.0047672525979578495, 0.018091585487127304, 0.013849928975105286, -0.006431641522794962, 0.03290049731731415, -0.0051453132182359695, 0.021632445976138115, 0.005703182891011238, -0.017612092196941376, -0.025560587644577026, -0.04997777193784714, -0.03522418439388275, -0.0049193985760211945, 0.005504931788891554, 0.008746109902858734, 0.022720521315932274, 0.014052790589630604, -0.004059541504830122, -0.040351055562496185, 0.006892690900713205, 0.01365628745406866, 0.016809867694973946, 0.03072434291243553, 0.01701272837817669, 0.027681415900588036, -0.015740232542157173, -0.006422420497983694, -0.0414944589138031, 0.014984110370278358, -0.0004642192798200995, 0.04145757481455803, -0.007667254190891981, -0.03489223122596741, -0.004246266558766365, 0.035593025386333466, 0.026777759194374084, 0.03361973538994789, 0.005172975827008486, -0.0037552486173808575, 0.0198435727506876, 0.008437206968665123, 0.025579029694199562, -0.024398742243647575, 0.024841349571943283, -0.024214323610067368, -0.030539922416210175, 0.012983155436813831, -0.007349129766225815, 0.01582322083413601, 0.004734979011118412, -0.01238379068672657, -0.03605407476425171, -0.05632181465625763, -0.0007382556213997304, -0.028234675526618958, -0.013149132952094078, -0.05554725229740143, 0.0038405428640544415, 0.011129735969007015, -0.0016955097671598196, -0.07974313199520111, 0.0819561704993248, -0.014707480557262897, -0.008321944624185562, 0.022111937403678894, 0.008381880819797516, 0.011931962333619595, -0.057944707572460175, 0.048908136785030365, 0.00018542836187407374, 0.010143090039491653, -0.02264675498008728, 0.0029553277418017387, 0.05731768161058426, -0.015482043847441673, 0.030632132664322853, 0.0014569165650755167, 0.040867432951927185, 0.03869127854704857, -0.026242941617965698, -0.03651512414216995, 0.022056611254811287, 0.005016219336539507, -0.019419407472014427, 0.07716125249862671, -0.0011929657775908709, 0.009216380305588245, -0.037197478115558624, 0.0024942781310528517, -0.01011542696505785, 0.01936408132314682, -0.01238379068672657, 0.008262008428573608, -0.00035587261663749814, -0.014901122078299522, 0.028806377202272415, -0.033564407378435135, -0.0018730137962847948, -0.03129604458808899, 0.023218456655740738, 0.026777759194374084, 0.02174309641122818, 0.0568750724196434, 0.02679619938135147, -0.03802736848592758, 0.033177126199007034, 0.008995076641440392, 0.011470912955701351, 0.013794602826237679, -0.020452158525586128, -0.005113039631396532, -0.027201924473047256, 0.004951672162860632, 0.01510398369282484, -0.018663285300135612, -0.005200638901442289, 0.025357725098729134, -0.029322752729058266, -0.046621330082416534, -0.03212593495845795, 0.0008131761569529772, -0.008736888878047466, -0.03439429774880409, 0.04533039405941963, 0.013582520186901093, 0.02650112845003605, 0.004121783189475536, 0.0425272099673748, -0.06782960891723633, 0.01100064255297184, -0.018921473994851112, 0.03625693544745445, -0.03422831743955612, -0.006205726880580187, 0.0061227381229400635, 0.031277600675821304, -0.02117139659821987, -0.005873771384358406, -0.04448205977678299, -0.02083943970501423, -0.04831799492239952, 0.02096853405237198, -0.0642518699169159, -0.005048492457717657, 0.03288205340504646, 0.029636265709996223, -0.0025173306930810213, -0.024878233671188354, 0.009562168270349503, -0.00873227883130312, -0.037492550909519196, 0.0065607354044914246, 0.026316707953810692, 0.0074782236479222775, -0.07166554033756256, -0.005016219336539507, -0.05185885354876518, -0.003932752646505833, -0.0014799691271036863, -0.052707184106111526, -0.015168530866503716, -0.012586653232574463, 0.03664422035217285, -0.011443249881267548, -0.004734979011118412, 0.030613690614700317, -0.028474420309066772, -0.009848018176853657, 0.004412244074046612, -0.0011722184717655182, -0.02096853405237198, 0.0002648153458721936, -0.024527836591005325, -0.048428647220134735, 0.01730780117213726, 0.04669509828090668, 0.011572344228625298, -0.01685597188770771, -0.009303980506956577, -0.02000955119729042, 0.010539593175053596, -0.12252853065729141, -0.0037690801545977592, 0.005537205375730991, -0.0055279843509197235, -0.03065057471394539, -0.046141840517520905, -0.013914475217461586, -0.01542671862989664, -0.030281735584139824, -0.08217747509479523, 0.00809141993522644, 0.025431493297219276, 0.03539016470313072, -0.030761227011680603, -0.01032751053571701, -0.0019098977791145444, -0.04651067778468132, 0.0073721823282539845, 0.0106318024918437, 0.031019413843750954, 0.016477910801768303, 0.03976091369986534, -0.0003518384473863989, 0.02541305124759674, -0.016671551391482353, -0.031000971794128418, -0.04577299952507019, -0.0530022569000721, -0.0417526476085186, -0.03540860489010811, 0.002487362362444401, -0.07539082318544388, 0.03480001911520958, 0.039871565997600555, 0.006367094349116087, -0.025745008140802383, -0.004038793966174126, 0.002655645366758108, -0.0286957249045372, -0.031830862164497375, 0.01794404909014702, 0.008432595990598202, -0.05156378075480461, 0.01669921539723873, -0.07930052280426025, 0.07428430765867233, 0.0028953913133591413, -0.015814000740647316, -0.04370749741792679, -0.0070494478568434715, -0.01668999344110489, 0.020728787407279015, -0.05514152720570564, 0.024306531995534897, 0.015703348442912102, 0.048133574426174164, -0.04396568611264229, 0.021079186350107193, -0.0017462251707911491, 0.058202896267175674, -0.03223658353090286, -0.013038481585681438, -0.04857617989182472, -0.01598919928073883, 0.0031881576869636774, -0.01111129391938448, -0.02965470775961876, -0.012715746648609638, -0.043633729219436646, 0.03162800148129463, 0.026556454598903656, 0.016358038410544395, -0.01076089683920145, -0.04621560871601105, -0.00828506052494049, -0.020544368773698807, 0.01238379068672657, -0.0045321169309318066, 0.010825444012880325, -0.009373137727379799, -0.02052592672407627, -0.01598919928073883, -0.04610495641827583, -0.006542293354868889, 0.022812731564044952, -0.04931386187672615, -0.0071739312261343, 0.014550724066793919, -0.07841531187295914, 0.014513839967548847, -0.03843308985233307, -0.014357083477079868, -0.014062011614441872, -0.014845795929431915, -0.0016251996858045459, -0.003914310596883297, -0.022185705602169037, 0.008529417216777802, -0.0002802316739689559, -0.030871879309415817, -0.06155933812260628, 0.03609095886349678, 0.009958670474588871, 0.03920765593647957, -0.00837727077305317, 0.029156774282455444, -0.00932703260332346, 0.01497488934546709, 0.04824422672390938, 0.030503038316965103, -0.054440733045339584, 0.017547545954585075, -0.008049924857914448, 0.022941825911402702, -0.07007953524589539, -0.020046435296535492, 0.035463932901620865, 0.042379673570394516, 0.03760320320725441, 0.006238000467419624, 0.03740033879876137, -0.004149445798248053, -0.017741186544299126, -0.018995242193341255, 0.011268051341176033, 0.07409988343715668, 0.06303469836711884, 0.025689681991934776, -0.007934662513434887, -0.004822578281164169, 0.048465531319379807, -0.04001910239458084, -0.029617823660373688, 0.00474420003592968, 0.017455335706472397, 0.0259847529232502, -0.021687772125005722, 0.029673149809241295, -0.04370749741792679, -0.004942451138049364, -0.07103851437568665, -0.020286180078983307, 0.002782433992251754, -0.034910671412944794, -0.03939207270741463, -0.00014321351773105562, -0.006408588960766792, -0.00014141254359856248, -0.01763053424656391, -0.02779206819832325, -0.0016620836686342955, 0.010816222988069057, 0.03197839856147766, 0.018488086760044098, 0.0070955525152385235, 0.015601917169988155, -0.04503532126545906, 0.03328777849674225, 0.011009863577783108, 0.06878859549760818, 0.01858951896429062, -0.004216297995299101, -0.014606050215661526, -0.0425272099673748, -0.04912944138050079, 0.043633729219436646, -0.030281735584139824, 0.018100805580615997, -0.020673461258411407, 0.04580988362431526, 0.002584182657301426, 0.0099217863753438, -0.031572673469781876, 0.02817934937775135, -0.004790304694324732, -0.003125916002318263, 0.005938318092375994, 0.053223561495542526, 0.0006108906818553805, 0.0002816724590957165, 0.03854374215006828, 0.022960267961025238, 0.021816864609718323, 0.013600962236523628, -0.023052478209137917, 0.000994714442640543, -2.7680982384481467e-05, 0.03160955756902695, 0.030410828068852425, -0.037197478115558624, -0.002929969923570752, 0.0013036176096647978, 0.054957106709480286, 0.00455978000536561, 0.01698506623506546, 0.03297426551580429, 0.04444517567753792, 0.0396871455013752, 0.006053580902516842, 0.02386392466723919, -0.051895737648010254, -0.010493488050997257, 0.02498888596892357, -0.024398742243647575, -0.018617181107401848, -0.015721790492534637, 0.005057713482528925, -0.05776028707623482, -0.05801847577095032, -0.021152954548597336, 0.027294134721159935, -0.012964713387191296, -0.022056611254811287, 0.03160955756902695, 0.007330688182264566, -0.0020078709349036217, 0.044371411204338074, 0.01534372940659523, 0.006136569660156965, 0.01246677991002798, -0.012540548108518124, 0.0035777445882558823, 0.0063993679359555244, -0.04282228276133537, -0.04577299952507019, -0.0017681251047179103, 0.012761851772665977, -0.016256608068943024, -0.004615106154233217, 0.013481088913977146, 0.010742454789578915, -0.0027063610032200813, 0.003665344091132283, 0.00944229494780302, -0.0028100970666855574, -0.030152641236782074, -0.010972979478538036, 0.01804547943174839, -0.0522276945412159, -0.030097315087914467, 0.05307602509856224, -0.017510661855340004, 0.06576410681009293, -0.03323245048522949]', 'distance': 0.6725456118583679}, {'title': 'When Prompting Fails to Sway: Inertia in Moral and Value Judgments of Large Language Models', 'authors': ['Bruce W. Lee', 'Yeongheon Lee', 'Hyunsoo Cho'], 'summary': 'Large Language Models (LLMs) exhibit non-deterministic behavior, and\\nprompting has emerged as a primary method for steering their outputs toward\\ndesired directions. One popular strategy involves assigning a specific\\n\"persona\" to the model to induce more varied and context-sensitive responses,\\nakin to the diversity found in human perspectives. However, contrary to the\\nexpectation that persona-based prompting would yield a wide range of opinions,\\nour experiments demonstrate that LLMs maintain consistent value orientations.\\nIn particular, we observe a persistent inertia in their responses, where\\ncertain moral and value dimensions, especially harm avoidance and fairness,\\nremain distinctly skewed in one direction despite varied persona settings. To\\ninvestigate this phenomenon systematically, use role-play at scale, which\\ncombines randomized, diverse persona prompts with a macroscopic trend analysis\\nof model outputs. Our findings highlight the strong internal biases and value\\npreferences in LLMs, underscoring the need for careful scrutiny and potential\\nadjustment of these models to ensure balanced and equitable applications.', 'published': '2024-08-16T23:24:10+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2408.09049v2', 'vector': '[-0.06257887184619904, 0.008008947595953941, -0.004226944409310818, -0.01325255911797285, -0.044245366007089615, 0.004714944865554571, 0.014582599513232708, -0.021701663732528687, -0.016955619677901268, -0.025797039270401, -0.007989809848368168, -0.004741258453577757, -0.04072410985827446, 0.012994205579161644, -0.006774593144655228, 0.030657906085252762, -0.0024148840457201004, -0.01693648286163807, -0.03695406764745712, -0.0060091023333370686, -0.007559221237897873, 0.028457120060920715, 0.007683613803237677, -0.012075616978108883, -0.01555859949439764, 0.021261505782604218, -0.025050684809684753, -0.0258353129029274, -0.0315573550760746, 0.030313434079289436, -0.023711076006293297, 0.030887551605701447, 0.032973513007164, -0.017127856612205505, -0.014754834584891796, -0.0011392656015232205, -0.0008534025982953608, -0.009118909016251564, -0.03287782892584801, 0.023309193551540375, -0.02694527432322502, -0.002278531203046441, -0.0258353129029274, -0.011003930121660233, 0.033777281641960144, -0.024878449738025665, 0.01183640118688345, -0.031595632433891296, 0.014563461765646935, -0.03343280777335167, -0.01680252142250538, -0.032475944608449936, 0.0415661484003067, -0.026275470852851868, 0.02062997594475746, 0.010276713408529758, -0.00777451554313302, -0.0060904361307621, -0.03326057270169258, 0.01791248470544815, -0.010209732688963413, -0.02623719535768032, 0.010869968682527542, -0.017252247780561447, 0.010917811654508114, -0.0007828339003026485, -0.018907621502876282, 0.03209320083260536, -0.012994205579161644, -0.06625322252511978, 0.008659614250063896, 0.03643735870718956, -0.04550842568278313, -0.004380042664706707, -0.054579492658376694, 0.048838309943675995, -0.05576600134372711, 0.01589350216090679, -0.06483706831932068, 0.029605355113744736, -0.009372477419674397, -0.0023084331769496202, -0.01397977489978075, 0.006410985253751278, 0.03953759744763374, 0.0401882641017437, 0.00010032115096691996, -0.0005271719419397414, 0.027079235762357712, -0.033337123692035675, -0.019242525100708008, -0.017261816188693047, 0.03282041847705841, -0.04906795918941498, -0.04083893075585365, 0.001881432719528675, -0.06185165420174599, 0.012888951227068901, -0.019902760162949562, 0.008764869533479214, 0.036915794014930725, 0.04271438345313072, -0.0215294286608696, -0.02566307783126831, 0.0008163241436704993, -0.03343280777335167, -0.001010686974041164, 0.025471705943346024, -0.006406201049685478, 0.0037891792599111795, -0.013329108245670795, 0.03498292714357376, 0.060856517404317856, 0.0250698234885931, -0.029567081481218338, 0.006846358068287373, 0.03216974809765816, -0.03488724306225777, 0.024916725233197212, 0.000767882913351059, 0.018276091665029526, 0.03236112371087074, 0.10693906247615814, -0.02409382164478302, -0.01894589699804783, 0.00873616337776184, -0.016391070559620857, 0.017099149525165558, -0.05052239075303078, -0.0013635304057970643, 0.007377417292445898, 0.0005962455179542303, -0.004246081691235304, 0.01241051871329546, -0.039767246693372726, -0.06521981209516525, -0.023194370791316032, 0.044398464262485504, -0.012008636258542538, -0.020113270729780197, 0.03222716227173805, 0.017960326746106148, -0.010171458125114441, -0.03969069570302963, -0.01680252142250538, -0.031614769250154495, 0.029471393674612045, -0.008123770356178284, -0.018132561817765236, -0.05036929249763489, 0.018314367160201073, -0.009449026547372341, 0.02024723030626774, 0.06690389662981033, 0.003554747672751546, -0.015147148631513119, -0.03926967456936836, 0.05970827862620354, -0.01690777763724327, 0.03969069570302963, 0.016965189948678017, 0.03603547811508179, -0.010142752900719643, 0.01778809167444706, 0.008630908094346523, -0.010697733610868454, 0.03699234127998352, 0.061622004956007004, -0.012841107323765755, -0.06514326483011246, 0.010209732688963413, -0.03770042210817337, 0.028437981382012367, -0.017587149515748024, -0.009539928287267685, 0.005731611978262663, 0.07922829687595367, 0.034045200794935226, -0.0037867871578782797, -0.001471177558414638, 0.010248007252812386, -0.039652422070503235, -0.007717103697359562, 0.011453655548393726, -0.016563305631279945, -0.023041272535920143, -0.0401117168366909, -0.03136598318815231, 0.04129822552204132, -0.011750283651053905, -0.014343383722007275, -0.02206527069211006, 0.0008420398226007819, 0.027270609512925148, 0.03379641845822334, -0.0746736228466034, -0.034217435866594315, -0.02811264805495739, -0.007248240523040295, -0.029050374403595924, -0.02665821649134159, 0.038791242986917496, -0.02941398322582245, -0.03515516221523285, -0.04531705379486084, -0.022007860243320465, -0.07436742633581161, -0.050981685519218445, 0.02395986020565033, -0.020285505801439285, 0.03859987109899521, 0.01605616882443428, 0.046082545071840286, -0.0250889603048563, -0.026428569108247757, -0.027002686634659767, -0.0430588573217392, 0.07138201594352722, -0.01606573723256588, -0.007444397546350956, -0.036207713186740875, 0.057564906775951385, -0.011558910831809044, 0.039499323815107346, -0.013367382809519768, 0.04060928523540497, 0.04971862584352493, -0.0008540006238035858, 0.0070712207816541195, 0.02181648649275303, 0.01921381801366806, 0.017300091683864594, 0.006434906739741564, -0.012946362607181072, -0.027174921706318855, -0.021912172436714172, -0.012266989797353745, -0.016716403886675835, 0.018400484696030617, -0.0022091586142778397, -0.00013762387970928103, -0.06135408580303192, 0.014324245974421501, 0.042829208076000214, -0.041374776512384415, -0.04887658730149269, -0.026887863874435425, 0.01605616882443428, 0.01693648286163807, -0.013443931937217712, 0.022294918075203896, 0.010936949402093887, -0.016725974157452583, -0.038331951946020126, -0.02095530927181244, -0.006549730431288481, 0.03481069207191467, -0.020323779433965683, -0.00996094848960638, 0.010056635364890099, 0.024266056716442108, 0.012152166105806828, 0.002973453141748905, 0.007219534832984209, 0.007788868620991707, -0.004097767639905214, -0.020993584766983986, 0.01723311096429825, 0.004937415476888418, 0.030906690284609795, -0.025050684809684753, -0.022849898785352707, 0.02395986020565033, -0.018237818032503128, -0.012142597697675228, 0.04535532742738724, -0.03098323941230774, -0.039805520325899124, 0.07348711043596268, 0.016094444319605827, -0.024323469027876854, 0.01821867935359478, 0.03861900791525841, -0.023978998884558678, 0.01619969867169857, -0.003822669619694352, -0.05997620150446892, -0.056148748844861984, -0.015606443397700787, -0.07540084421634674, 0.04692458361387253, -0.011147459037601948, 0.07475017011165619, 0.018907621502876282, -0.04550842568278313, -0.022256644442677498, -0.01205647923052311, -0.1705513447523117, -0.004401572048664093, -0.03230370953679085, 0.007358280010521412, 0.0008569908095523715, 0.013357813470065594, -0.05817729979753494, 0.019998446106910706, -0.0001316434791078791, 0.009420320391654968, 0.0008803143864497542, -0.057564906775951385, 0.02378762513399124, 0.003518865443766117, -0.05147925391793251, 0.021031858399510384, 0.003994904924184084, 0.012228715233504772, 0.005052239168435335, -0.01949130930006504, -0.005970827769488096, -0.07869245111942291, 0.01748189516365528, -0.059019338339567184, -0.008071143180131912, -0.029471393674612045, -0.018314367160201073, 0.015950914472341537, -0.01097522396594286, -0.008956242352724075, -0.022486291825771332, 0.0429823063313961, -0.0002966276661027223, 0.020170681178569794, 0.00777451554313302, 0.060435496270656586, -0.008994516916573048, -0.02206527069211006, -0.014907932840287685, 0.023462291806936264, 0.004071454051882029, 0.03255249559879303, 0.001103383139707148, 0.031461670994758606, 0.002595492172986269, -0.014314677566289902, 0.005009180400520563, -0.004109728615731001, 0.009501653723418713, 0.011099616065621376, -0.0006470789085142314, -0.026792176067829132, -0.05817729979753494, 0.01253491174429655, -0.05565118044614792, -0.016697267070412636, 0.04187234491109848, -0.018601424992084503, -0.00938683096319437, 0.005420631729066372, -0.030811002478003502, -0.013137735426425934, -0.0014891187893226743, -0.014506050385534763, -0.017252247780561447, -0.019252093508839607, 0.07915174216032028, 0.021893035620450974, 0.00782714318484068, -0.024036409333348274, 0.027174921706318855, -0.07080789655447006, 0.018304798752069473, -0.020036721602082253, 0.014745266176760197, 0.026447705924510956, 0.03345194831490517, -0.012592323124408722, -0.007908476516604424, -0.07157338410615921, 0.010085340589284897, 0.017692405730485916, 0.011405812576413155, 0.018898053094744682, -0.008501731790602207, -0.005760318133980036, 0.031346846371889114, 0.0039040029514580965, 0.03641822189092636, 0.22750385105609894, 0.008726594969630241, 0.0014759618788957596, 0.021012721583247185, 0.008827065117657185, 0.043097130954265594, 0.014008481055498123, 0.030657906085252762, -0.011109184473752975, -0.02022809349000454, -0.003997297026216984, 0.011252714321017265, 0.011434518732130527, 0.034638457000255585, -0.021318918094038963, 0.0008384516113437712, -0.07046342641115189, 0.012649734504520893, 0.08856727927923203, -0.023978998884558678, 0.008224241435527802, 0.05760318040847778, 0.0004267012991476804, -0.01952001452445984, -0.06322953850030899, 0.010984792374074459, 0.011951224878430367, -0.027040962129831314, 0.04627391695976257, 0.005678984802216291, -0.045814622193574905, 0.0021601193584501743, 0.04401572048664093, 0.02811264805495739, 0.03946105018258095, -0.012477499432861805, -0.008372555486857891, -0.02623719535768032, 0.002478276379406452, -0.017606288194656372, -0.007358280010521412, 0.002827531425282359, -0.05679941549897194, 0.012123459950089455, 0.007487456779927015, -0.025701353326439857, 0.013233421370387077, -0.0037269832100719213, -0.01347263716161251, 0.005473258905112743, -0.005774670746177435, -0.00390639528632164, -0.012142597697675228, 0.0416044220328331, 0.011731145903468132, 0.022773349657654762, -0.0128985196352005, 0.04470466077327728, 0.010649890638887882, 0.04857039079070091, 0.05036929249763489, -0.009836556389927864, -0.025452567264437675, -0.036188576370477676, 0.004547493532299995, 0.04317367821931839, -0.020591702312231064, -0.003250943496823311, 0.04822591692209244, 0.019175544381141663, 0.061583731323480606, 0.05469431355595589, -0.009539928287267685, 0.006186122074723244, 0.044666387140750885, -0.036513909697532654, 0.010133184492588043, 0.014343383722007275, 0.016592012718319893, -0.009071066044270992, 0.0021792566403746605, 0.029050374403595924, 0.013128167018294334, -0.048953134566545486, 0.016821660101413727, -0.0002173874236177653, 0.004092983435839415, 0.06610012799501419, 0.004970905836671591, -0.008492163382470608, -0.028495393693447113, 0.014668717049062252, -0.01261145994067192, -0.009573418647050858, -0.03354763239622116, -0.0060904361307621, 0.04975689947605133, -0.03498292714357376, -0.03460018336772919, -0.011932087130844593, 0.03754732385277748, 0.01031498797237873, 0.0559573769569397, -0.0329543761909008, -0.0005603631725534797, 0.00048052487545646727, 0.006296161562204361, 0.03569100797176361, -0.018831072375178337, 0.06154545769095421, -0.03530826047062874, -0.004394395276904106, -0.023596253246068954, -0.0010429811663925648, 0.01894589699804783, 0.07073134928941727, -0.0372602641582489, 0.014946207404136658, 0.044972583651542664, 0.013635304756462574, -0.03838936239480972, 0.04860866442322731, -0.018457897007465363, 0.022256644442677498, -0.03467673063278198, 0.004846513271331787, 0.028610218316316605, 0.016458051279187202, -0.015223697759211063, -0.00975522305816412, 0.012171302922070026, 0.00054660823661834, -0.04271438345313072, 0.01554903108626604, 0.010774282738566399, -0.003121766960248351, 0.044360190629959106, -0.02020895667374134, -0.02637115679681301, 0.002994982525706291, -0.007267377804964781, -0.011654596775770187, -0.006832004990428686, 0.0018670798745006323, 0.04355642572045326, 0.039652422070503235, -0.05580427870154381, -0.01676424778997898, -0.021873898804187775, 0.039231400936841965, -0.006865495350211859, 0.008482594974339008, 0.0036839242093265057, -0.022428879514336586, -0.003038041526451707, 0.0063248672522604465, -0.027174921706318855, -0.0037604733370244503, -0.0011213243706151843, -0.04118340462446213, -0.002595492172986269, 0.03366245701909065, 0.030811002478003502, 0.03708802908658981, -0.009152399376034737, 0.07536256313323975, 0.03557618334889412, 0.04206371679902077, -0.03775783255696297, -0.028457120060920715, -0.022849898785352707, 0.0007032946450635791, -0.015041893348097801, 0.08504602313041687, -0.0050474549643695354, -0.0026863941457122564, -0.042790934443473816, 0.0007230299524962902, -0.01253491174429655, -0.009468164294958115, 0.008559144102036953, 0.0016625502612441778, -0.012333969585597515, 0.049833450466394424, 0.008592633530497551, 0.006382279098033905, 0.004937415476888418, -0.023711076006293297, 0.024438293650746346, -0.028055235743522644, -0.00990353710949421, 0.10395364463329315, -0.03316488862037659, 0.0042747873812913895, 0.011109184473752975, 0.0030858847312629223, 0.027921276167035103, 0.03701147809624672, 0.03639908507466316, -0.05404364690184593, 0.016850365325808525, -0.05626356974244118, -0.02093617245554924, 0.011090047657489777, 0.01992189697921276, 0.049680352210998535, -0.03339453414082527, 0.0008659614250063896, -0.008142908103764057, -0.045699797570705414, -0.009645183570683002, -0.011960793286561966, -0.06395675241947174, 0.014955775812268257, -0.010008791461586952, -0.02378762513399124, 0.015539462678134441, 0.037930067628622055, 0.005932553671300411, 0.009530359879136086, 0.012171302922070026, -0.016429346054792404, 0.02994982711970806, -0.0315573550760746, -0.05955518037080765, -0.014171147719025612, 0.03410261496901512, -0.007755378261208534, 0.016745110973715782, -0.008339065127074718, -0.0010645105503499508, -0.01341522578150034, -0.00038005420356057584, 0.033356260508298874, 0.0040020812302827835, 0.03840849921107292, -0.013807539828121662, 0.008061574772000313, -0.01636236533522606, -0.022869037464261055, -0.043365053832530975, -0.005458905827254057, -0.007860633544623852, 0.011300557292997837, -0.013826676644384861, -0.025854451581835747, -0.03548049554228783, -0.02451484091579914, 0.028189197182655334, 0.053125061094760895, -0.0058081611059606075, -0.010161889716982841, 0.017118288204073906, -0.025471705943346024, 0.0058512198738753796, -0.013147303834557533, -0.014515618793666363, -0.010898674838244915, -0.036226850003004074, -0.003238982753828168, -0.010238438844680786, 0.045546699315309525, -0.00462882686406374, -0.018831072375178337, -0.046082545071840286, -0.03071531653404236, 0.02451484091579914, -0.01205647923052311, 0.010755144990980625, -0.05760318040847778, 0.029720179736614227, 0.01485052052885294, 0.016151854768395424, -0.060320671647787094, 0.020113270729780197, -0.0017905307468026876, -0.011778988875448704, 0.005181415472179651, -0.012927225790917873, -0.01675467938184738, -0.06265541911125183, 0.06541118770837784, -0.018008170649409294, 0.030179472640156746, -0.017108717933297157, 0.046120818704366684, 0.03771955892443657, -0.006463612895458937, 0.038791242986917496, -0.007985025644302368, 0.04864693805575371, 0.022199232131242752, -0.018716249614953995, -0.0034494928549975157, 0.05052239075303078, 0.03222716227173805, -0.0077027506195008755, 0.07876899838447571, -0.007726672571152449, 0.010716870427131653, -0.06786075979471207, 0.001851530745625496, 0.004951768554747105, -0.011855538003146648, -0.026275470852851868, -0.009563850238919258, -0.014094598591327667, -0.024055548012256622, 0.0558808259665966, 0.031040649861097336, 0.0025907077360898256, -0.04244646430015564, -0.003006943501532078, 0.002326374175027013, -0.004064277745783329, 0.027787314727902412, 0.02024723030626774, -0.043824344873428345, 0.010286281816661358, 0.004865650553256273, 0.01608487404882908, 0.0064875343814492226, -0.02769162878394127, -0.01325255911797285, -0.026715626940131187, -0.00451878784224391, -0.009798281826078892, -0.0031576494220644236, 0.016678130254149437, -0.04413054138422012, -0.07226233184337616, -0.05993792787194252, -0.027021823450922966, -0.0031337279360741377, -0.0006566475494764745, 0.008578280918300152, 0.021739937365055084, 0.030160335823893547, 0.007616633083671331, -0.010659459047019482, 0.06495188921689987, -0.060894791036844254, 0.0301029235124588, 0.00304761016741395, 0.015587305650115013, -0.012659303843975067, -0.03775783255696297, -0.009520791471004486, -0.007195613346993923, -0.023290056735277176, 0.027423705905675888, -0.014754834584891796, -0.012707146815955639, -0.015539462678134441, 0.00705686816945672, -0.015223697759211063, 0.01634322851896286, 0.052436117082834244, 0.014592167921364307, 0.01708958111703396, -0.00523404311388731, -0.018180405721068382, -0.015989188104867935, -0.032935239374637604, 0.014927069656550884, 0.015864796936511993, 0.000662029895465821, -0.03913571685552597, 0.016132717952132225, -0.05220647156238556, 0.0036121595185250044, 0.07398468255996704, -0.03798747807741165, -0.01662071794271469, -0.03961414843797684, 0.048264194279909134, 0.008291222155094147, -0.013941500335931778, 0.03125116229057312, 0.029471393674612045, -0.012831538915634155, -0.009377261623740196, 0.027787314727902412, -0.0387146957218647, 0.03069617971777916, -0.018036875873804092, -0.009520791471004486, 0.0005980396526865661, 0.035939790308475494, 0.06567911058664322, -0.017749818041920662, 0.023002997040748596, -0.04458983615040779, 0.0007660887786187232, -0.14720387756824493, 0.015845658257603645, 0.016142286360263824, 0.025758763775229454, -0.011654596775770187, -0.03984379395842552, -0.0034805908799171448, 0.004760395735502243, -0.02394072338938713, -0.060856517404317856, 0.03530826047062874, -0.010018360801041126, 0.004281964153051376, -0.01834307238459587, -0.019271230325102806, 0.019902760162949562, -0.053125061094760895, -0.04577634856104851, 0.01497491355985403, 0.04688630998134613, -0.009769575670361519, 0.024553116410970688, 0.018563151359558105, 0.04501085728406906, -0.00097779487259686, -0.03881038352847099, -0.019558290019631386, -0.03211233764886856, -0.01792205311357975, -0.011003930121660233, -0.014601736329495907, -0.06139235943555832, 0.05262748897075653, 0.05147925391793251, 0.024495704099535942, -0.02179734967648983, -0.003248551394790411, -0.014898364432156086, -0.01649632677435875, -0.015128010883927345, 0.034753281623125076, 0.017462758347392082, -0.028170060366392136, -0.001966354437172413, -0.04803454503417015, 0.09162924438714981, -0.01607530564069748, -0.001797707169316709, -0.013099460862576962, 0.017175698652863503, -0.02826574631035328, 0.06854970008134842, -0.01880236715078354, -0.0013120990479364991, 0.015845658257603645, 0.028016962110996246, -0.016314521431922913, -0.02064911276102066, 0.012152166105806828, 0.028170060366392136, -0.01305161789059639, 0.015003618784248829, -0.000488299410790205, -0.044360190629959106, 0.031078925356268883, -0.007908476516604424, -0.04125995188951492, -0.00044524052646011114, -0.045814622193574905, 0.038064029067754745, 0.0004909905837848783, 0.013204716145992279, -0.006214828230440617, -0.02970104105770588, -0.008319927379488945, 0.005138356704264879, -0.020017582923173904, 0.008123770356178284, -0.02049601450562477, -0.0034566691610962152, -0.006650201044976711, -0.01907985657453537, -0.02908864989876747, 0.0005941524286754429, 0.0272323340177536, -0.038657285273075104, -0.010917811654508114, 0.01852487586438656, -0.053431253880262375, 0.013730990700423717, -0.045240502804517746, -0.01764456182718277, -0.026179784908890724, -0.0016206874279305339, 0.002868198323994875, 0.017424482852220535, -0.055574629455804825, 0.03431312367320061, -0.029586218297481537, -0.038504187017679214, -0.02164425142109394, 0.03178700432181358, 0.022180095314979553, -0.021012721583247185, -9.635913738748059e-05, 0.04129822552204132, -0.008128555491566658, 0.008836634457111359, 0.022620251402258873, 0.035920653492212296, -0.03226543590426445, -0.040800657123327255, 0.008075927384197712, -0.01097522396594286, -0.04168097302317619, -0.012946362607181072, 0.02210354618728161, 0.0487234890460968, 0.010755144990980625, 0.03393037989735603, 0.042369913309812546, -0.0034638457000255585, 0.021031858399510384, -0.025490842759609222, -0.012467931024730206, 0.09048100560903549, 0.007205181755125523, 0.03601634129881859, 0.02954794280230999, -0.017826367169618607, 0.03125116229057312, -0.027481118217110634, -0.02091703563928604, 0.02612237259745598, -9.501355089014396e-05, 0.028514530509710312, -0.009884399361908436, 0.02552911639213562, 0.005267533473670483, -0.039231400936841965, -0.005004395730793476, -0.033203162252902985, -0.0037532970309257507, -0.04512568190693855, -0.03511688858270645, -0.004461375996470451, -0.0008420398226007819, 0.031174611300230026, -0.0272514708340168, -0.03574841842055321, -0.009209810756146908, 0.009334202855825424, 0.031614769250154495, 0.011979930102825165, 0.06349746137857437, 0.018840640783309937, -0.030734455212950706, -0.003961414564400911, 0.015529894270002842, 0.050713762640953064, 0.01183640118688345, -0.05036929249763489, -0.027002686634659767, -0.023691939190030098, -0.028016962110996246, 0.07635770738124847, 0.014793109148740768, 0.008760085329413414, -0.01621883548796177, 0.014927069656550884, 0.04485775902867317, 0.008621339686214924, -0.04723078012466431, -0.03069617971777916, -0.006463612895458937, -0.010343694128096104, 0.009712164290249348, 0.003511688904836774, 0.0018766485154628754, 0.0016589619917795062, 0.026064960286021233, 0.031882692128419876, -0.03814057633280754, 0.02411295846104622, -0.03396865352988243, -0.023041272535920143, -0.02568221464753151, 0.06012929975986481, 0.033471085131168365, -0.010056635364890099, -0.020591702312231064, -0.031078925356268883, 0.002152942819520831, -0.03494465351104736, 0.02769162878394127, 0.004222160205245018, 0.02970104105770588, 0.022180095314979553, -0.022180095314979553, 0.03379641845822334, -0.009989654645323753, 0.0036384733393788338, -0.01061161607503891, -0.021204093471169472, -0.013635304756462574, -0.01306118629872799, -0.023022135719656944, -0.007798437029123306, -0.018553582951426506, 0.040226541459560394, -0.00974087044596672, -0.032054927200078964, -0.029069511219859123, 0.05136442929506302, -0.004356120713055134, 0.002631374401971698, 0.04263783618807793, -0.02497413568198681, 0.042676109820604324, -0.024495704099535942, 0.01462087407708168, 0.02868676744401455, 0.031155474483966827, -0.04191061854362488, -0.022601114585995674, -0.02062997594475746, 0.008788790553808212, -0.025165509432554245, 0.0036408654414117336, 0.03282041847705841, -0.0007379809394478798, -0.04129822552204132, 0.01117616519331932, 0.035059478133916855, 0.024476567283272743, 0.05622529610991478, 0.01025757659226656, -0.0014484520070254803, -0.03957587108016014, -0.034198299050331116, 0.042216815054416656, -0.010812557302415371, 0.04489603266119957, -0.0026648647617548704]', 'distance': 0.6748250126838684}, {'title': 'Prompting Frameworks for Large Language Models: A Survey', 'authors': ['Xiaoxia Liu', 'Jingyi Wang', 'Jun Sun', 'Xiaohan Yuan', 'Guoliang Dong', 'Peng Di', 'Wenhai Wang', 'Dongxia Wang'], 'summary': 'Since the launch of ChatGPT, a powerful AI Chatbot developed by OpenAI, large\\nlanguage models (LLMs) have made significant advancements in both academia and\\nindustry, bringing about a fundamental engineering paradigm shift in many\\nareas. While LLMs are powerful, it is also crucial to best use their power\\nwhere \"prompt\\'\\' plays a core role. However, the booming LLMs themselves,\\nincluding excellent APIs like ChatGPT, have several inherent limitations: 1)\\ntemporal lag of training data, and 2) the lack of physical capabilities to\\nperform external actions. Recently, we have observed the trend of utilizing\\nprompt-based tools to better utilize the power of LLMs for downstream tasks,\\nbut a lack of systematic literature and standardized terminology, partly due to\\nthe rapid evolution of this field. Therefore, in this work, we survey related\\nprompting tools and promote the concept of the \"Prompting Framework\" (PF), i.e.\\nthe framework for managing, simplifying, and facilitating interaction with\\nlarge language models. We define the lifecycle of the PF as a hierarchical\\nstructure, from bottom to top, namely: Data Level, Base Level, Execute Level,\\nand Service Level. We also systematically depict the overall landscape of the\\nemerging PF field and discuss potential future research and challenges. To\\ncontinuously track the developments in this area, we maintain a repository at\\nhttps://github.com/lxx0628/Prompting-Framework-Survey, which can be a useful\\nresource sharing platform for both academic and industry in this field.', 'published': '2023-11-21T18:51:03+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2311.12785v1', 'vector': '[-0.0632309839129448, -0.013533518649637699, -0.004662632942199707, -0.03517596423625946, -0.02074766717851162, -0.05886894464492798, -0.0076242550276219845, -0.017867600545287132, -0.023226948454976082, -0.024680962786078453, -0.024848733097314835, -0.01726176030933857, -0.018725095316767693, 0.011893092654645443, 0.00039962094160728157, 0.004266506992280483, -0.0031829867511987686, -0.005317405331879854, -0.0019899492617696524, -0.020132508128881454, -0.009749352931976318, 0.010066254064440727, 0.04160718247294426, -0.0035371696576476097, -0.024438627064228058, 0.02031891979277134, -0.0439559742808342, -0.022164398804306984, -0.014102076180279255, 0.002472290536388755, 0.018510721623897552, 0.010895787738263607, 0.08619695901870728, -0.008691464550793171, -0.07497494667768478, -0.032845813781023026, 0.01779303513467312, -0.010038292035460472, -0.03786030039191246, -0.015453564003109932, -0.043881408870220184, -0.03918382525444031, -0.005256821401417255, -0.02883795276284218, -0.0024350080639123917, -0.013123412616550922, -0.03443031758069992, -0.030385173857212067, 0.043396737426519394, -0.020934078842401505, -0.03800942748785019, -0.014484221115708351, 0.03940751776099205, -0.008756708353757858, 0.004080094862729311, -0.0005059341201558709, -0.07680179178714752, -0.004716226365417242, -0.08299066871404648, 0.003164345631375909, -0.01504345703870058, 0.006347332149744034, 0.01104491762816906, -0.04209185391664505, 0.02031891979277134, 0.021195055916905403, -0.03129859268665314, 0.03347961604595184, -0.019759682938456535, -0.05405951291322708, 0.016767768189311028, 0.017979448661208153, -0.03629443794488907, -0.031764622777700424, -0.04596922546625137, 0.05323929712176323, 0.003781835548579693, -0.026265466585755348, -0.013794495724141598, 0.04272565618157387, -0.014269846491515636, -0.0069112288765609264, 0.03109353967010975, -0.03582840785384178, 0.00391465425491333, -0.005671588238328695, -0.0163483414798975, -0.0003151529817841947, 0.0011196377454325557, -0.0011965327430516481, -0.012312519364058971, 0.0012862435542047024, 0.01812857761979103, -0.03415069729089737, -0.07206691801548004, 0.005676248576492071, -0.027197526767849922, 0.018585287034511566, -0.019796965643763542, 0.01946142315864563, 0.010122177191078663, 0.018296347931027412, -0.008849915117025375, 0.0011732311686500907, 0.013626725412905216, -0.05185984820127487, -0.0006332186167128384, 0.014008870348334312, 0.015108701772987843, -0.018249744549393654, -0.00552245881408453, 0.03528781235218048, 0.021325545385479927, 0.055140700191259384, -0.014241885393857956, -0.023730261251330376, 0.021101851016283035, -0.03888556733727455, 0.030012348666787148, -0.038736436516046524, 0.03580976650118828, 0.023040536791086197, 0.08455653488636017, -0.015285792760550976, -0.018259065225720406, 0.008770689368247986, -0.006156259682029486, -0.02214575931429863, -0.0332372784614563, 0.007983098737895489, 0.020523972809314728, 0.016320379450917244, -0.03896012902259827, 0.0034556144382804632, -0.028297357261180878, -0.06009926274418831, -0.03057158552110195, -0.029098929837346077, -0.015416281297802925, -0.03441167622804642, 0.04623020440340042, 0.026675572618842125, -0.047162264585494995, -0.045708250254392624, 0.03249163180589676, -0.08291610330343246, 0.025072429329156876, 0.025594381615519524, -0.048504430800676346, 0.010075574740767479, 0.04902638494968414, 0.030273325741291046, -0.018790340051054955, 0.052530933171510696, 0.002106456784531474, -0.00381445768289268, -0.0455591194331646, 0.026340030133724213, 0.0010305094765499234, 0.038624588400125504, 0.004557776264846325, 0.03661133721470833, -0.015192586928606033, 0.022910047322511673, 0.021754292771220207, -0.021381469443440437, 0.021474674344062805, 0.0222576055675745, -0.021474674344062805, -0.04000403732061386, 0.030049631372094154, -0.0017149914056062698, 0.028185511007905006, 0.004376024007797241, -0.0040428126230835915, -0.0009093415574170649, 0.05130061134696007, 0.037133291363716125, 0.036592695862054825, -0.024475909769535065, -0.028148228302598, -0.007176866289228201, -0.0011132297804579139, -0.021511957049369812, -0.03388972207903862, 0.03008691407740116, -0.010364512912929058, -0.0777711346745491, 0.0158077459782362, -0.0146147096529603, -0.004350392613559961, -0.06144142895936966, 0.03746883198618889, 0.03881100192666054, 0.027067037299275398, -0.06956899911165237, 0.005485176108777523, -0.028931159526109695, -0.005331386346369982, -0.046304766088724136, -0.010578887537121773, 0.04895181953907013, -0.02294733002781868, 0.006734137423336506, -0.05827242508530617, -0.010616169311106205, -0.03500819206237793, -0.058570683002471924, 0.006696855183690786, -0.03666726127266884, 0.013253901153802872, 0.026507802307605743, 0.05383581668138504, 0.021381469443440437, 0.008514372631907463, 0.04600650817155838, -0.028017740696668625, 0.06054665148258209, 0.0072654117830097675, -0.04156989976763725, -0.030422456562519073, 0.0552152656018734, -0.0025957885663956404, 0.024419985711574554, 0.012685343623161316, 0.0037841657176613808, 0.04805704206228256, 0.008775349706411362, 0.015509487129747868, 0.007074339315295219, 0.02917349524796009, -0.006706175394356251, -0.024308139458298683, -0.03978034481406212, -0.004557776264846325, -0.029956426471471786, -0.0028357941191643476, -0.022127117961645126, 0.021306904032826424, 0.0024886017199605703, 0.0003608822007663548, -0.04902638494968414, 0.00120352313388139, 0.025724871084094048, -0.026246825233101845, -0.005517798475921154, 0.017084669321775436, 0.047646936029195786, 0.010765299201011658, 0.01399954967200756, 0.005517798475921154, -0.007871251553297043, -0.021325545385479927, -0.050219420343637466, 0.019330935552716255, 0.029881861060857773, 0.03437439352273941, -0.02160516381263733, -0.05282919108867645, -0.023021895438432693, 0.05100235342979431, 0.0337778739631176, 0.011026276275515556, 0.008854575455188751, 0.017615944147109985, 0.007060358766466379, -0.04354586824774742, -0.04712498188018799, 0.011855809949338436, 0.04268837347626686, -0.0353996604681015, -0.03636900335550308, 0.009553620591759682, -0.020244354382157326, -0.0028893877752125263, 0.022444017231464386, -0.01791420392692089, -0.026358671486377716, 0.06293272972106934, 0.031988319009542465, -0.022350812330842018, -0.0010223538847640157, 0.0364622101187706, 0.005895283073186874, 0.004296799190342426, -0.005694889929145575, -0.05003301054239273, -0.041047945618629456, -0.020952720195055008, -0.03586569055914879, -0.02011386677622795, 0.014745197258889675, 0.06893520057201385, -0.016124647110700607, -0.010457719676196575, 0.007242110557854176, -0.006487141363322735, -0.15084467828273773, 0.00019631524628493935, -0.035735201090574265, 0.01845479942858219, 0.007638236042112112, 0.02794317528605461, -0.043881408870220184, 0.029285341501235962, -0.011678718961775303, 0.0036047440953552723, -0.0033507575280964375, -0.04123435914516449, -0.004383014515042305, 0.02600448951125145, -0.06569162756204605, 0.0638275071978569, -0.04212913662195206, 0.00044389383401721716, 0.01504345703870058, -0.028595617040991783, 0.010905108414590359, -0.04589466005563736, 0.053276579827070236, 0.005280123092234135, 0.005117012187838554, -0.006543064955621958, 0.022537223994731903, 0.0013456624001264572, -0.00645917933434248, 0.021139133721590042, -0.034933630377054214, 0.016264457255601883, 0.004655642434954643, 0.022499941289424896, 0.011650756932795048, 0.051710717380046844, -0.011175406165421009, -0.025594381615519524, -0.04473890736699104, 0.001862955978140235, 0.01677708886563778, 0.06856237351894379, 0.030441097915172577, 0.02551981806755066, -0.005475855432450771, -0.020412126556038857, 0.0369841605424881, -0.02408444508910179, -0.014391014352440834, -0.013952946290373802, -0.019647836685180664, 0.0026913248002529144, -0.04671487584710121, 0.004215243738144636, -0.04626748338341713, -0.03817719966173172, -9.852171206148341e-05, 0.013626725412905216, -0.020300278440117836, -0.02874474786221981, 0.010485680773854256, 0.006845984607934952, 0.025445252656936646, -0.060994040220975876, -0.032566193491220474, -0.02723480947315693, 0.046453896909952164, -0.0010357522405683994, 0.006025771610438824, -0.04690128564834595, 0.034448958933353424, -0.025855358690023422, 0.014372373931109905, -0.009861200116574764, 0.04391869157552719, 0.03815855830907822, 0.04231555014848709, 0.023786185309290886, -0.009260021150112152, -0.07232789695262909, -0.00645917933434248, -0.010411116294562817, 0.0032901735976338387, 0.03869915381073952, -0.0321747288107872, -0.03314407169818878, 0.041532617062330246, 0.01056024618446827, 0.043247610330581665, 0.24427442252635956, 0.016870295628905296, -0.01504345703870058, -0.025333404541015625, -0.005932565312832594, 0.015938235446810722, -0.018482759594917297, 0.04895181953907013, 0.07657809555530548, -0.0337778739631176, 0.03472857549786568, 0.03907197713851929, 0.005247500725090504, 0.014996853657066822, 0.008034361526370049, 0.031130822375416756, -0.0504058338701725, -0.01394362561404705, 0.07553418725728989, -0.005573722068220377, 0.0075170681811869144, 0.02574351243674755, 0.014232564717531204, -0.050368551164865494, -0.06800314038991928, -0.013794495724141598, 0.042651090770959854, -0.009334586560726166, 0.0009681778610683978, 0.019386859610676765, -0.02697383239865303, 0.05886894464492798, 0.045335423201322556, 0.02011386677622795, 0.007004435174167156, 0.021847499534487724, 0.004275827668607235, -0.007027736399322748, -0.03500819206237793, 0.01551880780607462, -0.011156764812767506, -0.017690509557724, -0.06684738397598267, -0.012321840040385723, 0.0015798426466062665, -0.04354586824774742, 0.01726176030933857, -0.008994383737444878, 0.04205457121133804, -0.020356202498078346, -0.017177876085042953, -0.015966197475790977, -0.014950251206755638, 0.013039526529610157, 0.021101851016283035, 0.00635199248790741, -4.365393033367582e-05, -0.002605109242722392, 0.021250979974865913, 0.021847499534487724, 0.036275796592235565, 0.0020109205506742, -0.023599773645401, 0.007176866289228201, 0.0012874086387455463, 0.011417741887271404, -0.005741492845118046, -0.008435147814452648, 0.044105105102062225, 0.015956876799464226, 0.048989102244377136, 0.05238180235028267, 0.017765073105692863, -0.013020886108279228, 0.035511504858732224, -0.0691588893532753, 0.026731496676802635, 0.029937785118818283, -0.01908859983086586, 0.026526443660259247, -0.025874000042676926, 0.03566063567996025, 0.011697359383106232, -0.009935765527188778, 0.02412172593176365, 0.0009798286482691765, 0.036704543977975845, 0.0327153243124485, -0.027961816638708115, -0.003462604945525527, -0.02466232143342495, -0.03342369198799133, 0.03726378083229065, 0.0042222342453897, -0.017233800143003464, -0.0009949746308848262, 0.013151374645531178, -0.057079385966062546, -0.038251765072345734, -0.01385974045842886, 0.06639999151229858, -0.0042059230618178844, 0.025594381615519524, 0.0005831203889101744, 0.03640628606081009, 0.005624985322356224, 0.0092507004737854, 0.03306950628757477, 0.005485176108777523, 0.05506613478064537, -0.05152430757880211, 0.0031992977019399405, 0.014297808520495892, -0.03813991695642471, 0.004108056891709566, 0.05323929712176323, 0.025296123698353767, 0.023133741691708565, 0.039593931287527084, 0.018091294914484024, -0.023562490940093994, 0.05331386253237724, -0.0932806208729744, 0.0321560874581337, 0.02669421397149563, -0.057452213019132614, 0.020766308531165123, 0.020840873941779137, -0.04119707643985748, 0.007475125603377819, 0.006878606975078583, 0.01201426051557064, 0.01380381640046835, 0.05532711371779442, -0.011967657133936882, 0.006603648886084557, 0.010783940553665161, 0.019480064511299133, -0.00981459766626358, 0.029136212542653084, -0.047423239797353745, 0.026768779382109642, -0.002148399595171213, -0.020505331456661224, 0.040451426059007645, 0.042912065982818604, -0.055625371634960175, 0.041159793734550476, -0.003005895297974348, 0.02643323689699173, 0.01468927413225174, -0.002106456784531474, 0.051561590284109116, -0.046677593141794205, 0.013272542506456375, -0.045074447989463806, -0.02434542216360569, -5.4248837841441855e-05, 0.00620752340182662, -0.04865356162190437, -0.004215243738144636, 0.03368466719985008, 0.04160718247294426, 0.02755170874297619, -0.008919819258153439, -0.00909225083887577, -0.027682198211550713, 0.061851538717746735, -0.003422992303967476, -0.007740762550383806, -0.01625513657927513, -0.01865985244512558, -0.04488803446292877, 0.05599819868803024, -0.0007765229092910886, 0.008341941982507706, -0.02926670014858246, 0.010793261229991913, -0.06032295897603035, -0.013636046089231968, 0.013104771263897419, 0.0005408863653428853, -0.021567881107330322, 0.05320201441645622, -0.006533744279295206, -0.026638289913535118, -0.01871577650308609, -0.014735877513885498, 0.019983377307653427, 0.0054152715019881725, -0.04921279475092888, 0.10051340609788895, -0.05130061134696007, -0.010895787738263607, 0.03640628606081009, -0.011594832874834538, 0.08388544619083405, -0.03963121399283409, 0.002817152999341488, -0.056333739310503006, -0.014204602688550949, -0.03120538592338562, -0.022238964214920998, -0.011585512198507786, -0.047646936029195786, -0.01720583811402321, -0.009269341826438904, 0.006612969562411308, -0.035362377762794495, -0.042278267443180084, -0.03506411612033844, -0.04384412616491318, -0.035045474767684937, -0.00865884218364954, -0.009935765527188778, -0.0017942165723070502, -0.0027472483925521374, 0.04596922546625137, 0.0013002244522795081, 0.032733965665102005, 0.028055021539330482, -0.007806006819009781, -0.028073662891983986, -0.033591460436582565, -0.0015938235446810722, 0.018324309960007668, 0.010327231138944626, -0.016795730218291283, 0.03944480046629906, 0.029620883986353874, -0.00016558637435082346, -0.010439078323543072, -0.01112880278378725, -0.0009571096743457019, 0.0045601059682667255, 0.00451816339045763, 0.008011060766875744, 0.02246265858411789, -0.01934957690536976, -0.0032272597309201956, -0.009441773407161236, -0.011100840754806995, 0.010783940553665161, 0.04585737735033035, 0.001158667728304863, -0.015733182430267334, 0.006347332149744034, 0.018529362976551056, 0.03899741172790527, 0.029956426471471786, 0.00873806793242693, -0.005820718128234148, 0.012480290606617928, -0.014763838611543179, 0.03722649812698364, -0.019703758880496025, -0.013421671465039253, -0.005531779490411282, -0.030217403545975685, -0.011007634922862053, 0.006384614855051041, -0.011016955599188805, 0.01358944270759821, -0.0029802636709064245, -0.025650305673480034, -0.032081522047519684, -0.005816057790070772, -0.016544073820114136, -0.005764794535934925, -0.043508585542440414, -0.014875685796141624, 0.007838629186153412, 0.00321094854734838, -0.07374463230371475, 0.027477145195007324, -0.021996628493070602, -0.02020707167685032, 0.020188432186841965, -0.012648061849176884, 0.01024334505200386, -0.048392582684755325, 0.0520089790225029, 0.03629443794488907, 0.020896797999739647, -0.006985793821513653, 0.012741267681121826, 0.06226164475083351, -0.011166085489094257, 0.015164624899625778, -0.008169510401785374, 0.03662997856736183, 0.008719426579773426, -0.01951734721660614, -0.03452352061867714, 0.015006174333393574, 0.027421221137046814, -0.016329700127243996, 0.10371969640254974, 0.0022718976251780987, 0.008374564349651337, -0.02965816669166088, 0.04015316814184189, 0.019554629921913147, 0.024140367284417152, -0.04526086151599884, 0.021418750286102295, -0.003977568354457617, 0.0007240945124067366, 0.03715193271636963, 0.02628410793840885, -0.019796965643763542, 0.007242110557854176, 0.01351487822830677, 0.011911734007298946, 0.006631610915064812, 0.03398292884230614, 0.02488601580262184, -0.019927455112338066, 0.016217853873968124, 0.012992924079298973, 0.018249744549393654, 0.03342369198799133, -0.03575384244322777, -0.03014283813536167, -0.03955664858222008, -0.010849184356629848, -0.003875041613355279, -0.0021810217294842005, -0.01064413134008646, 0.013095450587570667, -0.035474225878715515, -0.05115148052573204, 0.0022963641677051783, -0.015108701772987843, -0.009963727556169033, -0.0025794776156544685, 0.017653226852416992, 0.0029406510293483734, -0.0010450729168951511, 0.007489106617867947, 0.05297832190990448, -0.035381019115448, -0.0007654547225683928, -0.02675013802945614, 0.050107575953006744, 0.011464344337582588, -0.0035441601648926735, 0.016711845993995667, -0.008323300629854202, -0.008113587275147438, 0.007577652111649513, -0.02622818388044834, -0.04857899621129036, -0.024699604138731956, 0.020449407398700714, -0.03286445513367653, -0.0514870248734951, 0.04086153581738472, 0.012461649253964424, 0.037562038749456406, -0.027533067390322685, 0.007386579643934965, 0.012722626328468323, 0.0012710975715890527, 0.027588991448283195, 0.0033740592189133167, 0.0006693359464406967, -0.03480314090847969, -0.01511802151799202, -0.03649948909878731, -0.0031433741096407175, 0.0423528291285038, -0.028595617040991783, -0.01753205806016922, -0.02417764998972416, 0.018622569739818573, -0.01187445130199194, 0.012564175762236118, 0.04895181953907013, -0.02846512943506241, -0.005326726008206606, 0.0010800251038745046, 0.02460639737546444, -0.03705872595310211, 0.017038065940141678, -0.007610274478793144, -0.02048669010400772, 0.022537223994731903, 0.03840089589357376, 0.051822565495967865, -0.003704940667375922, 0.009479056112468243, -0.036965519189834595, 0.02074766717851162, -0.1324271559715271, 0.012853114865720272, -0.005270802415907383, 0.0007258421392180026, -0.02643323689699173, -0.026899266988039017, -0.016599997878074646, -0.009404490701854229, -0.006967152468860149, -0.042017288506031036, 0.034504882991313934, 0.005648287013173103, 0.014307129196822643, -0.030366532504558563, 0.0029290001839399338, 0.03045973926782608, -0.06036024168133736, -0.019554629921913147, -0.006976473145186901, 0.045074447989463806, 0.00418495200574398, 0.004136018455028534, 0.048392582684755325, 0.014577426947653294, 0.012284558266401291, -0.004893317818641663, -0.0052008978091180325, -0.08082828670740128, -0.016366982832551003, 0.020132508128881454, -0.02031891979277134, -0.10066253691911697, 0.04175631329417229, 0.04492531716823578, -0.0007404055795632303, -0.02348792552947998, 0.0019223748240619898, -0.038624588400125504, -0.015332396142184734, -0.03463536873459816, 0.002362773520871997, 0.007260751444846392, -0.015453564003109932, 0.017410891130566597, -0.04119707643985748, 0.08880672603845596, 0.0009472065139561892, -0.012601458467543125, -0.042651090770959854, -0.005168275907635689, 0.005438573192805052, 0.01907927915453911, -0.02820415236055851, 0.010401795618236065, 0.03019876219332218, -0.006706175394356251, -0.043247610330581665, -0.0227982010692358, -0.0036606676876544952, 0.04585737735033035, -0.04980931431055069, 0.052903756499290466, -0.038251765072345734, -0.012349802069365978, -0.027141602709889412, -0.012526893988251686, -0.036816392093896866, -0.0009681778610683978, -0.043135762214660645, 0.020020660012960434, -0.020039301365613937, 0.01077461987733841, -0.016320379450917244, -0.004117377568036318, -0.008216113783419132, -0.017998088151216507, 0.02991914376616478, -0.012247275561094284, -0.02214575931429863, 0.003970577847212553, -0.043620433658361435, -0.012098145671188831, -0.03239842504262924, 0.013561480678617954, 0.04966018721461296, -0.015462884679436684, 0.01455878559499979, -0.0008773019653744996, -0.07404288649559021, 0.024252215400338173, -0.03608938306570053, -0.03200696036219597, -0.031503647565841675, -0.016497472301125526, 0.00013150791346561164, 0.02268635295331478, -0.04309847950935364, 0.0009343907004222274, 0.016963502392172813, -0.022425375878810883, -0.03828904777765274, 0.05185984820127487, -0.017252441495656967, 0.026675572618842125, 0.019219087436795235, 0.07381919771432877, 0.004266506992280483, -0.0036350360605865717, 0.028222793713212013, 0.012769229710102081, -0.04175631329417229, -0.026246825233101845, 0.030497020110487938, 0.015751823782920837, -0.06677281856536865, -0.017093989998102188, 0.03334912657737732, 0.03191375359892845, 0.0017988767940551043, 0.031279951333999634, 0.008593598380684853, 0.01747613586485386, -0.01871577650308609, -0.02771948091685772, 0.008006400428712368, 0.03623851388692856, 0.04910095036029816, 0.017504096031188965, 0.028856594115495682, 0.0026004489045590162, 0.0016136298654600978, 0.015462884679436684, -0.007325995713472366, 0.02386074885725975, 0.012722626328468323, 0.015313754789531231, 0.02059853821992874, 0.03491498902440071, -0.02423357404768467, -0.013747893273830414, -0.0557745024561882, -0.0048653557896614075, -0.022388095036149025, -0.03003099001944065, -0.015295113436877728, 0.00828135758638382, -0.008327960968017578, -0.006277427542954683, -0.012098145671188831, -0.03275260701775551, 0.030049631372094154, -0.01705670729279518, 0.01774643175303936, 0.005979168228805065, 0.030683433637022972, 0.018650531768798828, -0.048616278916597366, 0.0016206202562898397, 0.004669623449444771, 0.07161953300237656, 0.027868609875440598, -0.004930600058287382, -0.0268060602247715, -0.049250077456235886, -0.022481299936771393, 0.04540998861193657, 0.00941381137818098, -0.0015681918011978269, -0.04156989976763725, 0.04224098473787308, -0.011939695104956627, 0.012676022946834564, -0.034989554435014725, 0.014279167167842388, 0.006762098986655474, -0.0268060602247715, -0.0021845169831067324, 0.025277482345700264, 0.04205457121133804, 0.009292643517255783, 0.019386859610676765, 0.020710384473204613, 0.0048653557896614075, 0.011538909748196602, -0.012648061849176884, -0.002912689233198762, 0.00399620970711112, 0.0407496877014637, 0.024140367284417152, 0.00652908394113183, -0.008626220747828484, -0.017783714458346367, 0.011949015781283379, -0.005499157123267651, 0.03569791838526726, 0.00015976099530234933, 0.03888556733727455, -0.0025981187354773283, -0.01576114445924759, 0.024159008637070656, -0.0292294193059206, 0.02492329850792885, 0.009190117008984089, -0.005345367360860109, -0.0036979501601308584, 0.011305894702672958, -0.046565745025873184, -0.03275260701775551, -0.0227795597165823, -0.022593148052692413, 0.002451319247484207, 0.007489106617867947, -0.03497091308236122, 0.029136212542653084, 0.030161479488015175, 0.004462239798158407, 0.017876921221613884, -0.004122037906199694, -0.017308363690972328, 0.0009442938608117402, 0.02803638018667698, 0.01235912274569273, 0.018268385902047157, -0.04455249384045601, -0.05558808892965317, -0.005909263622015715, -0.005438573192805052, 0.028875235468149185, 0.008057663217186928, 0.016394944861531258, 0.030012348666787148, 0.002770549850538373, -0.0021367487497627735, 0.0391465425491333, 0.02846512943506241, 0.006324030924588442, 0.01413003820925951, 0.009525658562779427, -0.03526917099952698, -0.04701313376426697, 0.04156989976763725, -0.028931159526109695, 0.05297832190990448, 0.015947556123137474]', 'distance': 0.67873615026474}, {'title': 'Large Language Model Prompt Chaining for Long Legal Document Classification', 'authors': ['Dietrich Trautmann'], 'summary': 'Prompting is used to guide or steer a language model in generating an\\nappropriate response that is consistent with the desired outcome. Chaining is a\\nstrategy used to decompose complex tasks into smaller, manageable components.\\nIn this study, we utilize prompt chaining for extensive legal document\\nclassification tasks, which present difficulties due to their intricate\\ndomain-specific language and considerable length. Our approach begins with the\\ncreation of a concise summary of the original document, followed by a semantic\\nsearch for related exemplar texts and their corresponding annotations from a\\ntraining corpus. Finally, we prompt for a label - based on the task - to\\nassign, by leveraging the in-context learning from the few-shot prompt. We\\ndemonstrate that through prompt chaining, we can not only enhance the\\nperformance over zero-shot, but also surpass the micro-F1 score achieved by\\nlarger models, such as ChatGPT zero-shot, using smaller models.', 'published': '2023-08-08T08:57:01+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2308.04138v1', 'vector': '[-0.08188541233539581, -0.008327407762408257, -0.0006219116039574146, -0.009265447966754436, 0.0012852073414251208, -0.013638372533023357, -0.023947620764374733, 0.0058259665966033936, -0.030973726883530617, -0.024499408900737762, -0.06319817155599594, -0.05528920516371727, -0.02889532409608364, 0.004025756847113371, -0.03218766301870346, -0.0007069790153764188, -0.00543971499428153, -0.013730336911976337, 0.017804374918341637, -0.013905069790780544, 0.003680889029055834, 0.03553517907857895, 0.012461223639547825, 0.027497459203004837, 0.007159455679357052, 0.006593872327357531, -0.02363494038581848, -0.03757679462432861, -0.03250034153461456, -0.00384182739071548, 0.013923462480306625, -9.965242497855797e-05, 0.03972877189517021, -0.03356713429093361, -0.03411892056465149, -0.016847942024469376, 9.778439562069252e-05, -0.0027589425444602966, -0.020508138462901115, -0.006428335793316364, -0.012065774761140347, 0.011651933193206787, 0.02551102079451084, -0.0606231614947319, -0.009270046837627888, -0.0009357412927784026, 0.01362917572259903, -0.038661979138851166, -0.022899221628904343, -0.024499408900737762, -0.023837262764573097, 0.01593749038875103, 0.02378208376467228, -0.024260301142930984, 0.025676557794213295, 0.00789057556539774, -0.028325142338871956, 0.02236582711338997, -0.07879539579153061, 0.0028509071562439203, 0.007380171213299036, 0.0026186963077634573, 0.007720440626144409, -0.0412369929254055, 0.0033636107109487057, 0.007159455679357052, -0.050507038831710815, 0.03757679462432861, -0.00586275290697813, -0.053155623376369476, -0.012746314518153667, 0.022145111113786697, -0.06290388852357864, -0.03185658901929855, -0.04944024980068207, 0.0366203635931015, -0.014079802669584751, -0.014033820480108261, -0.039434485137462616, 0.006230611819773912, -0.02381886914372444, -0.0005598354036919773, 0.003864818485453725, -0.004011962097138166, -0.012681938707828522, -0.013840694911777973, -0.006607667077332735, 0.02254975587129593, 0.00875504408031702, 0.04252449795603752, -0.018724022433161736, 0.0016151309246197343, 0.027313530445098877, -0.05304526537656784, -0.022512970492243767, 0.004726988263428211, -0.03796304762363434, 0.0031474935822188854, 0.0017760691698640585, 0.005136230960488319, 0.008888392709195614, 0.019220631569623947, 0.0070399013347923756, 0.012589974328875542, 0.00252673146314919, -0.0630878135561943, 0.0439959354698658, 0.04565130174160004, -0.007996334694325924, -0.017096247524023056, -0.006469720043241978, 0.029851756989955902, 0.007164054084569216, 0.03983912989497185, 0.005218999460339546, 0.003919997252523899, 0.007113473489880562, -0.03735608235001564, 0.02611798793077469, -0.030716225504875183, 0.021961182355880737, 0.016443297266960144, 0.07872182130813599, -0.050323110073804855, -0.01440167985856533, -0.019606884568929672, 0.019698848947882652, 0.03525928407907486, -0.016103027388453484, -0.00010669347830116749, 0.01044719573110342, 0.04458450898528099, -0.03036675974726677, 0.008286023512482643, -0.04668130725622177, -0.04848381504416466, -0.020195458084344864, 0.014548823237419128, -0.013196941465139389, -0.03914019837975502, 0.07206358015537262, 0.043149858713150024, -0.04362807795405388, -0.03709857910871506, 0.04120020568370819, -0.058599937707185745, 0.028968894854187965, 0.02117028459906578, 0.015578828752040863, -0.06912070512771606, 0.01920223981142044, 0.04086913540959358, 0.0421566404402256, 0.0378526896238327, 0.05212561786174774, -0.03031158074736595, -0.033125702291727066, 0.007619279436767101, 0.006865168455988169, 0.02464655227959156, -0.01367515791207552, 0.0329233780503273, -0.0034877629950642586, 0.007003115490078926, 0.020820818841457367, 0.009435582906007767, 0.004216583911329508, 0.05554670840501785, -0.031341586261987686, -0.06334532052278519, 0.017068658024072647, -0.039986271411180496, 0.015946688130497932, 0.0033038335386663675, 0.04120020568370819, 0.0017024973640218377, 0.04351771995425224, 0.024775303900241852, 0.01845732517540455, -0.029667828232049942, 0.03899305313825607, 0.008819418959319592, 0.0005095421802252531, -0.01486150361597538, -0.028196392580866814, 0.0145396264269948, 0.02560298517346382, -0.046754878014326096, 0.00480055995285511, -0.015827132388949394, -0.011284074746072292, -0.05091168358922005, 0.029299968853592873, 0.012930243276059628, 0.006014494691044092, -0.05867350846529007, 0.010162104852497578, -0.03719054535031319, 0.006511104293167591, -0.011569165624678135, -0.024297086521983147, 0.02551102079451084, -0.011707112193107605, -0.008690668269991875, -0.04612951725721359, -0.009950585663318634, -0.06113816425204277, -0.05241990461945534, -0.006092664785683155, 0.00331532908603549, 0.04256128519773483, 0.003680889029055834, 0.02597084455192089, 0.041273780167102814, 0.0007707795593887568, 0.01609383150935173, -0.035995002835989, 0.042818788439035416, 0.0218508243560791, -0.04329700395464897, -0.0023014177568256855, 0.03465231880545616, 0.02264172025024891, 0.02418672852218151, 0.025547806173563004, -0.012838278897106647, 0.03698822110891342, -0.005651233717799187, 0.008957366459071636, -0.012157740071415901, 0.014705163426697254, 0.008281425572931767, -0.025547806173563004, -0.014079802669584751, -0.047711312770843506, -0.04406950622797012, 0.01929420419037342, 0.026265131309628487, 0.0021209369879215956, -0.028656216338276863, 0.02039778046309948, -0.043149858713150024, 0.03259230777621269, 0.0002691405825316906, -0.04568808525800705, -0.07265215367078781, -0.018788397312164307, 0.005320160649716854, 0.017096247524023056, -0.021096713840961456, -0.013040601275861263, -0.016130616888403893, -0.04443736746907234, -0.0056926179677248, -0.0048465421423316, -0.024996018037199974, 0.03067944012582302, -0.015744365751743317, -0.03371427580714226, -0.01685713790357113, 0.035185713320970535, 0.030293188989162445, 0.005596054717898369, 0.007748030126094818, 0.008336604572832584, -0.0046557155437767506, -0.034413207322359085, 0.01056674961000681, -0.01186345238238573, 0.06032887473702431, -0.02547423541545868, -0.013049798086285591, 0.0049844891764223576, -0.011173716746270657, -0.004299351945519447, 0.0640074610710144, -0.05606171116232872, -0.031084084883332253, 0.08048754930496216, 0.01769401691854, -0.07489608973264694, 0.01944134756922722, 0.036877863109111786, -0.032169267535209656, 0.015164987184107304, -0.03364070504903793, -0.03542482107877731, -0.03266587853431702, -0.002391083398833871, -0.05411205813288689, 0.004910917486995459, -0.00764686893671751, 0.06481675803661346, -0.00590873509645462, -0.02532709203660488, -0.0009276943746954203, -0.020839212462306023, -0.14140500128269196, -0.023359045386314392, -0.007605484686791897, -0.006612265482544899, 0.01873321831226349, 0.04237735643982887, -0.042414143681526184, 0.002108291955664754, -0.007876780815422535, 0.037521615624427795, 0.025051197037100792, -0.050139181315898895, 0.00998737197369337, -0.004393615759909153, -0.05083811283111572, 0.023248689249157906, -0.029962114989757538, 0.009527548216283321, 0.01150478981435299, -0.002632490824908018, -0.009155090898275375, -0.07695610076189041, 0.005894940346479416, -0.008805624209344387, -0.01566159725189209, -0.003662496106699109, -0.00589953875169158, 0.023377439007163048, -0.02867460809648037, 0.01753767766058445, -0.022936008870601654, -0.012350865639746189, 0.0057569933123886585, 0.02643066830933094, -0.012921047396957874, 0.0036670942790806293, -0.026596205309033394, 0.031709443777799606, -0.029943721368908882, 0.03389820456504822, 0.04532022774219513, 0.06316138803958893, -0.009251653216779232, 0.02350618876516819, -0.03503856807947159, -0.01812625117599964, 0.04682844877243042, -0.021243857219815254, 0.0012208319967612624, -0.025216734036803246, -0.012258901260793209, -0.004823551047593355, -0.009237859398126602, 0.008299818262457848, -0.05080132558941841, -0.014889093115925789, 0.006235209759324789, -0.00841477420181036, -0.012479616329073906, -0.004126918036490679, -0.00011337528849253431, -0.04274521395564079, 0.0403173454105854, -0.034321244806051254, -0.02876657247543335, -0.033677492290735245, 0.06536854058504105, 0.012350865639746189, 0.001819752505980432, -0.04741702601313591, 0.05157383158802986, -0.07004035264253616, -0.010805858299136162, -0.02208993211388588, 0.02620995230972767, 0.04756416752934456, 0.018595272675156593, 0.0013760225847363472, -0.009564333595335484, -0.09777691960334778, -0.01695830002427101, -0.007003115490078926, 0.0032463555689901114, 0.03375106304883957, -0.019588490948081017, -0.033953383564949036, 0.03211408853530884, 0.03614214435219765, 0.04403272271156311, 0.2363126128911972, 0.01634213514626026, -0.009518351405858994, -0.06632497906684875, 0.06720783561468124, 0.014815520495176315, -0.008194059133529663, 0.032169267535209656, 0.043922364711761475, -0.022531364113092422, 0.05131632834672928, 0.07997254282236099, 0.03036675974726677, 0.050874900072813034, -0.005076454021036625, -0.0017519284738227725, -0.053670626133680344, 0.010125318542122841, 0.06901034712791443, -0.019698848947882652, 0.0030118455179035664, 0.023083152249455452, 0.04333379119634628, -0.04171520844101906, -0.013095780275762081, -0.05113239958882332, 0.007076687179505825, -0.0011886443244293332, 0.009366609156131744, 0.007435349747538567, -0.0044694868847727776, 0.05889422446489334, 0.046754878014326096, 0.025492629036307335, 0.00014426969573833048, 0.026412276551127434, -0.01152318250387907, 0.020140279084444046, -0.028840145096182823, -0.00531556224450469, -0.039802342653274536, -0.008667677640914917, -0.03399017080664635, 0.00038424020749516785, 0.0005448911106213927, -0.05311883985996246, 0.002109441440552473, 0.01273711770772934, 0.015946688130497932, -0.02895050309598446, -0.00039516104152426124, -0.0005966213066130877, -0.0290792528539896, 0.028086034581065178, 0.02341422438621521, 0.01275551039725542, -0.04289235919713974, 0.019239025190472603, -0.015578828752040863, 0.032224446535110474, 0.03522249683737755, 0.009109108708798885, 0.0025704146828502417, 0.008713659830391407, -0.010704697109758854, 0.021887609735131264, -0.0007253719377331436, -0.022623328492045403, 0.07607323676347733, 0.009279242716729641, 0.023027973249554634, 0.03836769238114357, 0.010759875178337097, -0.026007629930973053, 0.01007933635264635, -0.044694866985082626, 0.0455409437417984, 0.013546407222747803, 0.002391083398833871, -0.0030463323928415775, -0.013951051980257034, 0.007968745194375515, 0.02839871495962143, -0.0015300634549930692, 0.011780683882534504, 0.0014576412504538894, 0.014364893548190594, 0.04417986422777176, -0.02735031582415104, -0.005274178460240364, -0.04682844877243042, -0.044878795742988586, -0.024664945900440216, -0.0154592739418149, -0.019312597811222076, 0.04837345704436302, 0.01867804117500782, -0.012792296707630157, -0.021409394219517708, 0.004703996703028679, 0.02665138430893421, 0.004933908581733704, 0.045614514499902725, -0.048998817801475525, 0.056503139436244965, -0.0332360602915287, -0.019239025190472603, 0.009099911898374557, -0.026173166930675507, 0.03178301826119423, -0.061248522251844406, 0.011569165624678135, 0.019717242568731308, -0.04252449795603752, 0.02117028459906578, 0.09255332499742508, -0.003368208883330226, 0.016296153888106346, 0.042965929955244064, 0.024113157764077187, -0.03895626589655876, 0.07702967524528503, -0.04237735643982887, 0.01275551039725542, 0.026375489309430122, -0.029667828232049942, 0.04009662941098213, -0.004641920793801546, -0.03663875535130501, 0.014337304048240185, 0.03463392332196236, 0.009030938148498535, -0.036749113351106644, 0.02039778046309948, 0.0050856503657996655, -0.026412276551127434, 0.05208883434534073, 0.0025451243855059147, -0.024499408900737762, 0.017206603661179543, -0.049550607800483704, -0.0065754796378314495, -0.012378455139696598, 0.023892441764473915, 0.03461553156375885, 0.05120597034692764, -0.05874708294868469, -0.004993685986846685, -0.005232794210314751, 0.027681389823555946, -0.005743198562413454, -0.006285790354013443, 0.02264172025024891, -0.05451670289039612, 0.0054810987785458565, -0.056723855435848236, -0.00967469159513712, -0.006855972111225128, -0.05223597586154938, -0.03625250235199928, 0.022604934871196747, 0.02323029562830925, -0.004451093729585409, 0.011238092556595802, -0.009711476974189281, -0.01642490364611149, 0.020636890083551407, 0.0658467561006546, -0.020177066326141357, -0.003929194062948227, -0.016774369403719902, 0.014282125048339367, -0.013500425033271313, 0.05628242716193199, -0.03406374156475067, 0.03577428683638573, -0.046718090772628784, 0.0015174183063209057, -0.023064758628606796, -0.001663412433117628, 0.013711944222450256, -0.011348449625074863, -0.014925878494977951, 0.04476843774318695, -0.019275810569524765, 0.01916545443236828, -0.03185658901929855, -0.02963104099035263, 0.04256128519773483, 0.005416723899543285, -0.04377521947026253, 0.11940702795982361, -0.03888269513845444, -0.01760205253958702, 0.06231531500816345, -0.005058061331510544, 0.09049331396818161, 0.007623877841979265, 0.011136931367218494, -0.040611632168293, -0.010769071988761425, -0.00909071508795023, 0.0025152359157800674, 0.015588024631142616, -0.016746779903769493, 0.014070606790482998, 0.019496526569128036, 0.00887919683009386, 0.006823784206062555, 0.007830798625946045, 0.003241757396608591, -0.022880829870700836, -0.04664452001452446, -0.008207853883504868, -0.0054305181838572025, -0.009030938148498535, 0.028012461960315704, 0.014594805426895618, 0.020360995084047318, 0.030440332368016243, 0.03393499180674553, -0.02972300723195076, -0.0008196358103305101, -0.0253822710365057, -0.014898288995027542, 0.007761824876070023, -0.01907348819077015, -0.01667320914566517, -0.014852306805551052, 0.009702281095087528, -0.0028279160615056753, -0.037245724350214005, -0.035829465836286545, -0.009748263284564018, -0.004083234816789627, 0.012516402639448643, -0.006474318448454142, 0.02959425561130047, 0.008548123762011528, 0.01833777129650116, -0.02812281996011734, 0.014337304048240185, 0.046754878014326096, 0.05131632834672928, -0.020692069083452225, -0.015229362063109875, -0.0018496409757062793, -0.020452959463000298, 0.024297086521983147, -0.001946203992702067, 0.009858621284365654, -0.032445162534713745, 0.017712410539388657, -0.03792626038193703, 0.045246656984090805, -0.013160155154764652, 0.0009708028519526124, 0.016995085403323174, -0.02620995230972767, -0.050323110073804855, 0.010888625867664814, 0.001131166354753077, 0.005196008365601301, -0.031084084883332253, -0.009637905284762383, -0.07316715270280838, 0.02227386273443699, -0.039618413895368576, -0.004653416108340025, -0.05330276861786842, 0.014649984426796436, -0.00406254269182682, 0.0009909201180562377, -0.06547889858484268, 0.03027479536831379, -0.014512036927044392, -0.023708513006567955, 0.015670793130993843, 0.04421665146946907, -0.0163053497672081, -0.044474150985479355, 0.05639278516173363, 0.012488813139498234, 0.03654678910970688, 0.009775852784514427, -0.010695500299334526, 0.06021851673722267, 0.005182213615626097, 0.030329974368214607, -0.01652606576681137, -0.001016785274259746, 0.047085952013731, -0.015220166184008121, 0.006777802016586065, -0.007651466876268387, 0.0253822710365057, -0.02433387190103531, 0.06080709025263786, -0.007324992213398218, -0.002162321237847209, -0.03284980729222298, 0.018006697297096252, -0.018999917432665825, 0.006580077577382326, -0.041494496166706085, 0.02953907661139965, 0.012038185261189938, -0.006235209759324789, 0.06279353052377701, 0.02812281996011734, -0.023248689249157906, -0.050323110073804855, -0.02387404814362526, -0.012562384828925133, 0.01794232241809368, 0.03590303659439087, 0.045246656984090805, -0.021519750356674194, 0.0016691602068021894, 0.020875997841358185, -0.01888955943286419, 0.0007684804149903357, -0.01372114010155201, -0.0201586727052927, -0.011964613571763039, 0.01636972464621067, 0.05863672494888306, -0.0014231544919312, -0.008718257769942284, -0.007734235376119614, -0.03836769238114357, -0.0609174482524395, -0.017979107797145844, -0.006773203611373901, 0.024205122143030167, 0.011486397124826908, 0.024591373279690742, 0.006124852225184441, 0.03213248401880264, -0.039213769137859344, 0.06669283658266068, -0.040611632168293, 0.050323110073804855, -0.023211902007460594, 0.047858454287052155, -0.03822055086493492, -0.028269963338971138, -0.023156723007559776, 0.006046682130545378, 0.004676407668739557, 0.017224997282028198, -0.019717242568731308, -0.03196694701910019, -0.00590873509645462, 0.021427785977721214, -0.014502841047942638, -0.0022037052549421787, 0.044842012226581573, 0.01975402794778347, 0.013058993965387344, -0.011872649192810059, 0.004777568858116865, -0.04009662941098213, -0.046938806772232056, 0.0335303470492363, 0.027460673823952675, -0.005177615210413933, -0.03833090886473656, -0.005540876183658838, -0.03695143759250641, 0.007182446774095297, 0.021924395114183426, -0.0097666559740901, -0.007076687179505825, -0.004087833222001791, 0.015440881252288818, -0.019827600568532944, -0.039397697895765305, 0.00547650083899498, -0.009490761905908585, 0.01787794753909111, -0.024959232658147812, 0.0023094648495316505, -0.018411342054605484, 0.01916545443236828, -0.01993795670568943, -0.020287424325942993, 0.0027750362642109394, 0.020931176841259003, 0.04344414547085762, -0.016811156645417213, 0.01109094824641943, -0.027129599824547768, 0.03266587853431702, -0.13743211328983307, 0.024444229900836945, 0.01092541217803955, 0.003931493032723665, -0.012507205829024315, -0.017712410539388657, -0.010254069231450558, -0.023929227143526077, -0.014962664805352688, -0.03410052880644798, 0.026320310309529305, -0.016903121024370193, 0.050102394074201584, -0.04480522498488426, -0.030256401747465134, 0.039213769137859344, -0.014594805426895618, -0.0016473185969516635, -0.005490295588970184, 0.04160485044121742, -0.0032624495215713978, 0.016783567145466805, 0.0011294420110061765, -0.0029888544231653214, -0.06128530949354172, -0.005200606305152178, -0.03525928407907486, -0.06606747210025787, -0.014282125048339367, -0.02611798793077469, 0.018466521054506302, -0.02963104099035263, 0.05021275207400322, 0.015054629184305668, -0.004274061881005764, 0.022145111113786697, 0.019809206947684288, -0.012635956518352032, -0.010759875178337097, -0.013849890790879726, 0.021243857219815254, 0.005421321839094162, -0.017574463039636612, 0.010162104852497578, -0.014024623669683933, 0.04576165974140167, 0.0056926179677248, 0.001554204267449677, -0.057275645434856415, -0.020195458084344864, 0.0019358579302206635, 0.040905918926000595, -0.06231531500816345, -0.011946220882236958, -0.00299575156532228, -0.0019151659216731787, -0.08262112736701965, -0.0136935506016016, -0.0035452409647405148, 0.042965929955244064, -0.03987591341137886, 0.03101051226258278, -0.043738435953855515, 0.002287623006850481, 0.013813105411827564, 0.005094847176223993, -0.024407444521784782, 0.007577895186841488, -0.027037635445594788, 0.05962994322180748, -0.028564250096678734, 0.011973810382187366, -0.03031158074736595, -0.026853706687688828, -0.013252120465040207, 0.004759175702929497, 0.004975292831659317, -0.021115105599164963, -0.0026163971051573753, -0.0037544609513133764, -0.013932659290730953, 0.02208993211388588, -0.039434485137462616, 0.018944738432765007, 0.033769454807043076, -0.03250034153461456, 0.005320160649716854, 0.008796428330242634, -0.07769181579351425, -0.010254069231450558, -0.006290388759225607, -0.018282592296600342, -0.027276745066046715, -0.02944711223244667, 0.021151892840862274, -0.022936008870601654, -0.04042770341038704, 0.03465231880545616, 0.0045660496689379215, -0.03485463932156563, -0.0273871012032032, 0.038110192865133286, -0.0017001982778310776, 0.04256128519773483, 0.026265131309628487, 0.06249924376606941, 0.03251873329281807, -0.002549722557887435, 0.018438931554555893, 0.009026340208947659, 0.021207071840763092, -0.001037477282807231, 0.02405797876417637, -0.00120933644939214, -0.07563181221485138, -0.011513986624777317, 0.016139812767505646, 0.030532296746969223, 0.02163010835647583, 0.04171520844101906, 0.02729513682425022, 0.009297636337578297, 0.0009236709447577596, -0.02273368649184704, 0.02190600335597992, 0.03123122826218605, 0.04855738580226898, 0.008773436769843102, -0.04947703331708908, -0.023984406143426895, 0.010042550042271614, -0.008543524891138077, -0.027994070202112198, 0.03305213153362274, 0.018025090917944908, 0.03524089232087135, 0.01929420419037342, 0.03695143759250641, -0.04756416752934456, -0.019625278189778328, -0.01410739216953516, -0.024701731279492378, -0.017951518297195435, -0.008975759148597717, -0.01106335874646902, -0.011605951003730297, 0.028821751475334167, 0.019993135705590248, -0.014282125048339367, -0.018576879054307938, 0.030569082126021385, 0.02670656330883503, 0.018944738432765007, 0.013132565654814243, 0.006184629164636135, 0.016719190403819084, -0.02766299620270729, -0.009831031784415245, -0.025087982416152954, 0.07408680021762848, 0.004115422256290913, -0.019606884568929672, -0.03086336888372898, 0.003269346896559, -0.04951382055878639, 0.04359129071235657, -0.004897122737020254, -0.028968894854187965, -0.02895050309598446, 0.02541905641555786, 0.02762621082365513, 0.016323743388056755, -0.04796881228685379, -0.04697559401392937, -0.005996101535856724, 0.020508138462901115, -0.0017726204823702574, 0.03463392332196236, 0.03036675974726677, -0.017684821039438248, 0.024664945900440216, 0.006570881232619286, -0.034964997321367264, -0.011854256503283978, -0.010364427231252193, 0.03851483762264252, 0.0026761742774397135, 0.05668707191944122, 0.023524582386016846, -0.012387651950120926, 0.007256018463522196, -0.019956350326538086, 0.022053146734833717, -0.0009386152378283441, 0.018540093675255775, 0.028067640960216522, 0.034137316048145294, 0.03279462829232216, -0.028104426339268684, 0.052750978618860245, -0.02012188732624054, 0.020011529326438904, -0.006764007266610861, -0.03375106304883957, -0.0170778539031744, -0.012847475707530975, 0.004400513134896755, -0.019588490948081017, -0.004494776949286461, -0.031120870262384415, 0.021795645356178284, 0.00463042501360178, 0.003158989129588008, 0.0344683863222599, 0.03135997802019119, -0.029097646474838257, 0.009210269898176193, 0.011238092556595802, 0.020232245326042175, 0.014751145616173744, 0.02701924368739128, 0.016351332888007164, 0.03364070504903793, -0.05363384261727333, -0.08850686997175217, -0.027239957824349403, 0.004391316790133715, 0.023929227143526077, 0.01867804117500782, 0.0004917240003123879, -0.012010595761239529, 0.008727454580366611, 0.004910917486995459, 0.0017404328100383282, 0.04142092168331146, 0.015201772563159466, -0.01870563067495823, 0.02762621082365513, 0.00131509592756629, 0.00017142803699243814, 0.034045349806547165, 0.01006094366312027, 0.00912290345877409, 0.03520410507917404]', 'distance': 0.6787811517715454}, {'title': '\"According to ...\": Prompting Language Models Improves Quoting from Pre-Training Data', 'authors': ['Orion Weller', 'Marc Marone', 'Nathaniel Weir', 'Dawn Lawrie', 'Daniel Khashabi', 'Benjamin Van Durme'], 'summary': 'Large Language Models (LLMs) may hallucinate and generate fake information,\\ndespite pre-training on factual data. Inspired by the journalistic device of\\n\"according to sources\", we propose according-to prompting: directing LLMs to\\nground responses against previously observed text. To quantify this grounding,\\nwe propose a novel evaluation metric (QUIP-Score) that measures the extent to\\nwhich model-produced answers are directly found in underlying text corpora. We\\nillustrate with experiments on three corpora (Wikipedia, PubMed, and the U.S.\\nlegal tax code) that these prompts improve grounding under our metrics, with\\nthe additional benefit of often improving end-task performance. Furthermore,\\nprompts that ask the model to decrease grounding (or to ground to other\\ncorpora) indeed decrease QUIP-Score, indicating the ability of LLMs to increase\\nor decrease grounded generations on request.', 'published': '2023-05-22T17:25:24+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2305.13252v2', 'vector': '[-0.058143291622400284, -0.026900164783000946, -0.008274094201624393, -0.013487521559000015, -0.03809452801942825, -0.06514444947242737, 0.004293824080377817, -0.0036152361426502466, -0.04376658797264099, -0.008325573056936264, -0.03734574094414711, -0.02817310206592083, -0.047285884618759155, 0.0016169113805517554, -0.043991222977638245, 0.0043359436094760895, -0.019917728379368782, 0.002122342586517334, -0.039161548018455505, -0.02661936916410923, -0.03549249470233917, -0.015949157997965813, 0.06551884114742279, -0.006257049273699522, 0.0072023929096758366, -0.006144731305539608, -0.025346431881189346, -0.03625999763607979, -0.0027400916442275047, 0.020310841500759125, 0.026656808331608772, -0.0013103767996653914, 0.07997042685747147, -0.0014308847021311522, -0.029633237048983574, -0.02072267420589924, -0.0008324402733705938, -0.008699966594576836, -0.02491587959229946, -0.017849205061793327, -0.016492027789354324, -0.020310841500759125, 0.0020088544115424156, -0.027237119153141975, 0.03283429890871048, 0.001853247289545834, -0.012607697397470474, -0.029801713302731514, 0.0015221431385725737, -0.031542643904685974, 0.007923100143671036, -0.013843195512890816, 0.03109337016940117, -0.013618559576570988, 0.017793046310544014, 0.011812111362814903, -0.07427965104579926, 0.026900164783000946, -0.05192836374044418, 0.011615554802119732, 0.01036133710294962, -0.01967437192797661, -0.03367668390274048, -0.01916894130408764, -0.011381559073925018, 0.028116943314671516, -0.019917728379368782, 0.042605966329574585, -0.024167092517018318, -0.0664173886179924, 0.020778832957148552, 0.05305154249072075, 0.007487867493182421, -0.023624222725629807, -0.03111208975315094, 0.00716963317245245, -0.011877629905939102, -0.008554888889193535, -0.022575920447707176, 0.037083663046360016, -0.01754968985915184, -0.003037266433238983, 0.03612896054983139, 2.701921039260924e-05, 0.012607697397470474, 0.015237811021506786, -0.020310841500759125, -0.03176727890968323, -0.00835365243256092, 0.006776520051062107, -0.02695632353425026, -0.007576785981655121, 0.0316549614071846, -0.07019875943660736, -0.017662007361650467, 0.007932459935545921, -0.03612896054983139, 0.028247980400919914, 0.014423505403101444, -0.0030021669808775187, 0.01217714510858059, 0.020778832957148552, 0.021190665662288666, -0.00553166214376688, 0.026712967082858086, -0.056271325796842575, -0.006565923802554607, 0.01495701540261507, 0.013309684582054615, -0.024129653349518776, 0.002686272608116269, 0.01952461525797844, 0.01813935860991478, 0.04170742258429527, 0.002108302665874362, -0.032927896827459335, 0.021321702748537064, -0.012682575732469559, 0.02177097462117672, -0.026338575407862663, 0.04376658797264099, 0.02452276647090912, 0.09097759425640106, -0.015790041536092758, -0.008250693790614605, -0.011025885120034218, 0.016913220286369324, -0.0290342066437006, -0.015883639454841614, 0.0033391208853572607, 0.005625260528177023, 0.011980588547885418, -0.028453897684812546, 0.010061821900308132, -0.03457522764801979, -0.053538255393505096, -0.01725953444838524, 0.00830217357724905, -0.00687011843547225, -0.022894155234098434, 0.022894155234098434, 0.07472892105579376, -0.0324973464012146, -0.040359605103731155, 0.01662306673824787, -0.08820708096027374, 0.03904923051595688, -0.0007879810873419046, -0.019094062969088554, -0.030176106840372086, -0.01503189466893673, 0.01303824968636036, 0.0072679114528000355, 0.04200693592429161, -0.012635776773095131, -0.025496190413832664, -0.010333257727324963, 0.03738318011164665, 0.011381559073925018, 0.035548653453588486, 0.0009722528047859669, 0.009921425022184849, -0.0014507743762806058, 0.012167785316705704, 0.023062633350491524, -0.011699792928993702, 0.021134506911039352, 0.02869725227355957, -0.04264340549707413, -0.043841466307640076, 0.010323897935450077, -0.012897851876914501, 0.02729327790439129, 0.010043102316558361, 0.01287913229316473, 0.01443286519497633, 0.07323134690523148, 0.033433329313993454, 0.026507051661610603, -0.015883639454841614, 0.01683834195137024, -0.026226256042718887, 0.011072684079408646, 0.005498902872204781, -0.04447793588042259, 0.004895193502306938, 0.0023165589664131403, -0.03908666968345642, 0.033302292227745056, -0.022501042112708092, -0.009097759611904621, -0.03251606598496437, 0.056121569126844406, 0.02212664857506752, 0.019075343385338783, -0.0625985711812973, -0.020273402333259583, -0.05518558621406555, -0.020479317754507065, -0.0341821163892746, -0.010127341374754906, 0.04852138087153435, -0.03373284265398979, -0.033807720988988876, -0.03006378933787346, -0.015649642795324326, -0.06720361113548279, -0.04095863550901413, 0.02059163525700569, -0.013590480200946331, 0.018214238807559013, 0.036709271371364594, 0.006954357028007507, 0.03217910975217819, 0.041744861751794815, 0.024953318759799004, -0.03837532177567482, 0.02437300980091095, 0.02143402211368084, -0.022931594401597977, -0.04391634464263916, 0.0840887576341629, -0.009752947837114334, 0.0442158579826355, 0.011999308131635189, 0.016782183200120926, 0.028772132471203804, 0.017231455072760582, 0.0006399786798283458, -0.027237119153141975, 0.03538017347455025, -0.01140027865767479, -0.021845854818820953, -0.029764274135231972, -0.03487474471330643, -0.03833788260817528, 0.005030910950154066, 0.03229143097996712, 0.017428012564778328, 0.027574073523283005, 8.570098725613207e-05, -0.056458521634340286, -0.011493876576423645, 0.032609663903713226, -0.02643217332661152, -0.04496464505791664, 0.018289117142558098, 0.04050936549901962, -0.009949504397809505, 0.006140051409602165, 0.013843195512890816, 0.004871794022619724, -0.0719209685921669, -0.031243128702044487, 0.02592674270272255, 0.02437300980091095, 0.02452276647090912, -0.01184019073843956, -0.027948467060923576, -0.004401462152600288, 0.057282187044620514, 0.00029322606860660017, 0.00014902612019795924, 0.019131502136588097, 0.026319855824112892, -0.010997805744409561, -0.010773169808089733, -0.031879596412181854, -0.0056346203200519085, 0.04286804422736168, -0.03801964968442917, -0.047435641288757324, 0.04006009176373482, -0.01692258007824421, -0.010997805744409561, 0.042605966329574585, -0.048858337104320526, -0.016360990703105927, 0.08311533182859421, 2.1278998246998526e-05, -0.043317314237356186, -0.009968223981559277, 0.052452512085437775, -0.02525283396244049, 0.019318697974085808, -0.003053646069020033, -0.00514322891831398, -0.0010717010591179132, -0.01443286519497633, -0.0774245485663414, -0.015780681744217873, 0.006865438539534807, 0.07184609025716782, -0.009752947837114334, -0.05859256535768509, -0.023979896679520607, 0.013787036761641502, -0.15469934046268463, -0.010249018669128418, -0.04721100628376007, -0.01916894130408764, 0.005236827302724123, 0.011344119906425476, -0.0140959108248353, 0.020797552540898323, 0.009448752738535404, 0.008498730137944221, 0.005583141464740038, -0.06432078033685684, 0.020797552540898323, -0.024784842506051064, -0.03908666968345642, 0.03494962304830551, 0.009486192837357521, -0.02023596316576004, -0.016632426530122757, -0.026338575407862663, -0.001203908701427281, -0.07143425941467285, 0.041220713406801224, -0.017390573397278786, -0.016295472159981728, -0.027555353939533234, 0.0305879395455122, 0.036203838884830475, -0.007895020768046379, 0.04062168300151825, -0.01643586903810501, 0.004853074438869953, -0.0009494381956756115, 0.0023446385748684406, -0.023586783558130264, 0.02957707829773426, -0.020966028794646263, -0.00794649962335825, -0.06192466616630554, -0.00315426429733634, 7.049125269986689e-05, 0.06409614533185959, 0.0001438343315385282, 0.025645947083830833, -0.01115692313760519, -0.04133303090929985, 0.0030325863044708967, -0.020797552540898323, -0.012392421253025532, -0.014021032489836216, -0.004251705016940832, 0.012486019171774387, -0.03833788260817528, 0.03006378933787346, -0.041557665914297104, -0.008943322114646435, 0.0050215511582791805, -0.011886989697813988, -0.010941646993160248, 0.0031753238290548325, 0.004375722724944353, -0.05061798542737961, 0.009490872733294964, -0.0587797611951828, -0.0269937627017498, -0.012270743027329445, 0.0792965218424797, 0.029670676216483116, 0.00027245894307270646, -0.048146989196538925, 0.04897065460681915, -0.0038445519749075174, -0.00025915043079294264, -0.011915069073438644, 0.03062537871301174, 0.014526463113725185, 0.010211579501628876, 0.014919576235115528, -0.015612204559147358, -0.08184239268302917, -0.04058424383401871, 0.006888838484883308, -0.019842850044369698, 0.04137047007679939, -0.007754622958600521, 0.004211925435811281, 0.02714352123439312, 0.014470304362475872, 0.04264340549707413, 0.23886297643184662, 0.016726024448871613, -0.03159880265593529, -0.02837901934981346, 0.002403137506917119, -0.014049111865460873, -0.0016683904686942697, 0.0480346716940403, -0.001909406273625791, -0.032272711396217346, 0.06353455781936646, 0.06136307492852211, -0.009345795027911663, 0.024635083973407745, -0.020479317754507065, 0.024429168552160263, -0.0487460196018219, -0.004101947415620089, 0.07495355606079102, 0.0057048192247748375, 0.04099607467651367, 0.006083892658352852, 0.03908666968345642, -0.009266235865652561, -0.02072267420589924, -0.005985614378005266, 0.009313035756349564, 0.00315426429733634, 0.038974352180957794, 0.013244166038930416, -0.02487844042479992, 0.042456209659576416, 0.04807211086153984, 0.037607815116643906, 0.013974233530461788, 0.014395426027476788, -0.008194535039365292, -0.00437104282900691, -0.0020673535764217377, 0.0305879395455122, -0.019150221720337868, -0.020966028794646263, -0.05069286376237869, 0.0070198760367929935, -0.030194826424121857, -0.04062168300151825, 0.03304021805524826, 0.028285419568419456, 0.01721273548901081, -0.027536634355783463, -0.019412297755479813, -0.017390573397278786, -0.00039691547863185406, 0.016052115708589554, -0.01295401155948639, 0.019655652344226837, -0.0016461609629914165, 0.03946106135845184, -0.03365796431899071, 0.04889577627182007, 0.0394236221909523, -0.003624595934525132, 0.021228104829788208, 0.015247170813381672, 0.029633237048983574, -0.00037205341504886746, -0.02059163525700569, -0.004600358661264181, 0.04646221920847893, 0.04234389215707779, 0.03867483511567116, 0.028135662898421288, 0.009411313571035862, 0.013150567188858986, 0.014376705512404442, -0.03803836926817894, 0.03994777426123619, 0.034818585962057114, -0.04047192633152008, -0.003456118982285261, -0.00023765205696690828, -0.02178969420492649, 0.00023633582168258727, 0.006406806875020266, 0.00035976863000541925, 0.001267087645828724, -0.007263231556862593, 0.041033513844013214, 0.007427028845995665, 0.016052115708589554, -0.027480473741889, 0.0025271554477512836, -0.020703954622149467, 0.0046143983490765095, -0.008980761282145977, 0.008550208993256092, -0.009818466380238533, -0.03410723805427551, -0.01954333484172821, -0.022950313985347748, 0.04215669631958008, -0.011353479698300362, 0.049869198352098465, -0.008877803571522236, 0.006551884114742279, 0.009565751068294048, -0.015818120911717415, 0.003952190279960632, -0.007328750565648079, 0.040546804666519165, -0.03764525428414345, -0.006579963956028223, 0.010183500126004219, -0.06623019278049469, 0.026132658123970032, 0.06267344951629639, 0.015406288206577301, 0.020460598170757294, 0.032272711396217346, 0.010960366576910019, -0.04425329715013504, 0.09150174260139465, -0.02281927689909935, 0.0372895821928978, 0.00032320679747499526, -0.014320546761155128, 0.02089115045964718, 0.037046223878860474, -0.020778832957148552, -0.004993471782654524, 0.0017760286573320627, 0.024803562089800835, 0.006200890522450209, 0.04043448716402054, 0.006196210626512766, -0.014123990200459957, 0.04047192633152008, 0.005344465374946594, -0.018691590055823326, 0.03581072762608528, -0.0463499017059803, 0.011465797200798988, -0.01893494464457035, -0.0013700458221137524, 0.019487176090478897, 0.00695903692394495, -0.011737233027815819, -0.026525771245360374, -0.019655652344226837, 0.029127804562449455, -0.010829328559339046, 0.04582574963569641, 0.02901548705995083, -0.05323873832821846, -0.007441068533807993, -0.016828982159495354, -0.035024501383304596, -0.016463948413729668, -0.004137047100812197, -0.012598337605595589, -0.00046506678336299956, 0.044178418815135956, 0.012186504900455475, 0.011297320015728474, -0.0018918565474450588, 0.009149238467216492, -0.002690952504053712, 0.0539875254034996, 0.007572106085717678, -0.04339219257235527, 0.0009008841007016599, -0.004513780120760202, 0.00532106589525938, 0.07967091351747513, -0.02057291567325592, 0.02281927689909935, -0.058367930352687836, 0.03833788260817528, -0.03755165636539459, -0.0501687154173851, 0.00017330318223685026, -0.002156271832063794, -0.03893691301345825, 0.04402866214513779, -0.019243819639086723, -0.004043448716402054, 0.003568436950445175, -0.02710608020424843, 0.03582944720983505, -0.01520973164588213, -0.03150520473718643, 0.12497251480817795, -0.043841466307640076, -0.011166282929480076, 0.023399585857987404, 0.002145742066204548, 0.06503213196992874, 0.014526463113725185, 0.01937485858798027, -0.04447793588042259, -0.0012343281414359808, 0.0006358837708830833, -0.017100417986512184, 0.031243128702044487, -0.015621564351022243, 0.03470626845955849, 0.014545182697474957, 0.011774672195315361, -0.0045722792856395245, -0.0456385537981987, -0.008891843259334564, -0.003867951687425375, -0.051254455000162125, 0.009298996068537235, 0.013524960726499557, -0.0134968813508749, 0.007993298582732677, 0.024148372933268547, -0.005789057817310095, 0.02920268476009369, 0.0009991623228415847, 0.004984111990779638, 0.01245793979614973, -0.03504322096705437, -0.03511809930205345, 0.00807285774499178, 0.005213427823036909, 0.01952461525797844, 0.03113080933690071, 0.007005836348980665, -0.004300843924283981, -0.028472617268562317, 0.018710309639573097, 0.021040908992290497, -0.007651664782315493, 0.011849550530314445, -0.006392767187207937, 0.024822281673550606, -0.02592674270272255, -0.031186968088150024, -0.005611220840364695, 0.04185717925429344, 0.006280449219048023, 0.05690779536962509, -0.0185792725533247, -0.01812063902616501, 0.008587648160755634, -0.0011793391313403845, 0.030119948089122772, 0.007412989158183336, -0.0033087015617638826, -0.007506587542593479, -0.01399295311421156, 0.011119483970105648, 0.0005089410115033388, 0.028098223730921745, 0.0017736887093633413, -0.010698290541768074, -0.016192514449357986, -0.0053678653202950954, 0.013028889894485474, 0.006037093233317137, 0.007778022438287735, -0.022706959396600723, -0.0401349700987339, -0.062486257404088974, 0.005498902872204781, -0.02487844042479992, -0.01761520840227604, -0.061962105333805084, 0.03923642635345459, 0.015771321952342987, 0.002630113624036312, -0.05657083913683891, 0.07862260937690735, -0.0011740742484107614, -0.003121504792943597, 0.005063670687377453, 0.02128426358103752, -0.01986156962811947, -0.06724105030298233, 0.09314907342195511, -0.007253871764987707, -0.02693760395050049, -0.013665358535945415, 0.04050936549901962, 0.07326878607273102, -0.018298476934432983, -0.0015303329564630985, -0.022575920447707176, 0.031374163925647736, 0.024635083973407745, -0.013047609478235245, -0.009855905547738075, 0.0041300272569060326, 0.011381559073925018, -0.020685235038399696, 0.06503213196992874, -0.04095863550901413, 0.011213081888854504, -0.019094062969088554, 0.027723830193281174, 0.0022849696688354015, -0.009303675964474678, -0.05046822875738144, 0.010258378461003304, -0.030718976631760597, 0.016651146113872528, 0.053276177495718, 0.019955167546868324, -0.026376014575362206, -0.027911026030778885, 0.018775828182697296, -0.017727525904774666, 0.018279757350683212, 0.05514814704656601, 0.04683661088347435, -0.014292467385530472, -0.007960539311170578, -0.01641714945435524, 0.03693390637636185, 0.01773688569664955, -0.02594546228647232, 0.0012448580237105489, -0.028229260817170143, 0.008358332328498363, -0.010108621791005135, 0.011784031987190247, -0.0010366017231717706, -0.021565059199929237, -0.021040908992290497, -0.04893321543931961, 0.018129998818039894, -0.025327712297439575, 0.01243922021239996, -0.012233303859829903, 0.024934599176049232, 0.013450082391500473, 0.023942457512021065, -0.02109706774353981, 0.03938618302345276, -0.04444049298763275, 0.029839152470231056, -0.00919603742659092, 0.0026230935472995043, -0.011128843761980534, 0.00556910177692771, -0.004733736161142588, 0.018523111939430237, -0.043616827577352524, 0.009097759611904621, 0.0418197400867939, -0.002777530811727047, -0.035848166793584824, 0.018298476934432983, -0.013328404165804386, 0.03425699472427368, 0.03421955555677414, 0.04402866214513779, 0.030513061210513115, 1.9944491214118898e-05, 0.022182807326316833, -0.046424780040979385, -0.050730302929878235, -0.017896004021167755, 0.013889994472265244, 0.0011313699651509523, -0.017072338610887527, -0.03251606598496437, -0.05428704246878624, -0.01572452299296856, 0.03893691301345825, -0.018298476934432983, -0.01635163091123104, -0.008760805241763592, 0.022089209407567978, -0.03186087682843208, 0.006448925938457251, 0.04459025338292122, -0.01782112568616867, 0.012130345217883587, -0.01593043841421604, -0.0012834672816097736, -0.03408851847052574, 0.032590944319963455, -0.03356436640024185, -0.02645089291036129, 0.03264710307121277, 0.07285695523023605, 0.050056394189596176, -0.02349318563938141, 0.008044777438044548, -0.022706959396600723, 0.016566907986998558, -0.1424192488193512, 0.01608019508421421, -0.011718512512743473, 0.002003004541620612, -0.02592674270272255, -0.02852877601981163, 0.01020221970975399, -0.027911026030778885, -0.045002084225416183, -0.053163859993219376, 0.03180471807718277, 0.021845854818820953, 0.011718512512743473, -0.041595105081796646, -0.0480346716940403, 0.025889301672577858, -0.057132430374622345, -0.010417495854198933, 0.004218945279717445, 0.02006748504936695, 0.021190665662288666, 0.017755607143044472, -0.010726370848715305, 0.02003004588186741, -0.033807720988988876, 0.0014659841544926167, -0.00877484492957592, -0.07630137354135513, -0.0022603999823331833, -0.033096376806497574, 0.0009242836968041956, -0.046050384640693665, 0.026357294991612434, 0.056421082466840744, 0.005311706103384495, -0.030550500378012657, 0.001061171293258667, -0.006528484635055065, -0.011147563345730305, -0.009074359200894833, 0.015144212171435356, 0.010501733981072903, -0.035361453890800476, 0.05076774209737778, -0.008540849201381207, 0.015200371854007244, -0.03783245012164116, -0.008971401490271091, -0.04215669631958008, -0.012289462611079216, 0.0030138667207211256, 0.025496190413832664, -0.034481629729270935, -0.010164780542254448, 0.013309684582054615, 0.021396581083536148, -0.03648463636636734, 0.00906967930495739, 0.006622083019465208, 0.02764895185828209, -0.014404785819351673, 0.01805512048304081, -0.028416458517313004, -0.006491045467555523, -0.011110123246908188, -0.009064999409019947, -0.02283799648284912, 0.005723538808524609, -0.026151377707719803, 0.03133672475814819, -0.02317495085299015, 0.009444072842597961, -0.011784031987190247, -0.042980361729860306, 0.00713687390089035, -0.028397738933563232, 0.02851005643606186, 0.0408463180065155, -0.025103077292442322, -0.030194826424121857, -0.03006378933787346, 0.03841276094317436, -0.04065912216901779, -0.027368156239390373, 0.02336214669048786, -0.048146989196538925, -0.01237370166927576, 0.01401167269796133, -0.09232541173696518, 0.011971228756010532, -0.0012939971638843417, -0.03279685974121094, -0.023998616263270378, -0.018401434645056725, -0.0064302063547074795, 0.015041254460811615, -0.03717726469039917, 0.03592304512858391, 0.01883198693394661, -0.053837768733501434, -0.031711120158433914, 0.03279685974121094, -0.018363995477557182, 0.028978047892451286, 0.011606195010244846, 0.033302292227745056, -0.026656808331608772, 0.017437372356653214, 0.03245990723371506, 0.06038965284824371, -0.010688930749893188, -0.01044557522982359, 0.03592304512858391, -0.010398776270449162, -0.038974352180957794, -0.03302149474620819, -0.010239658877253532, 0.02802334539592266, 0.03367668390274048, 0.01834527589380741, 0.03390132263302803, -0.014507743529975414, 0.0004296748957131058, -0.014039752073585987, 0.03255350515246391, 0.08281581848859787, 0.0152939697727561, 0.013197367079555988, 0.012673215940594673, -0.020011326298117638, 0.02852877601981163, -0.026862725615501404, -0.03695262596011162, 0.007029235828667879, 0.011175642721354961, 0.00034514389699324965, -0.009547031484544277, 0.042119257152080536, -0.029165245592594147, 0.0004232400096952915, -0.03581072762608528, -0.0163141917437315, -0.029820432886481285, -0.02261335961520672, -0.01988028921186924, 0.03738318011164665, 0.020685235038399696, -0.0017327393870800734, -0.021733535453677177, -0.02802334539592266, 0.021040908992290497, -0.00609793234616518, 0.0017198695568367839, 0.013356483541429043, 0.013281605206429958, -0.005901375785470009, -0.03590432554483414, -0.004214265383780003, 0.0032572224736213684, 0.07293183356523514, -0.010342617519199848, -0.012748095206916332, -0.016155075281858444, -0.06342224031686783, -0.02195817232131958, 0.04144534841179848, -0.024953318759799004, -0.0008985440945252776, -0.03214167058467865, 0.027068641036748886, 0.037926048040390015, 0.008100937120616436, -0.027443034574389458, 0.0049092331901192665, 0.0008131356444209814, 0.006762480363249779, -0.013758956454694271, 0.014264388009905815, 0.05443679913878441, -0.020179802551865578, 0.04137047007679939, -0.0031566042453050613, 0.0014741739723831415, 0.019973887130618095, -0.0152284512296319, 0.016819622367620468, -0.024241970852017403, 0.04425329715013504, 0.05578461289405823, -0.015125492587685585, -0.004385082516819239, -0.0005098184919916093, 0.02731199748814106, 0.016426509246230125, 0.03953593969345093, -0.010436215437948704, 0.021565059199929237, 0.060576848685741425, -0.05166628584265709, 0.04043448716402054, -0.018607351928949356, 0.013637279160320759, -0.013047609478235245, -0.03599792346358299, -0.014367345720529556, -0.020853711292147636, 0.014226948842406273, -0.07072290778160095, -0.026132658123970032, -0.04268084466457367, 0.010071181692183018, -0.024148372933268547, -0.01834527589380741, 0.065256766974926, -0.0016543507808819413, -0.025346431881189346, 0.03991033509373665, -0.022257687523961067, 0.02111578732728958, -0.02764895185828209, 0.03390132263302803, 0.027368156239390373, -0.009528311900794506, -0.043841466307640076, -0.0387122742831707, 0.004197885747998953, 0.008728045970201492, 0.0332086943089962, -0.012776174582540989, 0.014985094778239727, 0.011465797200798988, -0.01973053067922592, 0.005503582768142223, 0.02437300980091095, 0.01616443507373333, 0.006851398851722479, -0.023942457512021065, -0.0022159407380968332, -0.05207812041044235, -0.005475503392517567, 0.0480346716940403, -0.0161831546574831, 0.04956968501210213, -0.05117957666516304]', 'distance': 0.6840780973434448}, {'title': 'Tapping the Potential of Large Language Models as Recommender Systems: A Comprehensive Framework and Empirical Analysis', 'authors': ['Lanling Xu', 'Junjie Zhang', 'Bingqian Li', 'Jinpeng Wang', 'Sheng Chen', 'Wayne Xin Zhao', 'Ji-Rong Wen'], 'summary': 'Recently, Large Language Models~(LLMs) such as ChatGPT have showcased\\nremarkable abilities in solving general tasks, demonstrating the potential for\\napplications in recommender systems. To assess how effectively LLMs can be used\\nin recommendation tasks, our study primarily focuses on employing LLMs as\\nrecommender systems through prompting engineering. We propose a general\\nframework for utilizing LLMs in recommendation tasks, focusing on the\\ncapabilities of LLMs as recommenders. To conduct our analysis, we formalize the\\ninput of LLMs for recommendation into natural language prompts with two key\\naspects, and explain how our framework can be generalized to various\\nrecommendation scenarios. As for the use of LLMs as recommenders, we analyze\\nthe impact of public availability, tuning strategies, model architecture,\\nparameter scale, and context length on recommendation results based on the\\nclassification of LLMs. As for prompt engineering, we further analyze the\\nimpact of four important components of prompts, \\\\ie task descriptions, user\\ninterest modeling, candidate items construction and prompting strategies. In\\neach section, we first define and categorize concepts in line with the existing\\nliterature. Then, we propose inspiring research questions followed by detailed\\nexperiments on two public datasets, in order to systematically analyze the\\nimpact of different factors on performance. Based on our empirical analysis, we\\nfinally summarize promising directions to shed lights on future research.', 'published': '2024-01-10T08:28:56+00:00', 'source': 'arxiv', 'pdf_url': 'http://arxiv.org/pdf/2401.04997v2', 'vector': '[-0.02213672176003456, -0.013744841329753399, -0.010891787707805634, 0.0070768739096820354, -0.014739228412508965, -0.05222853645682335, 0.005139213986694813, -0.026430241763591766, -0.030017469078302383, -0.006147540640085936, -0.011960521340370178, -0.011291401460766792, -0.01160737406462431, -0.0001096323030651547, -0.03354893624782562, 0.02079848200082779, -0.0013208151794970036, 0.012518120929598808, -0.004837180487811565, -0.01278762798756361, -0.007685587275773287, 0.02780565619468689, 0.03133712336421013, -0.02905096299946308, -0.014144455082714558, 0.01592877507209778, -0.0284561887383461, -0.042451951652765274, -0.03215493634343147, 0.0238095223903656, 0.019664695486426353, -0.007313854061067104, 0.06371510028839111, -0.0061754207126796246, -0.0223783478140831, -0.02325192280113697, 0.008131667040288448, -0.010687334463000298, -0.0308166965842247, 0.032786883413791656, -0.041522618383169174, -0.04501691088080406, -0.01912568137049675, -0.03250808268785477, 0.0043725138530135155, -0.023660829290747643, -0.02334485575556755, -0.025222109630703926, 0.0071001071482896805, -0.011458680965006351, -0.021188801154494286, -0.024943308904767036, -0.0007057125912979245, -0.045239951461553574, 0.0024720269721001387, 0.015538454987108707, -0.03550053760409355, 0.008675327524542809, -0.09025686234235764, 0.0317460298538208, -0.023642241954803467, 0.0009148126118816435, 0.034422509372234344, -0.042266085743904114, -0.007699527312070131, 0.0029599268455058336, -0.02812162972986698, 0.026523176580667496, -0.027043603360652924, -0.061856433749198914, 0.014497601427137852, 0.024868961423635483, 0.0022826751228421926, -0.01139362808316946, -0.0466897115111351, 0.05360395088791847, 0.025054829195141792, -0.03289840370416641, -0.02492472156882286, 0.005599233787506819, -0.02706218883395195, -0.01661648228764534, 0.03970112279057503, -0.0008276875596493483, 0.01249024085700512, -0.03416229784488678, -0.0289580300450325, 0.03858592361211777, 0.02092858776450157, -0.03579792380332947, -0.016068175435066223, 0.0070768739096820354, 0.031448643654584885, -0.0284561887383461, -0.038139842450618744, 0.013921414501965046, -0.055722832679748535, 0.014005054719746113, -0.039478082209825516, 0.004711720626801252, 9.955847417586483e-06, 0.01648637466132641, -0.0050369868986308575, 0.0037428904324769974, 0.05297200381755829, -0.04605776444077492, 0.03245232254266739, -0.0026997136883437634, 0.008052674122154713, -0.013837774284183979, -0.004665253683924675, 0.0228801891207695, 0.018242815509438515, 0.03719192370772362, -0.006477453745901585, -0.009414147585630417, 0.015352588146924973, 0.0011639901204034686, 0.0067376671358942986, -0.030983977019786835, 0.04092784225940704, 0.028344668447971344, 0.07575926184654236, -0.024497229605913162, -0.05063008517026901, 0.013233708217740059, -0.0018203317886218429, -0.02247128263115883, -0.03328872472047806, 0.002219945192337036, 0.008252480998635292, -0.0007492750883102417, -0.06178208440542221, -0.00016887730453163385, -0.04735882952809334, -0.04044459015130997, -0.021839335560798645, -0.011598081327974796, -0.00866603385657072, -0.030203336849808693, 0.04137392342090607, 0.027972936630249023, -0.03618824481964111, -0.024590162560343742, 0.04364149644970894, -0.06360357999801636, 0.03364187106490135, -0.008884427137672901, -0.008614921011030674, 0.017582988366484642, 0.01986914873123169, 0.03373480215668678, 0.02130032144486904, 0.04616928473114967, 0.032377976924180984, -0.014330320991575718, -0.03494293615221977, 0.01602170802652836, -0.0161239355802536, 0.03364187106490135, -0.026578934863209724, 0.030240509659051895, 0.026207203045487404, -0.010222667828202248, 0.029069548472762108, 0.005854800343513489, 0.0013475334271788597, 0.003494293661788106, -0.06486747413873672, -0.042080216109752655, 0.021932268515229225, -0.008963420987129211, 0.0018598284805193543, -0.0064309872686862946, -0.007267387118190527, -0.0063334074802696705, 0.046206459403038025, 0.03211776167154312, 0.04163413867354393, -0.017694508656859398, -0.042823683470487595, -0.0019364985637366772, 0.005074160639196634, -0.010966134257614613, -0.020482508465647697, 0.03587226942181587, 0.005171740427613258, -0.05449611321091652, 0.03300992399454117, -0.04323258996009827, -0.0029715436976403, -0.05063008517026901, 0.03206200152635574, 0.02901379019021988, 0.02024088241159916, -0.08230176568031311, 0.015259655192494392, -0.03440392389893532, 0.008020147681236267, -0.04230325669050217, 0.004976580385118723, 0.02854912355542183, -0.01990632154047489, 0.00898665376007557, -0.04873424395918846, -0.02037098817527294, -0.06862197816371918, -0.030742349103093147, -0.010027507320046425, -0.04758187010884285, 0.03709898889064789, -0.0044003939256072044, 0.03676443174481392, -0.005450540687888861, -0.010380654595792294, 0.05170810967683792, -0.024311361834406853, 0.04055611044168472, 0.00510668708011508, -0.03806549683213234, -0.02752685546875, 0.05207984521985054, -0.0021572152618318796, 0.050295524299144745, 0.019664695486426353, -0.0028577002231031656, 0.03862309828400612, 0.004307460505515337, -0.002350051887333393, 0.00038218835834413767, 0.03204341605305672, 0.011217053979635239, -0.03821419179439545, -0.02460874803364277, -0.002092161914333701, -0.039478082209825516, -0.005938440561294556, -0.0326753631234169, 0.008210660889744759, -0.02302888222038746, 0.0340321883559227, -0.02827032282948494, 0.014627708122134209, 0.014032934792339802, -0.04996096342802048, -0.030333442613482475, -0.011170587502419949, 0.05007248371839523, -0.011430800892412663, -0.0012697017518803477, -0.02427418902516365, -0.013196534477174282, -0.04914315044879913, -0.04464517906308174, 0.02544514834880829, 0.025036241859197617, 0.02970149554312229, -0.017378535121679306, -0.03970112279057503, -0.003347923746332526, 0.02985018864274025, 0.011291401460766792, 0.0007481133798137307, 0.011635254137217999, 0.03654139116406441, 0.0020236235577613115, -0.014302440918982029, -0.0764283835887909, -0.0019690252374857664, 0.052154190838336945, -0.017490055412054062, -0.017350655049085617, 0.025240695104002953, -0.015045908279716969, -0.015213187783956528, 0.044719524681568146, -0.03263818845152855, -0.023642241954803467, 0.048065125942230225, 0.004107653629034758, -0.018335748463869095, -0.013605440966784954, 0.03408794850111008, 0.010157614015042782, 0.022248242050409317, -0.021374668926000595, -0.03347459062933922, -0.02464592270553112, -0.010064681060612202, -0.03797256201505661, 0.010705920867621899, 0.01981338858604431, 0.07044347375631332, -0.032842643558979034, -0.027006428688764572, 0.016839521005749702, 0.00962789449840784, -0.14794987440109253, -0.016858108341693878, -0.02998029626905918, 0.02460874803364277, 0.00532508036121726, 0.018698187544941902, -0.06557376682758331, 0.004047247115522623, -0.017099734395742416, 0.016188988462090492, 0.016932453960180283, -0.05115051195025444, -0.01053864136338234, -0.009126054123044014, -0.0569123774766922, 0.014330320991575718, -0.06338053941726685, 0.013735547661781311, 0.0005965158925391734, -0.019664695486426353, 0.007969033904373646, -0.06568528711795807, 0.03828853741288185, 0.005520240403711796, -0.0066122072748839855, -0.00730920722708106, 0.012192854657769203, 0.02064978890120983, -0.023549309000372887, 0.020854242146015167, -0.0438273623585701, 0.017675921320915222, -0.002289645140990615, 0.024683095514774323, 0.030110402032732964, 0.04605776444077492, -0.016300508752465248, 0.013112894259393215, -0.031355708837509155, 0.018122002482414246, 0.009739413857460022, 0.06672614067792892, 0.036225415766239166, 0.0033781270030885935, -0.04434778913855553, -0.017248427495360374, 0.04219173640012741, -0.034701310098171234, -0.017369242385029793, -0.023177575320005417, -0.02191368117928505, -0.009943868033587933, -0.02985018864274025, 0.02867922931909561, -0.05728411301970482, -0.03654139116406441, -0.009804467670619488, 0.03518456220626831, -0.01548269484192133, -0.00040019419975578785, -0.00818278081715107, -0.01051076129078865, 0.025277869775891304, -0.015231775119900703, -0.030091816559433937, -0.02148618921637535, 0.0653507262468338, 0.011217053979635239, 0.009590720757842064, -0.01602170802652836, 0.03314002975821495, -0.06453291326761246, 0.021318908780813217, -0.01819634810090065, 0.04457082971930504, 0.058139096945524216, 0.016096055507659912, 0.009153934195637703, -0.011802534572780132, -0.06940262019634247, 0.021151628345251083, -0.0009084233897738159, 0.0039032003842294216, 0.060778405517339706, -0.01615181565284729, -0.049180325120687485, 0.035296082496643066, 0.008322181180119514, 0.017304187640547752, 0.2371658831834793, -0.0023883869871497154, 0.031541574746370316, -0.028604881837964058, 0.040221549570560455, 0.008549867197871208, -0.043381284922361374, 0.04371584579348564, 0.05408720672130585, -0.02442288212478161, 0.026541762053966522, 0.05211701989173889, 0.006644733715802431, 0.019924908876419067, 0.015371174551546574, 0.024125495925545692, -0.031039735302329063, -0.016300508752465248, 0.07784096896648407, -0.030184749513864517, 0.0007725083851255476, 0.02260138839483261, 0.013001374900341034, -0.08423478156328201, -0.07479275017976761, -0.01832645572721958, 0.03336307033896446, -0.028065869584679604, 0.0247388556599617, 0.03713616356253624, -0.04773056507110596, 0.050481390208005905, 0.04163413867354393, 0.02018512226641178, 0.009981040842831135, 0.005143860355019569, -0.03289840370416641, 0.0033084270544350147, -0.008219953626394272, 0.026950668543577194, -0.007611240725964308, -0.03003605641424656, -0.03141146898269653, -0.03522173687815666, -0.017592281103134155, -0.03884613513946533, 0.013782014138996601, -0.02622578851878643, 0.0071001071482896805, -0.02994312345981598, -0.0003392066864762455, 0.0210215225815773, -0.0040379539132118225, 0.026746215298771858, 0.019423067569732666, 0.0161239355802536, -0.000267473777057603, 0.0177316814661026, 0.01977621577680111, 0.02594698965549469, 0.04248912259936333, -0.002058473415672779, -0.004957993980497122, 0.021318908780813217, -0.00598955387249589, 0.0036174303386360407, 0.029905948787927628, 0.010324894450604916, 0.06958848983049393, 0.0476190447807312, 0.01359614823013544, 0.06642875075340271, 0.009581427089869976, -0.01455336157232523, 0.03096538968384266, -0.04884576424956322, 0.034143708646297455, 0.057916060090065, 0.00950243417173624, 0.02148618921637535, -0.005752573721110821, 0.009720827452838421, -0.0008758967742323875, -0.016421321779489517, 0.02024088241159916, -0.013103601522743702, 0.0067376671358942986, 0.049737922847270966, -0.012815508060157299, -2.8769403797923587e-05, -0.047879256308078766, -0.02386528253555298, 0.02529645524919033, -0.006365933921188116, -0.016570014879107475, -0.019330134615302086, 0.024162668734788895, -0.04590906947851181, -0.014255974441766739, -0.027842829003930092, 0.034738484770059586, -0.0036406635772436857, 0.040965016931295395, 0.027452509850263596, 0.030798109248280525, -0.0023546984884887934, -0.02222965471446514, 0.013791307806968689, -0.0049347602762281895, 0.06282293796539307, -0.025166349485516548, 0.011291401460766792, 0.011988401412963867, -0.04085349664092064, 0.016328388825058937, 0.08081483095884323, 0.03936656191945076, 0.006180067081004381, 0.040965016931295395, 0.013484627939760685, -0.014767108485102654, 0.06769264489412308, -0.04293520376086235, -0.00047628337051719427, -0.008387234061956406, -0.08059179037809372, -0.00375915365293622, 0.008893720805644989, -0.04497973620891571, 0.01726701483130455, 0.02302888222038746, 0.01018549408763647, -0.01156090758740902, 0.04557451233267784, 0.02715512178838253, -0.0026973902713507414, 0.004700103774666786, 0.01111482735723257, -0.00789004098623991, 0.042637817561626434, -0.03252666816115379, 0.018233520910143852, -0.008814727887511253, -0.01051076129078865, 0.02761978842318058, 0.05992341786623001, -0.06081558018922806, 0.025092002004384995, -0.01810341514647007, 0.04122523218393326, 0.050667259842157364, 0.01292702741920948, 0.043009549379348755, -0.04234043136239052, 0.0037196569610387087, -0.0071791005320847034, -0.024125495925545692, 0.01925578899681568, -0.00531578715890646, -0.0322478711605072, -0.011375040747225285, 0.017991894856095314, 0.019739041104912758, 0.0368201918900013, 0.009767293930053711, 0.011867587454617023, 0.022917361930012703, 0.06219099089503288, -0.003043566830456257, -0.026393068954348564, -0.021709227934479713, -0.007411433849483728, -0.05839931219816208, 0.05542544648051262, -0.016765175387263298, 0.018921228125691414, -0.03077952191233635, -0.0014300118200480938, -0.03988698869943619, -0.023289095610380173, 0.018029067665338516, -0.043938882648944855, -0.01981338858604431, 0.04698709771037102, -0.017871081829071045, -0.03572357818484306, -0.004230790305882692, -0.011235641315579414, 0.04962640628218651, -0.0049347602762281895, -0.010780267417430878, 0.11308129131793976, -0.06100144609808922, -0.009562840685248375, 0.02492472156882286, -0.0391806960105896, 0.07962528616189957, 0.0037126869428902864, 0.0027531501837074757, -0.014683468267321587, -0.011356454342603683, -0.011328574270009995, -0.017685214057564735, 0.016077468171715736, -0.045797549188137054, -0.003780063707381487, -0.0228430163115263, -0.013243000954389572, -0.026486001908779144, -0.03068658895790577, -0.012759747914969921, -0.0009304950945079327, -0.05438459292054176, -0.0009636025642976165, -0.01550128124654293, 0.00818278081715107, -0.03955243155360222, 0.05799040570855141, 0.004879000596702099, 0.02042674832046032, 0.009925280697643757, -0.01831716112792492, -0.034701310098171234, -0.042860858142375946, 0.00777387386187911, -0.011402920819818974, 0.017229842022061348, 0.003417623694986105, 0.01972045563161373, 0.007323147263377905, 0.00148809514939785, -0.022991709411144257, -0.03884613513946533, -0.011728188022971153, 0.012555294670164585, 0.013958588242530823, 0.010073973797261715, 0.01940448209643364, -0.026262963190674782, -0.0013603117549791932, -0.007769227493554354, 0.025556668639183044, 0.017908254638314247, 0.03525891155004501, 0.01227649487555027, -0.013735547661781311, -0.010668748058378696, 0.007086167111992836, 0.031021149829030037, 0.015761494636535645, 0.027229469269514084, -0.021969441324472427, 0.005501653999090195, -0.010362067259848118, -0.0005976775428280234, -0.006063900422304869, -0.024255601689219475, -0.008689267560839653, -0.024701682850718498, -0.02191368117928505, 0.015287534333765507, 0.0009333992493338883, -0.01810341514647007, -0.006398460362106562, -0.009516374208033085, -0.060369499027729034, -0.014404667541384697, -0.018270695582032204, -0.0170532688498497, -0.018707482144236565, -0.0066168541088700294, 0.00962789449840784, 0.0015496634878218174, -0.07635403424501419, 0.029088135808706284, -0.03440392389893532, 0.007871453650295734, 0.009214340709149837, 0.036002375185489655, 0.019980669021606445, -0.04598341882228851, 0.043046724051237106, 0.014915801584720612, 0.017787441611289978, 0.014432547613978386, -0.017722388729453087, 0.028233150020241737, -0.0008027117582969368, 0.017099734395742416, -0.020352402701973915, 0.027396749705076218, 0.029534216970205307, -0.01844726875424385, -0.039998508989810944, 0.04587189853191376, 0.019478827714920044, -0.028716402128338814, 0.07903051376342773, -0.005761867389082909, 0.0026718336157500744, -0.02771272324025631, 0.025054829195141792, 0.026616109535098076, 0.019701868295669556, -0.031262777745723724, 0.02724805660545826, -0.005641053896397352, -0.004869706928730011, 0.036968883126974106, 0.012332254089415073, -0.01692316122353077, -0.014516187831759453, 0.02581688202917576, 0.022248242050409317, -0.011941934004426003, 0.03063082881271839, 0.030612243339419365, -0.006886360701173544, 0.013633321039378643, 0.030649416148662567, 0.020110774785280228, 0.010036800988018513, -0.02687632292509079, -0.016040295362472534, -0.019181441515684128, -0.0061754207126796246, -0.016672242432832718, -0.02529645524919033, 0.009739413857460022, 0.00035314669366925955, -0.03230363130569458, -0.03605813533067703, -0.020203707739710808, -0.01341957412660122, 0.0065657407976686954, 0.015817254781723022, 0.025073416531085968, 0.0014021317474544048, 0.002918106969445944, -0.031727444380521774, 0.05449611321091652, -0.04163413867354393, 0.019924908876419067, -0.038177017122507095, 0.05631760507822037, -0.046541016548871994, -0.01670941524207592, 0.027303816750645638, 0.000437077134847641, -0.013057134114205837, -0.007248800713568926, -0.019739041104912758, -0.06479312479496002, -0.04085349664092064, 0.04836251214146614, -0.01207204069942236, -0.033995017409324646, 0.05248875170946121, 0.00962789449840784, 0.029961708933115005, -0.032749708741903305, 0.005980260670185089, -0.004727983847260475, -0.02176498807966709, 0.04048176482319832, 0.011430800892412663, 0.022304002195596695, -0.032136350870132446, -0.012592467479407787, -0.04144826903939247, 0.010659454390406609, 0.023976802825927734, -0.032563842833042145, -0.007838927209377289, -0.032006241381168365, 0.04512843117117882, 0.0009055192349478602, 0.010956840589642525, 0.026653282344341278, -0.009665067307651043, 0.010733800940215588, -0.009260807186365128, 0.006644733715802431, -0.022489868104457855, 0.017750268802046776, -0.0350358709692955, 0.008786847814917564, 0.03063082881271839, 0.05193115025758743, 0.0448310449719429, 0.011774654500186443, 0.026671869680285454, -0.02135608159005642, -0.00027821920230053365, -0.1362031102180481, 0.018586669117212296, -0.004075127188116312, 0.008419760502874851, -0.02064978890120983, -0.01599382795393467, -0.029775843024253845, -0.016932453960180283, -0.02241552248597145, -0.03869744390249252, 0.01193264126777649, 0.04494256526231766, 0.029961708933115005, -0.022062376141548157, 0.0037661236710846424, 0.02548232302069664, -0.07594512403011322, -0.015835842117667198, -0.0026950668543577194, 0.01288056094199419, 0.016746588051319122, 0.008763614110648632, 0.03364187106490135, 0.0067701940424740314, -0.0026950668543577194, -0.03574216365814209, -0.003928756806999445, -0.09092598408460617, -0.02483178861439228, -0.016412029042840004, 0.0010362067259848118, -0.06360357999801636, 0.013280174694955349, 0.052377231419086456, -0.009813761338591576, 0.001068733399733901, 0.0064588673412799835, 0.0009502434404566884, -0.014627708122134209, -0.03880896419286728, -0.009460614062845707, -0.001255761831998825, -0.015194601379334927, -0.022173894569277763, -0.0321735218167305, 0.09352811425924301, 0.015566335059702396, 0.002894873498007655, -0.06304597854614258, 0.015594215132296085, 0.004319076891988516, 0.01689528115093708, -0.020668374374508858, -0.015817254781723022, 0.010176201350986958, 0.02371658943593502, -0.027489682659506798, -0.012164974585175514, -0.014153747819364071, 0.06189360469579697, -0.03468272462487221, 0.05416155233979225, -0.027173709124326706, -0.012666814960539341, -0.014497601427137852, -0.039106350392103195, -0.059477340430021286, 0.0014637000858783722, -0.029181068763136864, 0.030054641887545586, -0.009739413857460022, 0.00412391684949398, -0.029738670215010643, -0.00364995701238513, 0.008591687306761742, -0.011356454342603683, 0.02172781527042389, 0.005557413678616285, -0.02005501464009285, 0.0014300118200480938, -0.04680123180150986, -0.024441469460725784, -0.03642987087368965, 0.04914315044879913, 0.05371547117829323, -0.007527600508183241, 0.0038427936378866434, 0.002609103685244918, -0.05616891011595726, -0.004458477254956961, -0.03248949721455574, -0.030928216874599457, -0.021783575415611267, -0.011384334415197372, -0.0034594435710459948, 0.0210215225815773, -0.018986281007528305, 0.00255334354005754, 0.01670941524207592, -0.05602021887898445, -0.03327013552188873, 0.02576112188398838, -0.04323258996009827, 0.010612987913191319, 0.00822924729436636, 0.09033121168613434, 0.011300694197416306, -0.011337867937982082, 0.03828853741288185, -0.0032549903262406588, -0.03717333823442459, -0.02200661599636078, 0.001696033519692719, 0.003886936930939555, -0.10126017034053802, -0.0345340296626091, 0.036318350583314896, 0.03421805799007416, -0.017183374613523483, 0.027322402223944664, 0.004337663762271404, 0.013317347504198551, -0.007462547160685062, -0.01986914873123169, -0.004391100257635117, 0.05717259272933006, 0.04869706928730011, 0.022434107959270477, 0.012964201159775257, 0.014367494732141495, 0.012471654452383518, 0.0023546984884887934, -0.0022606034763157368, 0.02817738987505436, 0.01868889480829239, 0.03206200152635574, -0.02148618921637535, 0.03124419040977955, -0.003605813719332218, -0.019831975921988487, -0.06616853922605515, 0.0034780302084982395, -0.01677446812391281, -0.01649566739797592, -0.03336307033896446, 0.0003121979534626007, -0.004256347194314003, 0.012109214439988136, -0.010947547852993011, -0.030463550239801407, 0.038548748940229416, -0.0035639936104416847, 0.01550128124654293, 0.005654993932694197, 0.015510574914515018, 0.035853683948516846, -0.007206980604678392, 0.019924908876419067, -0.006677260622382164, 0.06929109990596771, 0.029274003580212593, 0.004237760324031115, -0.031541574746370316, -0.014702054671943188, -0.02399538829922676, 0.039106350392103195, -0.0006435633986257017, 0.007114047184586525, -0.033065684139728546, 0.04401323199272156, 0.011365748010575771, 0.009971747174859047, -0.03118843026459217, 0.030054641887545586, 0.014590534381568432, -0.02358648180961609, 0.015371174551546574, 0.03793539106845856, 0.04048176482319832, 0.01972045563161373, 0.033028509467840195, 0.003807943779975176, 0.010854613967239857, 0.010910374112427235, -0.02297312207520008, 0.0015322384424507618, -0.008870487101376057, 0.05397568643093109, 0.0475446991622448, -0.02064978890120983, -0.023010294884443283, -0.03258242830634117, 0.0401843786239624, -0.01831716112792492, 0.06014645844697952, -0.015278241597115993, 0.03128136321902275, -0.005157800391316414, -0.010994014330208302, 0.02027805522084236, -0.03343741595745087, 0.013874948024749756, -0.004597877152264118, -0.020110774785280228, 0.003961283713579178, 0.0032480203080922365, -0.06259989738464355, -0.038177017122507095, -0.04457082971930504, -0.00645422050729394, 0.009465260431170464, 0.0006545992218889296, -0.02070554904639721, 0.018363628536462784, 0.019144268706440926, -0.020222295075654984, 0.03351176157593727, 0.017006801441311836, 0.01232296135276556, 0.011756068095564842, 0.027322402223944664, -0.0008160708821378648, 0.017211254686117172, -0.06988587230443954, -0.03996133804321289, -0.005027693696320057, 0.013187240809202194, 0.04092784225940704, 0.013131481595337391, 0.010008920915424824, 0.0011930317850783467, -0.015045908279716969, -0.005394780542701483, 0.04851120337843895, 0.04137392342090607, 0.01881900243461132, -0.0065332138910889626, 0.012081334367394447, -0.05483067035675049, -0.06159621849656105, 0.009767293930053711, 0.007569420617073774, 0.05431024357676506, -0.007750640623271465]', 'distance': 0.685951292514801}]\n"]}], "source": ["print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "query_data = load_dataset(\"princeton-nlp/LitSearch\", \"query\", split=\"full\")\n", "corpus_clean_data = load_dataset(\"princeton-nlp/LitSearch\", \"corpus_clean\", split=\"full\")\n", "corpus_s2orc_data = load_dataset(\"princeton-nlp/LitSearch\", \"corpus_s2orc\", split=\"full\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "for data in query_data:\n", "    print(json.dumps(data, ensure_ascii=False, indent=4))\n", "    break\n", "for data in corpus_clean_data:\n", "    print(json.dumps(data, ensure_ascii=False, indent=4))\n", "    break\n", "for data in corpus_s2orc_data:\n", "    print(json.dumps(data, ensure_ascii=False, indent=4))\n", "    break"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["50\n", "{\n", "    \"question\": \"Give me papers which show that using a smaller dataset in large language model pre-training can result in better models than using bigger datasets.\",\n", "    \"answer\": [\n", "        \"When Less is More: Investigating Data Pruning for Pretraining LLMs at   Scale\",\n", "        \"How to Train Data-Efficient LLMs\",\n", "        \"Deduplicating Training Data Makes Language Models Better\",\n", "        \"AlpaGasus: Training A Better Alpaca with Fewer Data\",\n", "        \"Distilling Step-by-Step! Outperforming Larger Language Models with Less Training Data and Smaller Model Sizes\",\n", "        \"LESS: Selecting Influential Data for Targeted Instruction Tuning\",\n", "        \"Automatic Document Selection for Efficient Encoder Pretraining\",\n", "        \"Farewell to aimless large-scale pretraining: Influential subset selection for language model\",\n", "        \"Babyllama-2: Ensemble-distilled models consistently outperform teachers with limited data.\"\n", "    ],\n", "    \"answer_arxiv_id\": [\n", "        \"2309.04564\",\n", "        \"2402.09668\",\n", "        \"2107.06499\",\n", "        \"2307.08701\",\n", "        \"2305.02301\",\n", "        \"2402.04333\",\n", "        \"2210.10951\",\n", "        \"2305.12816\",\n", "        \"2409.17312\"\n", "    ],\n", "    \"source_meta\": {\n", "        \"published_time\": \"20241001\"\n", "    },\n", "    \"qid\": \"RealScholarQuery_0\"\n", "}\n"]}], "source": ["from datasets import load_dataset\n", "import sys\n", "import json\n", "sys.path.append('E:\\\\project\\\\arxiv-insight\\\\backend')\n", "from utils.config import config\n", "from service.search import deep_semantic_search_old, semantic_search\n", "# query_data = load_dataset(\"princeton-nlp/LitSearch\", \"query\", split=\"full\")\n", "\n", "# 加载 E:\\project\\arxiv-insight\\backend\\data\\aaa_pasa\\AutoScholarQuery_test.txt 里边是 jsonl 格式\n", "# path = \"E:\\\\project\\\\arxiv-insight\\\\backend\\\\data\\\\aaa_pasa\\\\AutoScholarQuery_test.jsonl\"\n", "path = \"E:\\\\project\\\\arxiv-insight\\\\backend\\\\data\\\\aaa_pasa\\\\RealScholarQuery.jsonl\"\n", "with open(path, \"r\", encoding=\"utf-8\") as f:\n", "    data_list = [json.loads(line) for line in f]\n", "print(len(data_list))\n", "print(json.dumps(data_list[0], indent=4))  # 打印第一条数据验证"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["初始化 Qwen/Qwen2.5-72B-Instruct 成功 ~~~\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/50 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Query 50 | Top20(R:0.000,P:0.000),Len:2568 Top2000(R:0.302,P:0.003),Len:2568 Top10000(R:0.636,P:0.001),Len:2568 : 100%|██████████| 50/50 [04:30<00:00,  5.41s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "最终评估结果:\n", "Top20 - Recall: 0.0000, Precision: 0.0000\n", "Top2000 - Recall: 0.3018, Precision: 0.0028\n", "Top10000 - Recall: 0.6364, Precision: 0.0010\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "import asyncio\n", "import time\n", "from utils.llm import BaseLLM, BasePrompt, ModelSelector\n", "from service.search import MultiSearchPrompt\n", "import contextlib\n", "import io\n", "f = io.StringIO()\n", "\n", "llm_bg = BaseLLM(**ModelSelector.glm4_32b_z1)\n", "\n", "\n", "result_list = []\n", "output_fields = [\"primary_key\", \"title\", \"authors\", \"summary\", \"published\", \"source\", \"pdf_url\"]\n", "# output_fields = [\"primary_key\"]\n", "# results = [result for result in results if result['score'] > 6]\n", "# results = results[0:32]\n", "# 初始化统计变量\n", "\n", "prompts = [\n", "    '''\n", "    你是一个精通多语言（尤其是中文和英文）的学术研究助手，擅长理解和扩展用户查询。用户的查询可能是模糊的、包含缩写或非标准的。你的任务是：\n", "    1.  识别用户查询中的核心概念。\n", "    2.  对核心概念进行同义词、近义词替换。\n", "    3.  如果查询中包含常见的英文缩写，请给出其全称。如果查询中包含中文术语，也考虑其对应的常见英文表达或缩写。\n", "    4.  如果查询比较口语化或不清晰，尝试将其改写为更学术、更明确的表述。\n", "    5.  生成一个或多个与原始查询语义高度相关，但表述更清晰、更全面的扩增查询。\n", "\n", "    请输出扩增后的查询（用英文表示）。扩增后的查询之间用逗号隔开，用一行的形式给出即可，但是整体不要超过 100 个英文单词\n", "    ''',\n", "    '''\n", "    你是一位资深的领域专家和科研导航员。用户的查询代表了一个研究主题或方向。你的任务是：\n", "    1.  分析用户查询的核心主题。\n", "    2.  基于该核心主题，联想并列出与之紧密相关的其他研究主题、子领域、或者经常伴随出现的技术/概念。\n", "    3.  这些扩增的查询应当有助于用户发现更广泛但依然相关的论文。\n", "\n", "    请输出扩增后的查询（用英文表示）。扩增后的查询之间用逗号隔开，用一行的形式给出即可，但是整体不要超过 100 个英文单词\n", "    ''',\n", "    '''\n", "    你是一位经验丰富的科研方法论顾问。用户的查询可能是一个现象、一个技术或一个概念。你的任务是：\n", "    1.  理解用户查询的本质。\n", "    2.  从研究问题的角度出发，思考与该查询相关的：\n", "        *   可能的研究问题或假设。\n", "        *   常用的研究方法、实验技术或分析模型。\n", "        *   该技术/概念的应用场景或解决的问题。\n", "    3.  生成的扩增查询应能帮助用户找到探讨这些具体问题或方法的论文。\n", "\n", "    请输出扩增后的查询（用英文表示）。扩增后的查询之间用逗号隔开，用一行的形式给出即可，但是整体不要超过 100 个英文单词\n", "    ''',\n", "    '''\n", "    你是一位知识图谱构建专家，擅长识别概念间的层级关系和交叉联系。用户的查询是一个特定的概念或技术。你的任务是：\n", "    1.  分析用户查询的核心概念。\n", "    2.  列出该概念的上位概念（更广义的类别）和一些典型的下位概念（更具体的实例或子类）。\n", "    3.  思考该概念可能涉及的交叉学科领域或与其他领域技术的结合点。\n", "    4.  生成的扩增查询应有助于用户在更广阔的知识体系中定位相关研究。\n", "\n", "    请输出扩增后的查询（用英文表示）。扩增后的查询之间用逗号隔开，用一行的形式给出即可，但是整体不要超过 100 个英文单词\n", "    ''',\n", "    '''\n", "    你是一位富有洞察力的信息检索策略师。用户的查询往往只表达了部分意图。你的任务是：\n", "    1.  理解用户查询字面意思背后的潜在提问角度。\n", "    2.  将原始查询（无论是关键词还是问题）改写成多种不同的问句形式，或从不同方面进行阐述。\n", "        *   例如，如果 query 是 \"X\"，可以扩增为 \"X 的影响是什么？\", \"如何实现 X？\", \"X 的最新进展？\", \"X 的优缺点？\"。\n", "    3.  生成的扩增查询应能覆盖用户可能想了解的关于该主题的多个方面。\n", "\n", "    请输出扩增后的查询（用英文表示）。扩增后的查询之间用逗号隔开，用一行的形式给出即可，但是整体不要超过 100 个英文单词\n", "    ''',\n", "]\n", "\n", "\n", "async def dense_search(query, depth=3, t=0.6):\n", "    # 查询扩增\n", "    async def get_expanded_queries(question, num_queries, t=0.6):\n", "        tasks = [\n", "            llm_bg.ask_async(question, system_prompt=prompt, temperature=t, max_tokens=50)\n", "            for prompt in prompts\n", "        ]\n", "        exp_queries = await asyncio.gather(*tasks)\n", "        return exp_queries\n", "    \n", "    querys = await get_expanded_queries(data[\"question\"], depth, t=0.6)\n", "    querys.append(query)\n", "\n", "    # 对每个扩增的查询进行检索\n", "    all_results = []\n", "    for exp_query in querys:\n", "        results = await semantic_search(\n", "            exp_query, limit=1000,\n", "            source=['arxiv'], output_fields=output_fields\n", "        )\n", "        all_results.extend(results)\n", "    \n", "    # 通过 primary_key 去重\n", "    seen_keys = set()\n", "    unique_results = []\n", "    for result in all_results:\n", "        if result['primary_key'] not in seen_keys:\n", "            seen_keys.add(result['primary_key'])\n", "            unique_results.append(result)\n", "\n", "    # 按照相似度排序，越小越靠前\n", "    unique_results = sorted(unique_results, key=lambda x: x['distance'])\n", "            \n", "    return unique_results\n", "\n", "tops = ['20', '2000', '10000']\n", "\n", "metrics = {\n", "    'recall': {top: 0 for top in tops},\n", "    'precision': {top: 0 for top in tops}\n", "}\n", "total_queries = 0\n", "pbar = tqdm(data_list)\n", "for data in pbar:\n", "    with contextlib.redirect_stdout(f):\n", "        results = await dense_search(data[\"question\"], depth=10, t=0.6)\n", "        # results = await deep_semantic_search_old(\n", "        #     data[\"question\"], limit=200, match_limit=2,\n", "        #     source=['arxiv'], use_sub_summary=True, output_fields=output_fields\n", "        # )\n", "    # result_list.append({\n", "    #     \"query\": data,\n", "    #     \"result\": results\n", "    # })\n", "    # real_ids = data[\"corpusids\"] # 真实标签 id\n", "    # real_ids = [str(id) for id in real_ids]\n", "    # result_ids = [result[\"primary_key\"].split(\"v\")[0].replace('.','') for result in results] # 召回的结果的 id\n", "    real_ids = data[\"answer_arxiv_id\"] # 真实标签 id\n", "    real_ids = [id.split(\"v\")[0] for id in real_ids]\n", "    result_ids = [result[\"primary_key\"].split(\"v\")[0] for result in results] # 召回的结果的 id\n", "    \n", "    # 计算各个top-k的指标\n", "    for top in tops:\n", "        k_int = int(top)\n", "        top_k_ids = set(result_ids[:k_int])\n", "        metrics['recall'][top] += len(top_k_ids.intersection(real_ids)) / len(real_ids)\n", "        metrics['precision'][top] += len(top_k_ids.intersection(real_ids)) / k_int\n", "    \n", "    total_queries += 1\n", "    \n", "    # 在进度条左侧显示当前评估结果\n", "    status_text = f\"Query {total_queries} | \"\n", "    for top in tops:\n", "        curr_recall = metrics['recall'][top] / total_queries\n", "        curr_precision = metrics['precision'][top] / total_queries\n", "        status_text += f\"Top{top}(R:{curr_recall:.3f},P:{curr_precision:.3f}),Len:{len(results)} \"\n", "    pbar.set_description(status_text)\n", "\n", "# 计算并打印最终平均结果\n", "print(\"\\n最终评估结果:\")\n", "for top in tops:\n", "    avg_recall = metrics['recall'][top] / total_queries\n", "    avg_precision = metrics['precision'][top] / total_queries\n", "    print(f\"Top{top} - Recall: {avg_recall:.4f}, Precision: {avg_precision:.4f}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ds' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mds\u001b[49m\u001b[38;5;241m.\u001b[39mcache_files)\n", "\u001b[1;31mNameError\u001b[0m: name 'ds' is not defined"]}], "source": ["print(ds.cache_files)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13.4321\n"]}], "source": ["import requests\n", "import json\n", "\n", "url = \"https://api.siliconflow.cn/v1/user/info\"\n", "\n", "headers = {\"Authorization\": \"Bearer sk-rupkhcgpmflubkiqeoqziwowjowherpaumdnrvrakerlnwuv\"}\n", "\n", "response = requests.request(\"GET\", url, headers=headers)\n", "result = json.loads(response.text)\n", "print(result['data']['balance'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "\n", "pdf_url = 'https://arxiv.org/pdf/2502.02584v1'\n", "\n", "# 创建tmp目录（如果不存在）\n", "tmp_dir = r'E:\\project\\arxiv-insight\\backend\\service\\tmp'\n", "\n", "# 下载PDF文件\n", "pdf_path = os.path.join(tmp_dir, 'input.pdf')\n", "response = requests.get(pdf_url)\n", "with open(pdf_path, 'wb') as f:\n", "    f.write(response.content)\n", "\n", "import pymupdf # imports the pymupdf library\n", "doc = pymupdf.open(pdf_path) # open a document\n", "text_all = ''\n", "for page in doc: # iterate the document pages\n", "  text = page.get_text() # get plain text encoded as UTF-8\n", "  text_all += text"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'QLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nZongyu Lin 1 * Yao Tang 2 * Xingcheng Yao 1 * Da Yin 1 * Ziniu Hu 1 Yizhou Sun 1 † Kai-Wei Chang 1 †\\nAbstract\\nLanguage agents have become a promising solu-\\ntion to complex interactive tasks. One of the key\\ningredients to the success of language agents is\\nthe reward model on the trajectory of the agen-\\ntic workflow, which provides valuable guidance\\nduring training or inference. However, due to\\nthe lack of annotations of intermediate interac-\\ntions, most existing works use an outcome reward\\nmodel to optimize policies across entire trajecto-\\nries. This may lead to sub-optimal policies and\\nhinder the overall performance. To address this,\\nwe propose QLASS (Q-guided Language Agent\\nStepwise Search), to automatically generate an-\\nnotations by estimating Q-values in a stepwise\\nmanner for open language agents. By introducing\\na exploration tree and performing process reward\\nmodeling, QLASS provides effective intermediate\\nguidance for each step. With the stepwise guid-\\nance, we propose a Q-guided generation strategy\\nto enable language agents to better adapt to long-\\nterm value, resulting in significant performance\\nimprovement during model inference on complex\\ninteractive agent tasks. Notably, even with almost\\nhalf the annotated data, QLASS retains strong\\nperformance, demonstrating its efficiency in han-\\ndling limited supervision. We also empirically\\nshow that QLASS can lead to more effective deci-\\nsion making through qualitative analysis. 1\\n1. Introduction\\nSupervised fine-tuning (SFT) is commonly employed to\\nmake base LLMs perform effective reasoning and planning\\nin complex agent tasks by imitating expert trajectories (Chen\\net al., 2023; Yin et al., 2024). However, the substantial hu-\\n* Equal contribution. † Equal advising.\\n1University of Califor-\\nnia, Los Angeles, USA 2Shanghai Jiaotong University, Shanghai,\\nChina. Correspondence to: Yizhou Sun <<EMAIL>>,\\nKai-Wei Chang <<EMAIL>>.\\n1We will release our code and data in https://github.\\ncom/Rafa-zy/QLASS\\n1. Behavior Cloning\\n2. Self-Generation & Tree Construction\\nEnvironment\\nSFT Dataset\\nLLM Agent\\nu\\na2\\n0\\ns3\\na4\\na5\\na1\\ns2\\ns4\\ns5\\ns1\\na3\\n0.5\\n0.9\\n0.9\\n0\\nu: task\\ns: state\\na: action\\nq: Q-value\\n(s1,a1,q1)\\n(s2,a2,q2)\\n...\\n3. Train QNet\\n4. Q-guided Generation\\na1\\na2\\na3\\nQNet\\n0.9\\n0.1\\n0.8\\na1\\n...\\nQNet\\nFigure 1. QLASS pipeline overview. QLASS involves mainly four\\nstages: 1) Supervised fine-tuning (SFT) on expert data. 2) Leverage\\nSFT agent to explore the environment and construct an exploration\\ntree for each task. After construction, estimate the Q-value of each\\ntree node based on Equation 7. 3) Train QNet on the estimated\\nQ-values. 4) Use the trained QNet to provide inference guidance\\nat each step.\\nman annotations required to collect training data present a\\nsignificant bottleneck, limiting both performance and scal-\\nability. This challenge is particularly pronounced in agent\\ntasks (Yao et al., 2022; Shridhar et al., 2021; Wang et al.,\\n2022a), where data scarcity is a critical issue due to the\\ninherent complexity and diversity of real-world interactions.\\nTo overcome this challenge, self-improvement techniques\\nhave shown to be a promising area of research, enabling\\nLLMs to learn from self-generated data without extensive\\nhuman intervention (Wang et al., 2022b; Singh et al., 2023;\\nHosseini et al., 2024; Zhang et al., 2024), and most recently,\\nenable LLMs to improve their outputs by scaling up their\\ntest-time computation in a more intelligent way (Wang et al.,\\n2024; Shinn et al., 2023; Snell et al., 2024). Inspired by\\nthis, we focus on improving the inference-time search for\\nlanguage agents, which is a crucial technique for the success\\nof more generalizable agents in real-world environments.\\nAn essential component of inference-time scaling methods\\nis the reward model (Snell et al., 2024), which evaluates the\\nquality of self-explored data. Many existing works derive a\\nsingle outcome reward based on ground-truth (Wang et al.,\\n2024; Shinn et al., 2023). Although this approach is straight-\\nforward, it falls short in handling complex agent tasks, since\\nan outcome-reward model cannot accurately score each step\\nwithin a long trajectory in intricate scenarios. In addition, a\\ntrajectory achieving a high final outcome reward does not\\n1\\narXiv:2502.02584v1  [cs.LG]  4 Feb 2025\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nnecessarily indicate that every action taken was optimal; the\\nagent may have completed the task successfully, but some\\nactions could have been inefficient or suboptimal (Uesato\\net al., 2022).\\nTherefore, a good process reward model is necessary to\\nlearn from the environmental feedback and provide step-\\nwise evaluations of agent actions. This model enables the\\nagent to fully understand and learn from the intermediate\\nstages of complex tasks, ultimately improving performance\\nand generalization. The key challenge lies in developing an\\neffective process reward model for self-improvement with-\\nout relying on extensive human annotations for the stepwise\\nreward. There has been a thread of work that has focused\\non process reward modeling (Uesato et al., 2022; Lightman\\net al., 2023; Wang et al., 2023; Chen et al., 2024). However,\\nthese methods rely on either costly human annotation or\\ncomputationally heavy random rollouts, rendering them in-\\nefficient for the self-improvement of language model agents.\\nTo reduce the annotation reliance on process rewards, we\\npropose QLASS to perform effective process reward mod-\\neling to guide agent inference. Specifically, we explicitly\\nformalize the self-generated exploratory trajectories as ex-\\nploration trees and update the process rewards of all the\\ntree nodes based on the tree structures. To better capture\\nthe future utility at each step of a multi-turn reasoning pro-\\ncess, we employ the Bellman equation (Bellman & Dreyfus,\\n2015) to learn a Q-based process reward. Unlike simple\\noutcome-based rewards, this Q-value captures how imme-\\ndiate decisions contribute to longer-term payoffs, enabling\\nfiner-grained control over the reasoning trajectory. More-\\nover, the Bellman update rule iteratively refines the esti-\\nmated Q-values by propagating future rewards back to ear-\\nlier states, reducing reliance on sparse or delayed feedback\\nsignals. This allows us to efficiently gather supervision for\\nstate-action pairs without requiring explicit annotations of\\nfull trajectories. With these Q values in hand, we can then\\ntrain a function approximator (QNet) (Watkins & Dayan,\\n1992) to predict the expected return of any partial solu-\\ntion, ultimately providing a strong inductive bias to guide\\nopen-language agents. By prioritizing actions with higher\\nestimated Q values, the agent steers its own reasoning in a\\nmore targeted manner, facilitating efficient stepwise plan-\\nning within expansive search spaces.\\nWhile recent attempts like KIMI-k1.5 (Team et al., 2025)\\nand Deepseek-R1 (Guo et al., 2025) report failures in pro-\\ncess reward modeling, we argue that such modeling is indis-\\npensable for agent tasks. Back-and-forth agent behaviors\\ninherently create stepwise inefficiencies (e.g., repetitive en-\\nvironment queries or cyclic reasoning), which the sparse\\noutcome rewards cannot diagnose. Our Q-value estimation\\ndirectly addresses this by propagating future utility back-\\nward through trajectories, dynamically pruning actions with\\nlow reward while preserving critical decision points. This\\nenables agents to disentangle productive reasoning from\\nwasteful loops, even with limited supervision. To summa-\\nrize, our contribution can be divided into three folds:\\n1) Process Reward Modeling with Q-Value Estimation:\\nWe introduce QLASS, a novel strategy that leverages es-\\ntimated Q-values to generate intermediate annotations for\\nlanguage agents, providing stepwise guidance for model\\ninference. We visualize the overall framework of QLASS\\nin Figure 1.\\n2) Q-Guided Generation Strategy: We propose a Q-\\nguided generation technique that significantly enhances\\nagent performance via process-based guidance during infer-\\nence, ensuring effective decision making at each step.\\n3) Superior Performance with Limited Supervision:\\nQLASS shows strong performance on a set of diverse agent\\nenvironments, including WebShop, ALFWorld, and Sci-\\nWorld. QLASS can give effective inference-time guidance\\neven when nearly half of the annotated data is reduced.\\nThese experimental results highlight the efficiency and ro-\\nbustness of QLASS in scenarios with limited supervision.\\n2. Related Work\\n2.1. Large Language Model Agent\\nLarge language models have shown impressive performance\\nin complex interactive tasks, such as web navigation (Yao\\net al., 2022), scientific reasoning (Wang et al., 2022a), and\\naction planning in embodied environments (Shridhar et al.,\\n2021). ReAct (Yao et al., 2023) developed a prompting\\nmethod to shape language models as agents that can reason\\nand act. While several works (Shen et al., 2024; Song et al.,\\n2023) improve agent performance with closed-source LLM\\ncontrollers, the open-source LLM agents still offer unique\\nadvantages like accessibility and customization. FireAct\\n(Chen et al., 2023) and LUMOS (Yin et al., 2024) leverage\\nhigh-quality data generated by experts and employ teacher-\\nforcing to improve the performance of open-source agents.\\nIn line with this, our QLASS is also based on open-source\\nLLMs.\\n2.2. Self-Improvement for LLM\\nThe self improvement of LLM can be a good way to im-\\nprove LLM without heavy human annotation, which can\\nbe divided into two parts. (1) Training models on self-\\ngenerated data is a promising approach. A large number\\nof works (Dou et al., 2024; Wang et al., 2022b; Yuan et al.,\\n2023; Singh et al., 2023; Gulcehre et al., 2023; Wang et al.,\\n2023) follow the paradigm of self-training, which filters\\npositive self-generated data and performs model training on\\nthose filtered data. Some other works (Song et al., 2024;\\n2\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nSetlur et al., 2024) utilize both positive and negative data to\\nconstruct preference pairs and update the policy using direct\\npreference optimization (Rafailov et al., 2024). (2) Another\\napproach is to scale up the computation of inference to im-\\nprove the outputs of LLMs. The methods include guiding\\nthe inference based on scalar-based reward models (Wang\\net al., 2024; Xu et al., 2022; Zhai et al., 2024) and modifying\\nthe output conditioning on the language feedback (critique\\nprovided by the LLM itself or another critique LLM) (Zhou\\net al., 2024; Wu et al., 2024; Shinn et al., 2023). In our\\npaper, we focus on the self-improvement at inference time\\nusing our proposed process reward models.\\n2.3. Process Reward Modeling for LLM\\nExisting works have explored various strategies and rea-\\nsoning policies for process reward modeling.\\n(Uesato\\net al., 2022) and (Lightman et al., 2023) utilize human-\\nannotated step-level correctness to train a reward model.\\nMath-Shepherd (Wang et al., 2023) infers per-step rewards\\nthrough random rollouts. TS-LLM (Feng et al., 2023) em-\\nploys an MCTS-based policy and infers per-step rewards us-\\ning the TD-λ (Sutton, 1988) method. ReST-MCTS* (Zhang\\net al., 2024) uses Monte Carlo tree search (MCTS) with\\nre-inforced self-training to enhance the diversity and per-\\nformance on general reasoning tasks like maths, science,\\nand code. Most recently, Wang et al. (2024) and Zhai\\net al. (2024) also use step-level guidance for agent infer-\\nence through training a step-level value model. Putta et al.\\n(2024) applies a hybrid process reward modeling for web\\nnavigation tasks by combining Monte Carlo Tree Search\\n(MCTS) rewards with scores generated by large language\\nmodels to form process rewards. Our approach focuses on\\nsolving complex agent tasks by providing effective per-step\\nguidance for LLM agent inference. Our method differs from\\nPutta et al. (2024) because we do not rely on a strong propri-\\netary LLM to provide rewards. Compared with Wang et al.\\n(2024) and Zhai et al. (2024), we shift our focus on more\\ncomplex agent tasks with larger search space and deeper\\nsearch depth like ALFWorld and SciWorld. Compared with\\nZhang et al. (2024), our framework is much simpler with\\nless stages of training and more straightforward to make pro-\\ncess reward modeling works better compared with strong\\ntraining-based baselines.\\n3. Preliminaries\\nIn this section, we introduce Q-learning, the key algorithm\\nthat inspires QLASS to extract Q-values from the explo-\\nration trees. Q-learning (Watkins & Dayan, 1992) is a tradi-\\ntional model-free reinforcement learning algorithm, where\\na value function Q(s, a) is trained to represent the expected\\nfuture rewards by taking action a given state s. The optimal\\nQ-function can be written as,\\nQ⋆(s, a) = max\\nπ\\nE[rt + γrt+1 + γ2rt+2 + ⋅⋅⋅∣\\nst = s, at = a, π],\\n(1)\\nwhere π is the policy, γ is the discount factor, and rt is\\nthe received reward at step t. Given the definition of op-\\ntimal Q-fucntion in Equation 1, the Bellman Optimality\\nEquation (Bellman & Dreyfus, 2015) of Q-function can be\\nwritten as,\\nQ⋆(st, at) = rt + γ max\\na∈A Q⋆(st+1, a).\\n(2)\\nIn Q-learning, the value model Q(st, at) is updated itera-\\ntively by,\\nQ(st, at) ←(1 −α)Q(st, at)\\n(3)\\n+ α(rt + γ max\\na∈A Q(st+1, a)),\\n(4)\\nwhere α is the learning rate and A is the action space. Com-\\nbining immediate rewards from the current action and future\\npotential rewards from subsequent actions, Q-value can be\\ninterpreted as the expected long-term value of taking a spe-\\ncific action in a given state.\\nIn complex interactive tasks, the agent needs to account\\nnot only for immediate rewards but also for the potential\\nlong-term effects of its current decisions. This is where\\nthe Q-value becomes essential. However, directly adapting\\nRL algorithms such as Q-learning to language agents can\\nbe sample-inefficient (Jin et al., 2018). This is because the\\naction space in language agent tasks is typically a vast vo-\\ncabulary, which may lead to an explosion of potential action\\nsequences to be explored. To address this challenge, our\\napproach successfully adapts Q-value extraction to language\\nagent tasks by introducing an exploration tree, which we\\nwill introduce in the next section.\\n4. QLASS Pipeline Details\\nIn this section, we will follow the order of QLASS pipeline\\nand introduce each critical component step by step. The\\noverall pipeline is shown in Figure 1 and Algorithm 1.\\nFirst, we will describe the initial stage of behavior cloning.\\nThen, we will explain how the exploration tree is constructed\\nduring the second self-generation stage and how we use it to\\nextract Q-values as the supervision to train Q-network (QNet\\nTraining). Finally, we will detail the Q-guided generation\\nhow the QNet is employed to guide the agent’s test-time\\ninference in a stepwise manner.\\n4.1. Behavioral Cloning\\nBehavior cloning provides a strong initial foundation for\\nlanguage agents by supervised fine-tuning on expert trajec-\\ntories. Formally, the first stage of QLASS is to supervised\\n3\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nAlgorithm 1 General QLASS Pipeline\\nInput: Expert dataset Dexpert = {(ui, ai\\nt, oi\\nt)T\\nt=1}N\\ni=1,\\npolicy πθ, QNet Qϕ\\nStage 1: Behavior Cloning\\nTrain πθ on Dexpert minimizing loss 5\\nStage 2: Construct Reasoning Trees\\nfor i = 1 to N do\\nConstruct a reasoning tree with Algorithm 2\\nUpdate Q-values recursively with Equation 7\\nend for\\nCollect Q-values from {Ti}N\\ni=1 as dataset DQ\\nStage 3: QNet Training\\nTrain QNet Qϕ on dataset DQ\\nStep 4: Q-guided Generation\\nUse QNet Qϕ to score state-actions at each step\\nfine-tune our language agent, denoted as the policy π, on a\\nset of annotated samples Dexpert. We use ReAct (Yao et al.,\\n2023)-style data for supervised fine-tuning, which addition-\\nally generates Chain-of-Thought (CoT) (Wei et al., 2022)\\nreasoning paths before executing each action. We will use a\\nto denote the complete ReAct-style response generated by\\nπ for simplicity.\\nFormally, given a dataset Dexpert = {(ui, ai\\nt, oi\\nt)T\\nt=1}N\\ni=1,\\nwhere ui represents the task description, T is the trajectory\\nlength, N is the number of trajectories in the expert dataset,\\noi\\nt is the environment observation after taking action ai\\nt at\\nstep t, we optimize the policy π by minimizing the negative\\nlog-likelihood loss:\\nL(θ) = −∑\\ni\\n∑\\nt\\nlog πθ(ai\\nt ∣ui, ai\\n<t, oi\\n<t),\\n(5)\\nwhere θ denotes the parameters of the policy model πθ,\\nwhich outputs the probability of action a given task descrip-\\ntion u and historical interactions ht = {a<t, o<t}.\\n4.2. Constructing an Exploration Tree\\nThe supervised fine-tuned agents can explore the environ-\\nment and collect a large amount of trajectories. However,\\ndue to the extremely large search space of language agents,\\ndirectly sampling trajectories without any guidance may\\nlead to low efficiency. To address this issue, we propose to\\nconstruct an exploration tree during self-generation.\\n4.2.1. TREE STRUCTURE\\nFor a trajectory, we take the task description as the root\\nnode, and each node below the root node is composed of the\\nstate, action, and related information for each step. For all\\ntrajectories of a task, they can be seen as different branches\\nthat originate from the same root node.\\nSpecifically, a Tree Node N in an exploration tree T is\\ndefined as a set of the following attributes:\\nState (st): Represents the accumulated historical context\\nfrom the initiation of the process up to the current time step\\nt, encapsulating all preceding reasoning paths and actions.\\nFormally, the state at time t is given by\\nst = {u, a1, o1, . . . , at−1, ot−1},\\n(6)\\nincluding the task description u and history at step t.\\nAction (at): denotes the specific operation performed at the\\ncurrent node, which affects the subsequent state. The action\\nis selected by the policy language agent π and is conditioned\\non the current state and reasoning path.\\nReward (rt): the immediate feedback received from envi-\\nronment after performing action at. In most language agent\\ntasks, the immediate rewards from environments are set to\\nzero or very sparse. For example, WebShop (Yao et al.,\\n2022) only provides a final reward from 0 to 1 at the end of\\ntrajectories.\\nChildren (C): is represented by a list containing nodes\\nexplored at the next step.\\nQ-value (q): represents the expected total future reward\\nachievable starting from the current state st, taking action\\nat. The Q-values are updated once an exploration tree is\\nconstructed. We will introduce how we extract Q-values in\\nthe following section.\\n4.2.2. TREE CONSTRUCTION\\nWith each step in a trajectory formalized as a TreeNode,\\nthe entire trajectory is a branch within an exploration tree.\\nTo explicitly construct an exploration tree that captures po-\\ntential generations from the root node (i.e., the initial task),\\nexploring new trajectories can be viewed as expanding new\\nbranches from the existing TreeNodes. For any non-leaf tree\\nnode, effective generation can be achieved by: 1) directly\\nexploring and adding new child nodes that differ from the\\nexisting ones. 2) For each branch that reaches a leaf node,\\nwe assess its quality based on the final reward provided by\\nthe environment. If the branch yields a zero reward, we\\nstop generation on that branch’s nodes, thereby reducing\\nineffective generation. We introduce our strategy for tree\\nconstruction in detail as follows.\\nTree Pruning. In practice, we have found that the average\\ndepths of tree searching for agent tasks are large. Build-\\ning an exploration tree and expanding every potential tree\\nnodes may lead to heavy cost to the trajectory generation.\\nTo address this, we propose several strategies to reduce the\\ncomputational burden during tree construction. We employ\\npre-pruning techniques to lower the generation costs when\\nconstructing an exploration tree for each task. First, we limit\\n4\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nReasoning Tree\\nTask 𝒖\\nI’d like to buy a pair of black pants for a cocktail party.\\n𝐚𝟏: Search [Black causal pants]\\ns𝟏: [𝒖]\\ns𝟐: [𝒖, 𝒂𝟏, 𝒐𝟏]\\n𝐚𝟐: Click [Item B]\\n𝐫𝟐: 𝟎\\n𝒓𝟏: 𝟎\\n𝒐T:Task is completed!\\n𝐫𝑻: 𝟎. 𝟑\\n𝒐T: Task is not completed!\\n𝐫𝑻: 𝟎\\nStop\\nexpansion\\ns𝟏: [𝒖]\\n𝐚𝟏: Search [black pants]\\n𝒓𝟏: 𝟎\\ns𝟐: [𝒖, 𝒂𝟏, 𝒐𝟏]\\n𝐚𝟐: Click [Item A]\\n𝐫𝟐: 𝟎\\n𝒐T:Task is completed!\\n𝐫𝑻:𝟎. 𝟗\\nKeep\\nexpansion\\ns𝟐: [𝒖, 𝒂𝟏, 𝒐𝟏]\\n𝐚𝟐: Click [Item C]\\n𝐫𝟐: 𝟎\\nFigure 2. Illustrative example of constructing a exploration tree. Grey nodes represent the branches with a zero outcome reward. Once the\\nleaf node with a zero outcome reward is detected, a Stop expansion signal will be sent back to the first unexpanded node on the\\nbranch. Green nodes are on branches where zero outcome reward is not detected and can keep expanding.\\nthe expansion of tree nodes to the early stages of a trajectory\\n(e.g., the first three to five steps, depending on the environ-\\nment’s complexity, with details provided in Appendix A.2).\\nNext, when a branch leads to a zero-outcome reward at its\\nleaf node, we propagate a Stop expansion signal from\\nthe leaf node back to the earliest unexpanded intermediate\\nnode on that branch. This helps prioritize the generation of\\noptimal trajectories given a limited generation budget. This\\nconstruction process is illustrated in Figure 2.\\nWith a set of exploration trees, we aim to efficiently gather\\neffective step-wise signals for training an effective process\\nreward model. Since most language agent tasks only return\\nan outcome reward at the end of the trajectory, which is\\nstored at the leaf nodes of the exploration tree, we need\\nto develop methods to leverage these outcome rewards to\\ngenerate effective intermediate signals.\\nExtracting Q-values. After constructing an exploration\\ntree, with the outcome rewards stored in leaf node rewards,\\nwe estimate the Q-values for each intermediate node lever-\\naging\\nQ(st, at) = rt + γ max\\nat+1∼Ct[Q(st+1, at+1)],\\n(7)\\nwhere γ is the discount factor, st+1 is the new state after\\naction at, Ct is the children set containing nodes explored\\nat the next step, and the expectation is over actions at+1\\ndrawn from the policy π. We provide the pseudocode of\\ntree construction and Q-value estimation on the exploration\\ntrees in Appendix A.3.1.\\n4.3. QNet Training\\nInspired by the value function representing the expected\\nlong-term value in Q-learning (Watkins & Dayan, 1992),\\nwe extract Q-values for each node on the exploration trees\\nusing Equation 7. For each node N = (s, a, q, . . . ) in the\\ncollected exploration trees, we can then construct a super-\\nvised dataset DQ = {(s, a, q)} to train a Q-network (QNet),\\ninitialized from a supervised-fine-tuned LLM. Directly ap-\\nplying online Q-learning in language settings is often im-\\npractical, due to the unbounded action space of natural lan-\\nguage and the sparse nature of rewards. Instead, by labeling\\neach node with a corresponding Q-value offline and then\\ntraining QNet in a purely supervised manner, we bypass the\\ninstability and excessive exploration costs that typical rein-\\nforcement learning loops would incur in high-dimensional\\nlanguage environments. The model architecture of QNet is\\nintroduced in Appendix A.2.2.\\nTraining Objective. Given each exploration tree T with n\\nnodes: T = (N1, N2, . . . , Nn), we train the QNet Qϕ by\\nminimizing the Mean Squared Error (MSE) loss between\\nthe predicted Q-values ˆqt and the target Q-value q calculated\\npreviously at each time step,\\nL(ϕ) = 1\\nn\\nn\\n∑\\nt=1\\n(ˆqt −qt)2 .\\n(8)\\nBy minimizing this loss, we encourage the QNet to pro-\\nduce consistent Q-value estimations across the sequence\\nthat align with the target Q-value q. This training objective\\nemphasizes accurate Q-value predictions at each token, re-\\ninforcing the model’s ability to assess the long-term value\\nof actions throughout the trajectory.\\n4.4. Q-Guided Generation\\nThe effectiveness of a good process reward model can be\\nrepresented by whether it can lead to better agent self-\\nimprovement. Therefore, we conduct Q-guided genera-\\ntion for self-improvement to evaluate the effectiveness of\\nQLASS. Q-guided generation enables agents to generate\\neach step under the guidance of QNet. At each step, agents\\nsample several actions and the one with the highest Q-value\\nis executed by the agent. We provide a more detailed algo-\\nrithm of Q-guided generation in Appendix A.3.2.\\n5\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nTable 1. The statistics of datasets (We follow the same setup as\\nETO (Song et al., 2024)). “Test-Seen” and “Test-Unseen” are test\\nsets with seen and unseen cases respectively. “#Turns” means the\\naverage number of interaction turns for the SFT trajectories.\\nDataset\\n#Train\\n#Test-Seen\\n#Test-Unseen\\n#Turns\\nWebShop\\n1,938\\n200\\n-\\n4.9\\nSciWorld\\n1,483\\n194\\n241\\n14.4\\nALFWorld\\n3,321\\n140\\n134\\n10.1\\nIn this section, we introduce QLASS, a strategy that lever-\\nages Q-value estimation for process reward modeling, pro-\\nviding step-wise guidance for language agents. Additionally,\\nwe propose a Q-guided generation strategy that enhances\\ndecision-making of the language agent by using Q-values\\nto drive more effective generation during inference.\\n5. Experiment\\nIn this section, we aim to evaluate the effectiveness of\\nQLASS for solving complex agent tasks in the following\\naspects: 1) whether QLASS can aid better decision making\\non different complex agent tasks; 2) whether the Q-value\\nin QLASS is an effective process reward to facilitate self-\\nimprovement; 3) whether QLASS can retain strong perfor-\\nmance with reduced annotated data.\\n5.1. Setup\\nDatasets.\\nWe assess the ability of QLASS on Web-\\nShop (Yao et al., 2022), ALFWorld (Shridhar et al., 2021)\\nand SciWorld (Wang et al., 2022a). These environments\\nonly provide a single outcome reward at the end of each\\ntrajectory. The statistics of three agent datasets are dis-\\nplayed in Table 1. The evaluation metric is the reward\\naveraged on the test sets. During the sampling process,\\nenvironments will give a termination signal when certain\\nactions like “Click[Buy Now]” in Webshop are taken or the\\nset maximum steps are reached. Details can be found in\\nAppendix A.2.\\nTraining Setup. In our work, we mainly use Llama-2-7B-\\nChat as base policy model and QNet backbone. We train our\\nmodels mainly using 4 or 8 A6000 GPUs. The experiments\\non Webshop, including the training of SFT model, QNet,\\nself-generation and Q-guided exploration, takes one or two\\ndays and the experiments on ALFWorld and SciWorld takes\\nfour or five days. The detailed hyper-parameters for training\\nand model architectures can be found in Appendix A.2.\\nBaselines. 1) SFT (Chen et al., 2023) is the base agent\\nafter supervised fine-tuning on the expert data. 2) RFT\\n(Rejection sampling Fine-Tuning) (Yuan et al., 2023) is a\\nself-improvement baseline which is trained on the merged\\ndata consisting of successful trajectories sampled and expert\\ndata. 3) ETO (Song et al., 2024) is a self-improvement base-\\nline which updates policy via constructing trajectory-level\\npreference pairs and conducting DPO. 4) PPO (Proximal\\nPolicy Optimization) (Schulman et al., 2017): a reinforce-\\nment learning baseline which directly trains the base agents\\nto optimize the final rewards. 5) Best-of-N samples N tra-\\njectories for each task and selects the one with the highest\\noracle outcome reward.\\nN is set to 6 in Table 2 and Table 3. All the inference-time\\nbaselines in the tables are under the same search budget\\nfor fair comparison. 6) Closed-source agents: GPT-3.5-\\nTurbo and GPT-4 with ReAct prompting (Yao et al., 2023),\\nand methods depending on the emergent properties of self-\\nreflection and planning from large proprietary models, and\\nwe use Reflexion (Shinn et al., 2023) as the baseline (use\\nGPT-4o as the base model).\\n5.2. Evaluation Results\\nIn this section, we compare the performance of our QLASS\\nwith all the baselines on WebShop, SciWorld, and ALF-\\nWorld. We evaluate all algorithms using one-shot evaluation.\\nThe decoding temperatures are set to 0.7 for QLASS and\\nBest-of-N and 0 for other baselines.\\nOverall Baseline Comparison. Results are summarized\\nin Table 2. From Table 2, we can observe that QLASS\\nconsistently achieves the highest scores among all the open-\\nsourced baselines, including both training-based methods\\nand inference-based methods. QLASS also demonstrates\\ncomparable performance with the best proprietary base-\\nlines. Specifically, GPT-4 is the state-of-the-art model, but\\nQLASS still outperforms it on all three benchmarks by\\n17.9% on average, especially on SciWorld and ALFWorld.\\nAlso, QLASS outperforms ETO and PPO consistently by\\nover 5% on average, which are two strong baselines based\\non multiple stages of training, including supervised fintun-\\ning on expert trajectories, training reward models and doing\\nDPO or PPO on the explored trajectories. We achieve better\\nperformance while avoiding the heavy cost (including the\\nhyperparameter tuning on DPO / PPO).\\nInference-time Search Efficiency. We compare QLASS\\nand Best-of-N under different search budgets and visualize\\nthe results in Figure 3. We find that increasing the number\\nof completion tokens will improve the performance of all\\ninference methods. We can observe that QLASS is con-\\nsistently better than Best-of-N under almost all the search\\nbudgets. Another notable observation is that compared with\\nBest-of-N (68.4) under 400K tokens, QLASS (70.3) with\\nonly about half of search budgets under 240k tokens, out-\\nperforms the highest score of Best-of-N (68.4). Also, as\\nthe completion tokens approach 360K, Best-of-N begins to\\nflatten, while the score of QLASS still gets improved by a\\nrelatively larger margin from 360K tokens to 400K tokens.\\nThis indicates that our approach is a more effective way to\\n6\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nTable 2. Performance of all the baselines on WebShop, SciWorld and ALFWorld. The table is divided into two sections: the first presents\\nthe results of closed-source agents and the second includes open-sourced agents. ♠indicates the baseline based on GPT-4o. In each\\ncolumn, the best result is bolded and the second-best result is underlined.1\\nMethod\\nWebShop\\nSciWorld\\nALFWorld\\nSeen\\nUnseen\\nSeen\\nUnseen\\nGPT-4\\n63.2\\n64.8\\n64.4\\n42.9\\n38.1\\nGPT-3.5-Turbo\\n62.4\\n16.5\\n13.0\\n7.9\\n10.5\\nReflexion (Shinn et al., 2023)♠\\n64.2\\n60.3\\n64.4\\n45.7\\n55.2\\nBase Agent (Llama-2-7B-Chat)\\n17.9\\n3.8\\n3.1\\n0.0\\n0.0\\nSFT\\n63.1\\n67.4\\n53.0\\n60.0\\n67.2\\nRFT (Yuan et al., 2023)\\n63.6\\n71.6\\n54.3\\n62.9\\n66.4\\nPPO (Schulman et al., 2017)\\n64.2\\n59.4\\n51.7\\n22.1\\n29.1\\nBest-of-N\\n67.9\\n70.2\\n57.6\\n62.1\\n69.4\\nETO (Song et al., 2024)\\n67.4\\n73.8\\n65.0\\n68.6\\n72.4\\nQLASS\\n70.3\\n75.3\\n66.4\\n77.9\\n82.8\\n150\\n200\\n250\\n300\\n350\\n400\\nCompletion Tokens\\n1e3\\n66\\n68\\n70\\n72\\nWebShop Score\\n66.4\\n67.9\\n67.7\\n68.4\\n65.1\\n70.3\\n70.9\\n72.6\\nBest-of-N\\nQLASS\\nFigure 3. QLASS and Best-of-N under different search budgets.\\nThe x-axis represents the number of tokens consumed by the tra-\\njectories generated during inference averaged on all the tasks in\\neach test set.\\nscale up the compute for inference-time self-improvement.\\nSelf-training Performance. Since process reward model-\\ning is an important module in our framework, we ablate on\\nhow different choices of process reward can affect the perfor-\\nmance. We mainly experiment with three approaches of con-\\nstructing process rewards for each intermediate nodes on the\\nexploration trees: Q-value (ours) is to estimate Q-value\\nfor each state-action pair (i.e. each tree node except for root\\nnode) using Equation 7; Avg reward (Wang et al., 2023)\\ncomputes the averaged the final rewards; Reward (Yuan\\net al., 2024) directly treats the final outcome reward and\\nbackpropagates it as the process reward for each intermedi-\\nate step. In addition to the self-improvement at inference\\ntime, we also evaluate the effectiveness of QLASS for se-\\nlecting high-quality data for self-training. We train the base\\nagent on the SFT dataset in addition to the Q-guided gen-\\nerated data. Results are visualized in Figure 4. We observe\\nthat QLASS achieves the highest among all the self-training\\nbaselines, compared with RFT which leverages oracle out-\\n1Part of the results results are adopted from (Song et al., 2024)\\nand (Zhou et al., 2024).\\nSFT\\nRFT\\nAvg Reward Reward\\nQ-value\\n60\\n62\\n64\\n66\\n68\\nWebShop Score\\n63.1\\n63.6\\n65.4\\n64.7\\n66.4\\nFigure 4. Self-training baselines. The three methods marked with\\ndiagonal stripes leverage different process reward modeling based\\non the same exploration trees constructed in Stage 2 to guide self-\\ntraining data generation.\\ncome rewards to filter high-quality trajectories and baselines\\nguided by other process reward models such as Reward\\nand Avg Reward.\\nAblation on Process Reward Modeling. We train three dif-\\nferent process reward models guiding trajectory generation\\nfor self-training. Self-training results are in Figure 4. From\\nFigure 4, we can observe that Q-value utilized by our\\nQLASS yields the best performance, while the one using\\nAvg reward is slightly better than the one directly using\\nReward, indicating the effectiveness of using Q-value\\nto model process reward.\\n5.3. Fewer Annotations\\nIn many real-world applications, collecting large amounts\\nof expert-annotated data is both time-consuming and costly.\\nTo evaluate the effectiveness of our approach under such\\nconstraints, we designed this setup with fewer annotations\\nto test how quickly the agents adapt to new environments in\\nthis section. We extract 1000 trajectories as a subset from\\nthe original 1938 trajectories. Under this setup, all baselines\\n7\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nAction1: go to countertop 3 (q=0.25)\\nObservation: sees lettuce 1.\\nAction2: take lettuce 1 from countertop 3 (q=0.41)\\nObservation: picks up lettuce 1.\\nAction3: go to fridge 1 (q=0.65)\\nObservation: fridge 1 is closed.\\nAction4: open fridge 1 (q=0.71)\\nObservation: fridge is now open.\\nAction5: cool lettuce 1 with fridge 1 (q=0.89)\\nObservation: “You cool the lettuce 1 using the fridge 1.”\\nAction6: go to countertop 1 (q=0.95)\\nObservation: sees a butterknife 1.\\nAction7: put lettuce 1 on countertop 1\\nObservation: “You put the lettuce 1 on countertop 1.”\\nTask Completed Successfully.\\nGoal: put a cool lettuce on a countertop.\\nAction1: go to countertop 3\\nObservation: sees lettuce 1, among other items.\\nAction2: take lettuce 1 from countertop 3\\nObservation: picks up lettuce 1.\\nAction3: go to fridge 1\\nObservation: fridge 1 is closed.\\nAction4: open fridge 1\\nObservation: fridge is now open.\\nAction5: close fridge 1 \\nObservation: you closed fridge 1.\\nAction6: open fridge 1\\nObservation: fridge is now open.\\nAction7: close fridge 1 \\nObservation: you closed fridge 1.\\nRepeatedly until hit max of turns\\nAction5 \\nclose fridge 1 \\n(q=0.10)\\ncool lettuce 1 with \\nfridge 1 (q=0.89)\\nSFT\\nQLASS\\nFigure 5. One example on the ALFWorld, the right is QLASS and the left is the SFT baseline.\\nTable 3. Average reward comparison on WebShop with 1000 anno-\\ntated trajectories for behavior cloning. The best result is bolded,\\nand the second-best result is underlined.\\nMethod\\nWebShop\\nWebShop-1000\\nSFT\\n63.1\\n21.7\\nETO\\n67.4\\n66.7\\nBest-of-N\\n67.9\\n47.1\\nQLASS\\n70.3\\n67.3\\nTable 4. The performance on a different base LLM on SciWorld.\\nBase LLM\\nMethod\\nSciWorld\\nSeen\\nUnseen\\nLlama-2-13B\\nSFT\\n68.1\\n57.6\\nETO\\n71.4\\n68.6\\nQLASS\\n72.7\\n69.3\\ncan only conduct behavior cloning with access to the SFT\\ndataset of 1K trajectories. After that, baselines like RFT,\\nETO and QLASS which involve generation can explore\\non 1938 tasks. The performance comparison is listed in\\nTable 3. We can observe that QLASS outperforms other\\nmethods trained on both the full WebShop training set and\\nWebShop-1000 subset. This highlights the robustness of\\nour method, especially its potential in scenarios with scarce\\nexpert data. While other methods like RFT and SFT show a\\nsignificant drop in performance, QLASS remains effective,\\ndemonstrating the advantage of Q-guided generation for\\ndata selection even in annotation-limited environments.\\n5.4. Case Study\\nWe pick out an example from ALFWorld in Figure 5 to\\nshowcase the difference between baselines and our models.\\nThe SFT agent correctly picks up the lettuce, cools it using\\nthe fridge, and places it on the countertop in the beginning.\\nHowever, it continues performing redundant actions after-\\nward, such as repeatedly opening and closing the fridge.\\nThe environment responds with ”Nothing happened“ until\\nthe agent exhausts its step limit, failing to recognize the\\ntask is already complete. By contrast, the QLASS uses a\\nstepwise reward mechanism. Once it has cooled the lettuce\\nand placed it on a countertop, it gains no further reward\\nfrom reopening the fridge or replacing the lettuce. Conse-\\nquently, the QLASS avoids futile actions and terminates as\\nsoon as the goal is satisfied, successfully completing the\\ntask in fewer steps. We can observe that Q-value gradually\\ngrows with the number of steps, but suddenly converges to\\nan extremely high or low value at Action 5, indicating it\\nis a key step that differentiates success and failure, where\\nQLASS assigns ”cool lettuce with fridge 1“ with very high\\nQ-value, only gives 0.10 to ”close fridge 1“ that leads to\\nnonsense behavior.\\n5.5. Ablations Across Different Base Policy Models\\nTo validate the robustness of our method across different\\nmodel architectures, we also conduct experiments on a large\\nbase model: Llama-2-13B. As shown in Table 4, QLASS\\nstill outperforms the baseline reported in Song et al. (2024)\\non the SciWorld benchmark.\\n6. Conclusion\\nIn this paper, we introduce QLASS, a novel approach that\\nenhances open-source language agents at inference time\\nby integrating Q-value-based process guidance. By model-\\ning the Q-value at each intermediate step during planning,\\nour method offers step-wise feedback that surpasses the\\nlimitations of outcome-based reward models, particularly\\nin complex, long-horizon tasks. Through extensive exper-\\niments, we have demonstrated that QLASS significantly\\n8\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nimproves the language agent to search more intelligently.\\nMoreover, our method demonstrates strong performance\\neven in scenarios with limited annotated data used for be-\\nhavior cloning. This work paves the way for more efficient\\nand scalable self-improvement techniques in language mod-\\nels, enabling them to tackle complex tasks with reduced\\nreliance on human annotations.\\nReferences\\nBansal, H., Lin, Z., Xie, T., Zong, Z., Yarom, M., Bitton,\\nY., Jiang, C., Sun, Y., Chang, K.-W., and Grover, A.\\nVideophy: Evaluating physical commonsense for video\\ngeneration. arXiv preprint arXiv:2406.03520, 2024.\\nBellman, R. E. and Dreyfus, S. E.\\nApplied dynamic\\nprogramming, volume 2050. Princeton university press,\\n2015.\\nChen, B., Shu, C., Shareghi, E., Collier, N., Narasimhan, K.,\\nand Yao, S. Fireact: Toward language agent fine-tuning.\\narXiv preprint arXiv:2310.05915, 2023.\\nChen, Z., Zhao, Z., Zhu, Z., Zhang, R., Li, X., Raj, B.,\\nand Yao, H. AutoPRM: Automating procedural super-\\nvision for multi-step reasoning via controllable question\\ndecomposition. In Duh, K., Gomez, H., and Bethard, S.\\n(eds.), Proceedings of the 2024 Conference of the North\\nAmerican Chapter of the Association for Computational\\nLinguistics: Human Language Technologies (Volume 1:\\nLong Papers), pp. 1346–1362, Mexico City, Mexico,\\nJune 2024. Association for Computational Linguistics.\\ndoi: 10.18653/v1/2024.naacl-long.73.\\nURL https:\\n//aclanthology.org/2024.naacl-long.73.\\nDou, Z.-Y., Yang, C.-F., Wu, X., Chang, K.-W., and Peng, N.\\nReflection-reinforced self-training for language agents.\\narXiv preprint arXiv:2406.01495, 2024.\\nFeng, X., Wan, Z., Wen, M., Wen, Y., Zhang, W., and\\nWang, J.\\nAlphazero-like tree-search can guide large\\nlanguage model decoding and training. arXiv preprint\\narXiv:2309.17179, 2023.\\nGulcehre, C., Paine, T. L., Srinivasan, S., Konyushkova, K.,\\nWeerts, L., Sharma, A., Siddhant, A., Ahern, A., Wang,\\nM., Gu, C., Macherey, W., Doucet, A., Firat, O., and\\nde Freitas, N. Reinforced self-training (rest) for language\\nmodeling, 2023. URL https://arxiv.org/abs/\\n2308.08998.\\nGuo, D., Yang, D., Zhang, H., Song, J., Zhang, R., Xu, R.,\\nZhu, Q., Ma, S., Wang, P., Bi, X., et al. Deepseek-r1: In-\\ncentivizing reasoning capability in llms via reinforcement\\nlearning. arXiv preprint arXiv:2501.12948, 2025.\\nHosseini, A., Yuan, X., Malkin, N., Courville, A., Sor-\\ndoni, A., and Agarwal, R. V-star: Training verifiers for\\nself-taught reasoners. arXiv preprint arXiv:2402.06457,\\n2024.\\nJin, C., Allen-Zhu, Z., Bubeck, S., and Jordan, M. I. Is\\nq-learning provably efficient? In Bengio, S., Wallach,\\nH., Larochelle, H., Grauman, K., Cesa-Bianchi, N.,\\nand Garnett, R. (eds.), Advances in Neural Information\\nProcessing Systems, volume 31. Curran Associates, Inc.,\\n2018.\\nURL https://proceedings.neurips.\\ncc/paper_files/paper/2018/file/\\nd3b1fb02964aa64e257f9f26a31f72cf-Paper.\\npdf.\\nLightman, H., Kosaraju, V., Burda, Y., Edwards, H., Baker,\\nB., Lee, T., Leike, J., Schulman, J., Sutskever, I., and\\nCobbe, K.\\nLet’s verify step by step.\\narXiv preprint\\narXiv:2305.20050, 2023.\\nPutta, P., Mills, E., Garg, N., Motwani, S., Finn, C., Garg,\\nD., and Rafailov, R.\\nAgent q: Advanced reasoning\\nand learning for autonomous ai agents. arXiv preprint\\narXiv:2408.07199, 2024.\\nRafailov, R., Sharma, A., Mitchell, E., Manning, C. D.,\\nErmon, S., and Finn, C. Direct preference optimiza-\\ntion: Your language model is secretly a reward model.\\nAdvances in Neural Information Processing Systems, 36,\\n2024.\\nSchulman, J., Wolski, F., Dhariwal, P., Radford, A., and\\nKlimov, O. Proximal policy optimization algorithms.\\narXiv preprint arXiv:1707.06347, 2017.\\nSetlur, A., Garg, S., Geng, X., Garg, N., Smith, V., and\\nKumar, A.\\nRl on incorrect synthetic data scales the\\nefficiency of llm math reasoning by eight-fold. arXiv\\npreprint arXiv:2406.14532, 2024.\\nShen, Y., Song, K., Tan, X., Li, D., Lu, W., and Zhuang,\\nY. Hugginggpt: Solving ai tasks with chatgpt and its\\nfriends in hugging face. Advances in Neural Information\\nProcessing Systems, 36, 2024.\\nShinn, N., Cassano, F., Gopinath, A., Narasimhan,\\nK. R., and Yao, S.\\nReflexion:\\nlanguage agents\\nwith verbal reinforcement learning. In Thirty-seventh\\nConference on Neural Information Processing Systems,\\n2023. URL https://openreview.net/forum?\\nid=vAElhFcKW6.\\nShridhar, M., Yuan, X., Cote, M.-A., Bisk, Y., Trischler,\\nA., and Hausknecht, M. {ALFW}orld: Aligning text\\nand embodied environments for interactive learning. In\\nInternational Conference on Learning Representations,\\n2021.\\n9\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nSingh, A., Co-Reyes, J. D., Agarwal, R., Anand, A., Patil,\\nP., Liu, P. J., Harrison, J., Lee, J., Xu, K., Parisi, A.,\\net al.\\nBeyond human data: Scaling self-training for\\nproblem-solving with language models. arXiv preprint\\narXiv:2312.06585, 2023.\\nSnell, C., Lee, J., Xu, K., and Kumar, A. Scaling llm test-\\ntime compute optimally can be more effective than scal-\\ning model parameters. arXiv preprint arXiv:2408.03314,\\n2024.\\nSong, Y., Xiong, W., Zhu, D., Wu, W., Qian, H., Song, M.,\\nHuang, H., Li, C., Wang, K., Yao, R., et al. Restgpt:\\nConnecting large language models with real-world restful\\napis. arXiv preprint arXiv:2306.06624, 2023.\\nSong, Y., Yin, D., Yue, X., Huang, J., Li, S., and Lin, B. Y.\\nTrial and error: Exploration-based trajectory optimization\\nof LLM agents. In Ku, L.-W., Martins, A., and Srikumar,\\nV. (eds.), Proceedings of the 62nd Annual Meeting of\\nthe Association for Computational Linguistics (Volume\\n1: Long Papers), pp. 7584–7600, Bangkok, Thailand,\\nAugust 2024. Association for Computational Linguis-\\ntics. URL https://aclanthology.org/2024.\\nacl-long.409.\\nSutton, R. S. Learning to predict by the methods of temporal\\ndifferences. Machine learning, 3:9–44, 1988.\\nTeam, K., Du, A., Gao, B., Xing, B., Jiang, C., Chen, C.,\\nLi, C., Xiao, C., Du, C., Liao, C., et al. Kimi k1. 5:\\nScaling reinforcement learning with llms. arXiv preprint\\narXiv:2501.12599, 2025.\\nUesato, J., Kushman, N., Kumar, R., Song, F., Siegel, N.,\\nWang, L., Creswell, A., Irving, G., and Higgins, I. Solv-\\ning math word problems with process- and outcome-\\nbased feedback, 2022.\\nWang, C., Deng, Y., Lv, Z., Yan, S., and Bo, A. Q*: Im-\\nproving multi-step reasoning for llms with deliberative\\nplanning. arXiv preprint arXiv:2406.14283, 2024.\\nWang, P., Li, L., Shao, Z., Xu, R., Dai, D., Li, Y., Chen,\\nD., Wu, Y., and Sui, Z. Math-shepherd: A label-free\\nstep-by-step verifier for llms in mathematical reasoning.\\narXiv preprint arXiv:2312.08935, 2023.\\nWang, R., Jansen, P., Cˆot´e, M.-A., and Ammanabrolu,\\nP.\\nScienceWorld: Is your agent smarter than a 5th\\ngrader? In Goldberg, Y., Kozareva, Z., and Zhang, Y.\\n(eds.), Proceedings of the 2022 Conference on Empirical\\nMethods in Natural Language Processing, pp. 11279–\\n11298, Abu Dhabi, United Arab Emirates, December\\n2022a. Association for Computational Linguistics. doi:\\n10.18653/v1/2022.emnlp-main.775. URL https://\\naclanthology.org/2022.emnlp-main.775.\\nWang, Z., Lin, Z., Liu, P., ZHeng, G., Wen, J., Chen,\\nX., Chen, Y., and Yang, Z. Learning to detect noisy\\nlabels using model-based features.\\narXiv preprint\\narXiv:2212.13767, 2022b.\\nWatkins, C. J. and Dayan, P. Q-learning. Machine learning,\\n8:279–292, 1992.\\nWei, J., Wang, X., Schuurmans, D., Bosma, M., Xia, F., Chi,\\nE., Le, Q. V., Zhou, D., et al. Chain-of-thought prompting\\nelicits reasoning in large language models. Advances in\\nneural information processing systems, 35:24824–24837,\\n2022.\\nWu, X., Lin, Z., Zhao, S., Wu, T.-L., Lu, P., Peng, N.,\\nand Chang, K.-W. Vdebugger: Harnessing execution\\nfeedback for debugging visual programs. arXiv preprint\\narXiv:2406.13444, 2024.\\nXu, H., Lin, Z., Zhou, J., Zheng, Y., and Yang, Z. A uni-\\nversal discriminator for zero-shot generalization. arXiv\\npreprint arXiv:2211.08099, 2022.\\nYao, S., Chen, H., Yang, J., and Narasimhan, K.\\nWebshop:\\nTowards scalable real-world web inter-\\naction with grounded language agents.\\nAdvances\\nin Neural Information Processing Systems, 35:20744–\\n20757, 2022.\\nYao, S., Zhao, J., Yu, D., Du, N., Shafran, I., Narasimhan,\\nK. R., and Cao, Y. React: Synergizing reasoning and\\nacting in language models. In The Eleventh International\\nConference on Learning Representations, 2023.\\nYin, D., Brahman, F., Ravichander, A., Chandu, K., Chang,\\nK.-W., Choi, Y., and Lin, B. Y. Agent lumos: Unified and\\nmodular training for open-source language agents. In Ku,\\nL.-W., Martins, A., and Srikumar, V. (eds.), Proceedings\\nof the 62nd Annual Meeting of the Association for\\nComputational Linguistics (Volume 1: Long Papers), pp.\\n12380–12403, Bangkok, Thailand, August 2024. Asso-\\nciation for Computational Linguistics. URL https:\\n//aclanthology.org/2024.acl-long.670.\\nYuan, L., Li, W., Chen, H., Cui, G., Ding, N., Zhang, K.,\\nZhou, B., Liu, Z., and Peng, H. Free process rewards\\nwithout process labels. arXiv preprint arXiv:2412.01981,\\n2024.\\nYuan, Z., Yuan, H., Li, C., Dong, G., Lu, K., Tan, C., Zhou,\\nC., and Zhou, J. Scaling relationship on learning math-\\nematical reasoning with large language models. arXiv\\npreprint arXiv:2308.01825, 2023.\\nZhai, Y., Yang, T., Xu, K., Dawei, F., Yang, C., Ding,\\nB., and Wang, H. Enhancing decision-making for llm\\nagents via step-level q-value models.\\narXiv preprint\\narXiv:2409.09345, 2024.\\n10\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nZhang, D., Zhoubian, S., Hu, Z., Yue, Y., Dong, Y., and\\nTang, J. ReST-MCTS*: LLM self-training via process\\nreward guided tree search. In The Thirty-eighth Annual\\nConference on Neural Information Processing Systems,\\n2024. URL https://openreview.net/forum?\\nid=8rcFOqEud5.\\nZhou, A., Yan, K., Shlapentokh-Rothman, M., Wang,\\nH., and Wang, Y.-X. Language agent tree search uni-\\nfies reasoning, acting, and planning in language mod-\\nels. In Forty-first International Conference on Machine\\nLearning, 2024. URL https://openreview.net/\\nforum?id=njwv9BsGHF.\\n11\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nA. Appendix\\nA.1. Discussion\\nWhy supervised train the offline QNet using LLM as the backbone instead of directly using deep Q-learning? Directly\\napplying deep Q-learning (Watkins & Dayan, 1992) to language agents face critical challenges. First, the action space (all\\npossible text outputs) is unbounded and orders of magnitude larger than typical RL environments (e.g., Atari’s 18 discrete\\nactions). Standard exploration strategies like ϵ-greedy fail because random text sampling rarely yields meaningful rewards\\nor trajectories. Second, language tasks often involve sparse rewards, destabilizing Q-learning’s reliance on frequent reward\\nsignals. Pure online Q-learning would suffer from high gradient variance and require infeasible exploration budgets.\\nInitializing the value function model from a well-pretrained large foundation model can encode rich linguistic and reasoning\\npriors, as well as world commonsense knowledge (Bansal et al., 2024; Song et al., 2024; Xu et al., 2022; Wang et al., 2023).\\nSo we initialize our QNet with the LLM trained in the agent environment to embrace both knowledge during pretraining and\\nagent specific capabilities, thus boosting the adaption to the long-term value modeling.\\nA.2. Experimental details\\nA.2.1. DATASETS\\nWe follow the setup of ETO (Song et al., 2024) to use the three agent tasks for our experiments.\\n(a) WebShop is an online shopping environment. The available action types for agents include search[keywords] and\\nclick[value]. The agent is instructed to complete the task with ReAct(Yao et al., 2023)-style response. The instruction is\\nspecified in Figure 6.\\n(b) ALFWorld (Shridhar et al., 2021) consists of interactive TextWorld environments paralleling the embodied worlds. In\\nthis setup, agents must explore and complete complex household tasks. The ALFWorld dataset includes both seen and\\nunseen evaluation sets. The seen set tests in-distribution generalization, while the unseen set evaluates out-of-distribution\\ngeneralization, featuring entirely new task instances for the agents to solve.\\n(c) SciWorld (Wang et al., 2022a) is a text-based virtual platform designed around conducting basic scientific experiments\\nacross ten task categories, such as thermodynamics and electrical circuits. Agents engage in embodied, interactive\\nenvironments to grasp scientific concepts through practical tasks. Each task in ScienceWorld includes optional subgoals,\\nwith the final reward calculated based on the achievement of these subgoals.\\nWe have summarize the statistics of SFT datasets for behavior cloning on all the environments in the main body. Note\\nthat the default reward from the environment is zero for the intermediate step before terminal. For self-generation and tree\\nconstruction, we set the maximum step as 5 in WebShop and 18 in ALFWorld and SciWorld. For inference, we set the\\nmaximum step as 5 in WebShop and 40 in ALFWorld and SciWorld. The instruction templates are displayed in Figure 6, 7\\nand 8.\\nA.2.2. QNET\\nModel Architecture. Our QNet is designed by sharing the backbone of the Large Language Model (LLM) and appending\\na value head to predict Q-values. Specifically, we utilize a pre-trained LLM, denoted as LLMθ, which serves as the\\nfoundational model for encoding input sequences. The value head is a Multi-Layer Perceptron (MLP) that takes the hidden\\nstates from the LLM and outputs scalar Q-value predictions.\\nFormally, given an input sequence of tokens x = (x1, x2, . . . , xn), the LLM produces hidden states h = (h1, h2, . . . , hn):\\nh = LLMθ(x),\\n(9)\\nwhere ht ∈Rd represents the hidden state at time step t, and d is the hidden size of the LLM.\\nThe value head MLPϕ processes each hidden state ht to predict the corresponding Q-value ˆqt:\\nˆqt = MLPϕ(ht),\\n(10)\\n12\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nAlgorithm 2 Constructing a Reasoning Tree\\nInput: A LLM agent πθ, a given task description u, a trajectory τ0 from the training set Dexpert on task u, max exploration\\ndepth D, max exploration width W\\nInitialize a root node U with state s ←u, depth t ←0, reward r ←0, action ←null, children set C ←{}\\nInitialize the reasoning tree T with U\\nThe expansion node queue E ←[u]\\nwhile E is not empty do\\nGet a node N ←E.pop with state N.s, action N.a, reward N.r, children set C at step N.t\\nif the number of children in N.C < W and N.t ≤D then\\nSample a new trajectory τ based on state N.s\\nGet a new branch b constructed on τ and merge b in node N.C\\nif τ achieves a non-zero final reward then\\nPush all the nodes on b with N.t ≤D into E\\nend if\\nend if\\nend while\\nConstruct a branch b with τ0 and merge in U.C\\nPush all the nodes on b with depth t and t ≤D into E\\nrepeat Function in Line 5-12\\nreturn the reasoning tree T\\nwhere ˆqt ∈R is the predicted Q-value at time step t, and ϕ denotes the parameters of the MLP.\\nThe MLP consists of multiple layers with ReLU activations, culminating in a linear layer that outputs a scalar Q-value.\\nThis design allows the model to capture complex patterns in the hidden representations and map them to accurate Q-value\\nestimates.\\nTraining Objective. Given an explored trajectory x = (x1, x2, . . . , xn) with an associated target Q-value q, we train the\\nQNet by minimizing the Mean Squared Error (MSE) loss between the predicted Q-values ˆqt and the target Q-value q at each\\ntime step:\\nL(θ, ϕ) = 1\\nn\\nn\\n∑\\nt=1\\n(ˆqt −q)2 .\\n(11)\\nBy minimizing this loss, we encourage the QNet to produce consistent Q-value estimations across the sequence that align\\nwith the target Q-value q. This training objective emphasizes accurate Q-value predictions at each token, reinforcing the\\nmodel’s ability to assess the long-term value of actions throughout the trajectory.\\nImplementation Details. In practice, we implement the value head as an MLP with two hidden layers of size 1024 and\\nReLU activation functions.\\nThe entire model, including the LLM and the value head, operates in bfloat16 precision to optimize memory usage without\\nsacrificing performance. The LLM backbone remains frozen or fine-tuned depending on the specific experimental setup,\\nallowing us to leverage pre-trained language representations while focusing on learning accurate Q-value predictions through\\nthe value head. By integrating the value head with the LLM, our QNet effectively combines language understanding with\\nreinforcement learning principles, enabling the agent to make informed decisions based on both linguistic context and\\nestimated future rewards.\\nA.3. Algorithms\\nA.3.1. PSEUDOCODE OF EXPLORATION TREE CONSTRUCTION AND Q-VALUE DISTILLATION\\nIn this section, we provide the pseudocode of constructing an exploration tree in stage 2 in Algorithm 2 and and how we\\ndistill the Q-value from an exploration tree in Algorithm 3.\\n13\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nAlgorithm 3 Q-value Estimation\\nInput: A reasoning tree T with a root node U, discount factor γ\\nProcedure Update Q Values(N)\\nif N.C = ∅then\\nreturn\\n⊳Leaf nodes do not update\\nend if\\nfor node Nchild in N.C do\\nUpdate Q Values(Nchild)\\n⊳Recursively update child nodes first\\nend for\\nN.q = N.r + γ maxNchild∈N.C(Nchild.q)\\n⊳Update Q-value after all children are updated\\nEnd Procedure\\nUpdate Q Values(U)\\n⊳Start the update process from the root\\nQmin = minN∈T (N.q)\\nQmax = maxN∈T (N.q)\\nfor node N in T do\\nN.q = N.q−Qmin\\nQmax−Qmin\\n⊳Apply min-max normalization\\nend for\\nreturn the reasoning tree T with estimated Q-value of each node\\nA.3.2. Q-GUIDED GENERATION\\nIn this section, we present the pseudocode of Q-guided generation in Algorithm 4, which is a critical component of our\\nframework.\\nPerturbation augmented generation. In WebShop, due to the limited diversity of sampled actions, we introduce augmenting\\naction diversity with perturbation during this stage, which is realized by prompting GPT-3.5-Turbo to paraphrase the task\\ndescription. This utilization of perturbation enables us to inject more variability into the prompts that guide action selection,\\nsubstantially enriching the range and relevance of possible actions. Such enhanced prompts help prepare the model to handle\\nmore diverse and unforeseen situations effectively. We augmented the action diversity in all inference-based algorithms\\nwhen evaluating in WebShop for fair comparison. Noted that it costs too much on ALFWorld and SciWorld, so we only\\nconduct perturbation on the WebShop.\\nWe introduce our implementation details and examples as follows. We use GPT-3.5-Turbo to perturb the task descriptions\\nusing the prompt “Paraphrase the text: {task description}”. We show an illustrative example on a WebShop task in Figure 9.\\nA.4. Hyper-parameters\\nWe summarize the hyper-parameters used across both all stages of QLASS in this section. The hyper-parameters leveraged\\nin behavior cloning and self-training is in Table 5. Training QNet shares all the same hyperparameters, except that the\\nnumber of training epochs is set to 2.\\n14\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nAlgorithm 4 Q-guided Generation\\nInput: A LLM agent πθ, a given task description u, an action set At containing M candidates at step t, a trained QNet\\nQϕ, sampled trajectory number N, max trajectory length L\\ntraj candidates = [ ]\\nfor i = 1 to N do\\nInitialize state si ←[u]\\nfor t = 1 to L do\\nCollect a set of action candidates At ←Sample a ∼πθ(a ∣si) for M times\\nat ←argmaxa∼AtQϕ(si, a)\\n⊳Select the best action with max Q-value\\nTake action at, and receive new observation ot from environment\\nsi ←si + [at, ot]\\n⊳Update state with executed action and new observation\\nif si is the final state then\\nbreak\\n⊳Exit loop if stop condition is met\\nend if\\nend for\\ntraj candidates.append(si)\\nend for\\nSelect the best trajectory s with best final reward s.reward from traj candidates\\nTable 5. Hyperparameters used in QLASS.\\nHyperparameter\\nValue\\nBatch size\\n64\\nNumber of training epochs\\n3\\nWeight decay\\n0.0\\nWarmup ratio\\n0.03\\nLearning rate\\n1e-5\\nLR scheduler type\\nCosine\\nLogging steps\\n5\\nModel max length\\n4096\\nDiscount factor γ\\n0.9\\nMaximum expansion depth D on WebShop\\n3\\nMaximum expansion depth D on SciWorld\\n6\\nMaximum expansion depth D on ALFWorld\\n8\\nAction candidate set size M for inference\\n2\\nSampled trajectory number N for self-training\\n1\\nExploration temperature\\n0.7\\n15\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nYou are web shopping.\\nI will give you instructions about what to do.\\nYou have to follow the instructions.\\nEvery round I will give you an observation and a \\nlist of available actions, you have to respond an \\naction based on the state and instruction.\\nYou can use search action if search is available.\\nYou can click one of the buttons in clickables.\\nAn action should be of the following structure:\\nsearch[keywords]\\nclick[value]\\nIf the action is not valid, perform nothing.\\nKeywords in search are up to you, but the value \\nin click must be a value in the list of available \\nactions.\\nRemember that your keywords in search should \\nbe carefully designed.\\nYour response should use the following format:\\nThought: I think ...\\nAction: click[something]\\nWebShop Instruction\\nFigure 6. The instruction prompt provided to language agent on WebShop.\\nYou are a helpful assistant to do some scientific experiment in an environment.\\nIn the environment, there are several rooms: kitchen, foundry, workshop, bathroom, \\noutside, living room, bedroom, greenhouse, art studio, hallway\\nYou should explore the environment and find the items you need to complete the \\nexperiment.\\nYou can teleport to any room in one step.\\nAll containers in the environment have already been opened, you can directly get items \\nfrom the containers.\\nThe available actions are:\\nopen OBJ: open a container\\nclose OBJ: close a container\\nactivate OBJ: activate a device\\ndeactivate OBJ: deactivate a device\\nconnect OBJ to OBJ: connect electrical components\\ndisconnect OBJ: disconnect electrical components\\nuse OBJ [on OBJ]: use a device/item\\nlook around: describe the current room\\nexamine OBJ: describe an object in detail\\nlook at OBJ: describe a container\\'s contents\\nread OBJ: read a note or book\\nmove OBJ to OBJ: move an object to a container\\npick up OBJ: move an object to the inventory\\npour OBJ into OBJ: pour a liquid into a container\\nmix OBJ: chemically mix a container\\nteleport to LOC: teleport to a specific room\\nfocus on OBJ: signal intent on a task object\\nwait: task no action for 10 steps\\nwait1: task no action for a step\\n---\\nNow, it\\'s your turn and here is the task.\\nTask Description:\\nYour task is to boil lead. For compounds without a boiling point, combusting the substance \\nis also acceptable. First, focus on the substance. Then, take actions that will cause it to \\nchange its state of matter.\\nSciWorld Instruction\\nFigure 7. The instruction prompt provided to language agent on SciWorld.\\n16\\nQLASS: Boosting Language Agent Inference via Q-Guided Stepwise Search\\nInteract with a household to solve a task. Imagine you are an intelligent agent in a \\nhousehold environment and your target is to perform actions to complete the task goal. \\nAt the beginning of your interactions, you will be given the detailed description of the \\ncurrent environment and your goal to accomplish. \\nFor each of your turn, you will be given the observation of the last turn. You should first \\nthink about the current condition and plan for your future actions, And then output your \\naction in this turn. Your output must strictly follow this format:\"Thought: your \\nthoughts.\\\\nAction: your next action\".\\nThe available actions are:\\n1. go to {recep}\\n2. task {obj} from {recep}\\n3. put {obj} in/on {recep}\\n4. open {recep}\\n5. close {recep}\\n6. toggle {obj} {recep}\\n7. clean {obj} with {recep}\\n8. heat {obj} with {recep}\\n9. cool {obj} with {recep}\\nwhere {obj} and {recep} correspond to objects and receptacles.\\nAfter your each turn, the environment will give you immediate feedback based on which \\nyou plan your next few steps. if the envrionment output \"Nothing happened\", that means \\nthe previous action is invalid and you should try more options.\\nYour response should use the following format:\\nThought: <your thoughts>\\nAction: <your next action>\\nNow, it\\'s your turn and here is the task.\\nYou are in the middle of a room. Looking quickly around you, you see a cabinet 6, a \\ncabinet 5, a cabinet 4, a cabinet 3, a cabinet 2, a cabinet 1, a coffeemachine 1, a \\ncountertop 3, a countertop 2, a countertop 1, a drawer 3, a drawer 2, a drawer 1, a fridge \\n1, a garbagecan 1, a microwave 1, a shelf 3, a shelf 2, a shelf 1, a sinkbasin 1, a \\nstoveburner 4, a stoveburner 3, a stoveburner 2, a stoveburner 1, and a toaster 1.\\nYour task is to: put a clean knife in countertop.\\nALFWorld Instruction\\nFigure 8. The instruction prompt provided to language agent on ALFWorld.\\nParaphrase the task: I need a long lasting 6.76 fl\\noz bottle of l\\'eau d\\'issey, and price lower than \\n100.00 dollars\\nI\\'m looking for a 6.76 fl oz bottle of L\\'eau D\\'Issey\\nthat lasts a long time and costs less than $100.\\nI need a durable 6.76 ounce bottle of L\\'eau\\nD\\'Issey, with a price under $100.\\nI am searching for a long-lasting 6.76 fluid ounce \\nL\\'eau D\\'Issey perfume for less than 100 dollars.\\nPerturbation\\nFigure 9. An illustrative example on task perturbation.\\n17\\n'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["text_all"]}], "metadata": {"kernelspec": {"display_name": "Paper-Insight", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}