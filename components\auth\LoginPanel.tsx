'use client';

import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, message, Space } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useUserContext } from '@/context/UserContext';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginPanelProps {
  onSwitchToRegister: () => void;
}

export default function LoginPanel({ onSwitchToRegister }: LoginPanelProps) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { login, setIsLoading } = useUserContext();

  const handleLogin = async (values: LoginFormData) => {
    setLoading(true);
    setIsLoading(true);
    
    try {
      // 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // 登录成功，更新用户上下文
        login(result.user);
        message.success('登录成功！');
      } else {
        message.error(result.error || '登录失败，请检查邮箱和密码');
      }
    } catch (error) {
      console.error('登录错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 h-full bg-white">
      <Card className="shadow-sm">
        <div className="text-center mb-6">
          <Title level={3}>用户登录</Title>
          <Text type="secondary">欢迎回来，请登录您的账户</Text>
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input 
              prefix={<MailOutlined />} 
              placeholder="请输入邮箱地址"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6位字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              size="large"
            />
          </Form.Item>

          <Form.Item>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              block
              size="large"
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div className="text-center">
          <Space direction="vertical">
            <Text type="secondary">
              还没有账户？
              <Button 
                type="link" 
                onClick={onSwitchToRegister}
                className="p-0 ml-1"
              >
                立即注册
              </Button>
            </Text>
          </Space>
        </div>
      </Card>
    </div>
  );
}
