import React, { useRef, useState, useEffect } from "react";
import { B<PERSON><PERSON>, Sender } from "@ant-design/x";
import type { BubbleProps } from '@ant-design/x';
/* eslint-disable react/no-danger */
import { CopyOutlined, SyncOutlined } from '@ant-design/icons';
import { Button, Space, message as antdMessage, theme, Select } from 'antd';
import { renderMarkdown } from '@/components/search/sub/Markdown';
import { models, DEFAULT_MODEL } from '@/lib/llm/models';

interface Message {
  role: "user" | "assistant";
  content: string;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const controllerRef = useRef<AbortController | null>(null);
  const { token } = theme.useToken();
  const [isStreaming, setIsStreaming] = useState(false);
  const [messageApi, contextHolder] = antdMessage.useMessage();
  const [selectedModel, setSelectedModel] = useState(DEFAULT_MODEL.id);

  // 初始欢迎消息
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([
        {
          role: 'assistant',
          content: `我是论文小助手🧑‍🎓，可以帮您：\n\n1. 📊 总结这些论文的共同点和差异\n2. 📈 分析某个具体研究方向的发展趋势\n3. 🔍 解释某个具体概念或方法\n4. ⚖️ 对比不同论文的方法和结果\n\n🎉：请您提问前先进行检索`
        }
      ]);
    }
  }, []);

  // 发送消息
  const sendMessage = async (msg?: string) => {
    let content = typeof msg === "string" ? msg : input;
    // 去除末尾空格和所有连续回车
    content = content.replace(/[\s\r\n]+$/g, '');
    console.log(content);
    if (!content.trim() && !isStreaming) return;

    // 如果正在流式响应，则中断
    if (isStreaming) {
      controllerRef.current?.abort();
      controllerRef.current = null;
      setIsStreaming(false);
      setLoading(false);
      return;
    }

    setLoading(true);
    setIsStreaming(true);

    const userMessage: Message = { role: "user", content };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");

    controllerRef.current?.abort();
    controllerRef.current = new AbortController();
    const { signal } = controllerRef.current;

    try {
      const response = await fetch("/api/stream-chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          prompt: content,
          history_messages: messages,
          model: selectedModel,
          system_prompt: '你是一个AI助手，请根据用户的问题给出回答，所有回答都要用markdown格式，公式用 $...$ 或者 $$...$$ 渲染，千万不要用 [] 包裹。',
          temperature: 0.8,
        }),
        signal,
      });
      if (!response.body) throw new Error("无响应体");
      const reader = response.body.getReader();
      let aiMsg = "";

      setMessages((prev) => [...prev, { role: "assistant", content: "" }]);
      while (true) {
        if (signal.aborted) break;
        const { done, value } = await reader.read();
        if (done) break;
        const text = new TextDecoder().decode(value);
        // 逐行处理 data: ... 格式
        const lines = text.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (!data || data === '[DONE]') continue;
            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                aiMsg += parsed.content;
                setMessages((prev) => {
                  const newMessages = [...prev];
                  const lastIndex = newMessages.length - 1;
                  if (lastIndex >= 0) {
                    newMessages[lastIndex] = {
                      ...newMessages[lastIndex],
                      content: aiMsg,
                    };
                  }
                  return newMessages;
                });
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setMessages((prev) => [
          ...prev,
          { role: "assistant", content: "抱歉，发生了错误，请稍后重试。" },
        ]);
      }
    } finally {
      setIsStreaming(false);
      setLoading(false);
      controllerRef.current = null;
    }
  };

  const onCopy = (messageContext: string) => {
    const textToCopy = messageContext;
    if (!textToCopy) return messageApi.success('内容为空');
    navigator.clipboard.writeText(textToCopy)
      .then(() => {
        messageApi.success('复制成功');
      })
      .catch(() => {
        messageApi.error('复制失败，请手动复制');
      });
  };

  return (
    <>
      {contextHolder}
      <div
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          borderRadius: 8,
          padding: 12
        }}
      >
        {/* 模型选择下拉框 */}
        <div className="mb-2 flex items-center gap-2">
          {/* <span style={{ fontWeight: 500 }}>选择模型：</span> */}
          <Select
            value={selectedModel}
            onChange={setSelectedModel}
            options={models.map(m => ({ label: m.name, value: m.id, description: m.description }))}
            popupMatchSelectWidth={false}
            className="rounded-2xl shadow-sm bg-white hover:bg-gray-100 focus:bg-gray-100 text-xs h-10"
            size="middle"
            disabled={loading || isStreaming}
            optionRender={option => (
              <div>
                <div style={{ fontWeight: 500, fontSize: 14 }}>{option.data.label}</div>
                <div style={{ fontSize: 12, color: '#888' }}>{option.data.description}</div>
              </div>
            )}
          />
        </div>
        {/* 对话历史 */}  
        {/* <div style={{ flex: 1, overflowY: "auto", padding: 16 }}> */}
          <Bubble.List
            autoScroll={true}
            items={messages.map((msg, idx) => ({
              key: idx.toString(),
              placement: msg.role === "user" ? "end" : "start",
              content: msg.content,
              variant: msg.role === "user" ? "filled" : "filled",
              footer: (messageContext: string) => (
                <Space size={token.paddingXXS}>
                  <Button color="default" variant="text" size="small" icon={<SyncOutlined />} />
                  <Button
                    color="default"
                    variant="text"
                    size="small"
                    onClick={() => onCopy(messageContext)}
                    icon={<CopyOutlined />}
                  />
                </Space>
              ),
              messageRender: renderMarkdown,
            }))}
          />
        {/* </div> */}
        {/* 输入框 */}
        <div
          style={{
            marginLeft: 6,
            marginRight: 6,
            marginBottom: 6,

            borderRadius: 12,
            alignItems: 'center',
            marginTop: 'auto'
          }}
        >
          <Sender
            value={input}
            onChange={setInput}
            onSubmit={sendMessage}
            disabled={loading}
            placeholder="Shift + Enter 换行，@选择论文，#选择Prompt"
            loading={loading}
          />
        </div>
      </div>
    </>
  );
};

export default Chat;

