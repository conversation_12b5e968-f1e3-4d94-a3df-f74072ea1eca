### Rules

---
description: 
globs: 
alwaysApply: true
---
# 1. 项目通用开发规范
## 目录结构
```
    axsight-front/
    ├── app/                    # Next.js 应用主目录
    │   ├── survey/           # 网站主界面，用于论文搜索和对话综述等功能
    │   ├── config/           # 应用配置
    │   ├── layout.tsx        # 全局布局组件
    │   ├── globals.css       # 全局样式文件
    │   └── api/               # API 路由接口
    │       ├── abstract-refinement-multi/  # 摘要精炼功能
    │       ├── pdf2md/                     # pdf 转 md
    │       ├── semantic-search/            # 检索核心函数
    │       ├── stream-chat/                # LLM 流式对话
    │       ├── translate/                  # 翻译
    │       └── databse/                    # 数据库相关增删查改操作
    │
    ├── components/            # 共享组件
    │   ├── ui/               # UI 基础组件
    │   ├── sidebar/          # 侧边栏相关组件
    │   ├── left_panel/       # 左栏页面（每个文件都是一个单独的页面处理）
    │   ├── main_panel/       # 主页面（每个文件都是一个单独的页面处理）
    │   └── right_panel/      # 右栏页面（每个文件都是一个单独的页面处理）
    │
    ├── lib/                   # 工具库和共享代码（如 LLM 的 id name，预设参数等）
    │   ├── database/          # 数据库表相关增删查改工具
    │   └── llm/               # LLM 相关工具
    │
    ├── context/               # 状态管理（单实例对象或者全局对象）
    │
    ├── public/                # 静态资源文件
    │
    ├── hooks/                 # React Hooks
    │
    ├── types/                 # 类型定义
    │   └── database/          # 数据库表相关类型定义
    │
    ├── 配置文件
    │   ├── next.config.ts     # Next.js 配置
    │   ├── tailwind.config.ts # Tailwind CSS 配置
    │   ├── tsconfig.json      # TypeScript 配置
    │   ├── postcss.config.mjs # PostCSS 配置
    │   └── .eslintrc.json     # ESLint 配置
    │
    ├── package.json           # 项目依赖配置
    └── README.md              # 项目说明文档
```

## 组织原则
── 保持项目结构清晰，遵循摸块化原则
── 相关功能应放在同一目录下
── 使用适当的目录命名，反映其包含内容
── 单个代码文件行数不宜超过三百行
── 单个函数不宜超过 50 行

## 命名规范

类名：PascalCase(大驼峰)
── 函数名：camelCase(小驼蜂) 或 snake_case
── 常量：UPPER SNAKE CASE
── 函数参数：使用语义化名称（如 `userInput` 而非 `input1`）
── 接口/类型：`IProps` → `ChatComponentProps`（具体化命名）
── 布尔变量：以 is/has/can 开头（`isLoading`, `hasPermission`）
── 事件处理：handle[元素][事件]（`handleInputChange`, `handleSubmitClick`）

## 注释规范

── 关键部分代码应有良好且简介的中文注释，但不宜过多，适量即可
── 所有注释之间应该形成良好的层次感和协同

# 2. 前端规范

## 前端架构

样式主要使用 Tailwind 来控制，极少数情况再用 Style 来控制
组件使用 antd 组件
尽可能以蓝、黑、白色调为主，所有实现都要考虑暗黑模式下的表现

所有提示功能统一使用 antd 的 Tooltip 创建

- **Rule 1**: 项目前端使用 next.js 框架、antd 组件库、lucide icons
- **Rule 2**: 项目前端使用 typescript 开发
- **Rule 3**: 项目前端网页需要兼容移动端和 PC 端
- **Rule 4**: 只在 /app 目录下进行核心总框架开发，剩余相关组件均放到 /components 目录下
- **Rule 5**: /components 目录下尽可能再创建子文件夹来容纳不同功能的内容，除非是类似侧边栏这种的基础组件
- **Rule 6**: 代码之间尽可能解耦，每个代码文件不超过 500 行代码
- **Rule 7**: 非必要不引入新的变量，尽可能复用现有组件和变量
- **Rule 8**: 包管理工具为 pnpm 
- **Rule 8**: 可以进行命令行调用包安装等，但是无需执行 pnpm dev 等项目启动命令
