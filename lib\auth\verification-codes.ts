// 验证码存储管理
// 生产环境应使用Redis等持久化存储

interface VerificationCodeData {
  code: string;
  expires: number;
}

// 简单的内存存储验证码
const verificationCodes = new Map<string, VerificationCodeData>();

// 生成6位随机验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 存储验证码
export function storeVerificationCode(phone: string, code: string, expiresInMinutes: number = 5): void {
  const expires = Date.now() + expiresInMinutes * 60 * 1000;
  verificationCodes.set(phone, { code, expires });
}

// 验证验证码
export function verifyCode(phone: string, inputCode: string): { valid: boolean; error?: string } {
  const storedCodeData = verificationCodes.get(phone);
  
  if (!storedCodeData) {
    return { valid: false, error: '验证码不存在或已过期' };
  }

  if (Date.now() > storedCodeData.expires) {
    verificationCodes.delete(phone);
    return { valid: false, error: '验证码已过期' };
  }

  if (storedCodeData.code !== inputCode) {
    return { valid: false, error: '验证码错误' };
  }

  // 验证成功后删除验证码
  verificationCodes.delete(phone);
  return { valid: true };
}

// 检查是否可以发送验证码（防止频繁发送）
export function canSendCode(phone: string): { canSend: boolean; remainingTime?: number } {
  const existingCode = verificationCodes.get(phone);
  
  if (existingCode && Date.now() < existingCode.expires) {
    const remainingTime = Math.ceil((existingCode.expires - Date.now()) / 1000);
    return { canSend: false, remainingTime };
  }
  
  return { canSend: true };
}

// 模拟发送短信的函数
export async function sendSMS(phone: string, code: string): Promise<boolean> {
  try {
    // 这里可以集成真实的短信服务商API
    // 比如阿里云短信、腾讯云短信等
    
    console.log(`发送验证码到 ${phone}: ${code}`);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 在开发环境下，我们直接返回成功
    return true;
  } catch (error) {
    console.error('发送短信失败:', error);
    return false;
  }
}
