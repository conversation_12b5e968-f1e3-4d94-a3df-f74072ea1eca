'use client';

import React from 'react';
import { Tooltip } from 'antd';
import { HomeOutlined, FileImageOutlined, VideoCameraOutlined, MessageOutlined, StarOutlined } from '@ant-design/icons';
import { FaSearch } from "react-icons/fa";
import { TbBookmarksFilled } from "react-icons/tb";
import { MdOutlineBookmarks } from "react-icons/md";
import { TbBookmarks } from "react-icons/tb";
import { PiChatTeardropDotsBold } from "react-icons/pi";
import { GrGooglePlus } from "react-icons/gr";

const iconItems = [
  { key: 'search', icon: <FaSearch size={18}/>, label: '搜索' },
  { key: 'bookmark', icon: <TbBookmarks size={26}/>, label: '收藏' },
  { key: 'chat', icon: <PiChatTeardropDotsBold size={24}/>, label: 'AI 助手' },
];

interface IconSideBarProps {
  selectedKey: string;
  onSelect: (key: string) => void;
}


const IconSidebar: React.FC<IconSideBarProps> = ({ selectedKey, onSelect }) => {
  return (
    <div
      className="w-12 m-1 flex flex-col items-center"
    >
      {/* 用户头像 */}
      <img  
        src="https://api.dicebear.com/7.x/pixel-art/svg?seed=cat"
        alt="用户头像"
        className="w-10 aspect-square rounded-full mb-2 mt-2 border border-gray-200 bg-white"
      />
      
      {/* 分隔线 */}
      <div className="w-[80%] h-px bg-gray-200 my-2" />

      {/* 图标列表 */}
      {iconItems.map(item => (
        <Tooltip title={item.label} placement="right" key={item.key}>
          <div
            className={`m-2 cursor-pointer rounded-[8px] transition-colors duration-200
              w-[36px] h-[36px] flex items-center justify-center
              ${selectedKey === item.key ? 'bg-gray-300 shadow-[0_2px_8px_#e6f4ff]' : 'text-gray-500'}
              hover:bg-gray-300`
            }
            onClick={() => onSelect(item.key)}
          >
            {item.icon}
          </div>
        </Tooltip>
      ))}
    </div>
  );
};

export default IconSidebar; 