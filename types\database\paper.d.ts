// 论文类型
export interface Paper {
    id: number; // 论文ID，自增主键
    title: string; // 论文标题
    authors?: string; // 作者（多个作者用逗号分隔）
    summary?: string; // 摘要
    sub_summary?: string; // 摘要的进一步精简
    keywords?: string; // 关键词（逗号分隔）
    published: string; // 发表日期
    source: string; // 期刊,会议名称
    doi?: string; // DOI标识符
    pdf_url: string; // 论文文件存储路径（如OSS路径）
    created_at: string; // 创建时间
    updated_at: string; // 更新时间
}

// 仅前端使用的额外字段类型，防止污染数据库表的严格对应
export interface PaperExtraFields {
    translated_title?: string;
    translated_summary?: string; // 中文摘要
    abstract_summary?: string; // 中文简短总结
    tags?: string[]; // 标签
}

export type PaperWithExtras = Paper & PaperExtraFields;

export type { Paper };