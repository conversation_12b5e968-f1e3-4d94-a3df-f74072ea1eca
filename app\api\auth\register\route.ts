import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, createUser, getUserById } from '@/lib/database/cloud_sql';
import { verifyCode } from '@/lib/auth/verification-codes';
import { getCurrentMySQLDateTime } from '@/lib/utils/datetime';
import bcrypt from 'bcryptjs';

export async function POST(req: NextRequest) {
  try {
    const { email, password, username, verificationCode } = await req.json();

    // 验证输入
    if (!email || !password || !username || !verificationCode) {
      return NextResponse.json(
        { error: '所有字段都是必填的' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json(
        { error: '密码至少需要6位字符' },
        { status: 400 }
      );
    }

    // 验证邮箱验证码
    const codeVerification = verifyCode(email, verificationCode);
    if (!codeVerification.valid) {
      return NextResponse.json(
        { error: codeVerification.error },
        { status: 400 }
      );
    }

    // 检查用户是否已存在
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 409 }
      );
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const currentTime = getCurrentMySQLDateTime();

    const newUser = {
      // id: 0, // 数据库会自动生成实际 id
      email,
      password: hashedPassword,
      username,
      role: 'user',
      created_at: currentTime,
      updated_at: currentTime,
      last_login_at: currentTime,
    };

    const result = await createUser(newUser);
    const insertId = (result as any).insertId;

    // 从数据库获取完整的用户信息（包含自动生成的ID）
    const createdUser = await getUserById(insertId);
    if (!createdUser) {
      throw new Error('创建用户后无法获取用户信息');
    }

    // 返回用户信息（不包含密码）
    const { password: _, ...userResponse } = createdUser;

    return NextResponse.json({
      success: true,
      user: userResponse,
      message: '注册成功'
    });

  } catch (error) {
    console.error('注册错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
