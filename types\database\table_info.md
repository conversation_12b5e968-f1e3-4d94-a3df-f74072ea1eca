# 用户

## User（用户表）

```MySQL
CREATE TABLE IF NOT EXISTS `user` (
  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '用户编号，主键',
  email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  username VARCHAR(50) DEFAULT 'Owo' NOT NULL COMMENT '用户昵称，不允许为空',
  role CHAR(10) DEFAULT 'user' NOT NULL COMMENT '用户角色（admin、user、guest，权限管理）'
  avatar_url VARCHAR(255) COMMENT '头像 url，使用 OSS 存储',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at DATETIME COMMENT '最后登录时间',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  social_type VARCHAR(20) COMMENT '第三方平台类型（如微信、GitHub等）',
  social_id VARCHAR(100) COMMENT '第三方登录ID',
  preferences JSON COMMENT '用户偏好设置（可用 JSON 存储）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

## LLM 对话

**Conversation（管理对话）**

```
CREATE TABLE IF NOT EXISTS `conversation` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID，自增主键',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID，关联User表',
    title VARCHAR(100) NOT NULL COMMENT '会话标题（用户可自定义或自动生成）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '会话创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    meta JSON COMMENT '会话附加信息',
    INDEX idx_user_id(user_id),
    CONSTRAINT fk_conversation_user FOREIGN KEY (user_id) REFERENCES `user`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话会话表';
```

**ChatMessage（管理对话消息）**

```MySQL
CREATE TABLE IF NOT EXISTS `chat_message` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    conversation_id BIGINT UNSIGNED NOT NULL COMMENT '会话ID，关联Conversation表',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID，关联User表',
    role ENUM('user','ai') NOT NULL COMMENT '消息角色',
    content TEXT NOT NULL COMMENT '消息内容',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '消息创建时间',
    meta JSON COMMENT '附加信息',
    INDEX idx_conversation_id(conversation_id),
    CONSTRAINT fk_chat_conversation FOREIGN KEY (conversation_id) REFERENCES `conversation`(id),
    CONSTRAINT fk_chat_user FOREIGN KEY (user_id) REFERENCES `user`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话消息表';
```

## 收藏夹

**关系图**

```
User
 └─< FavoriteFolder (多级目录, user_id, parent_id)
       └─< FavoritePaper (每条记录唯一user_id, folder_id, paper_id)
               └─< FavoriteNote (每条笔记唯一指向FavoritePaper, 可多条)
Paper
```

**Paper（管理论文元信息）**

```MySQL
CREATE TABLE IF NOT EXISTS `paper` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '论文ID，自增主键',
    title VARCHAR(255) NOT NULL COMMENT '论文标题',
    authors VARCHAR(500) COMMENT '作者（多个作者用逗号分隔）',
    summary TEXT COMMENT '摘要',
    sub_summary TEXT COMMENT '摘要的进一步精简',
    keywords VARCHAR(255) COMMENT '关键词（逗号分隔）',
    published DATE NOT NULL COMMENT '发表日期',
    source VARCHAR(255) NOT NULL COMMENT '期刊,会议名称',
    doi VARCHAR(100) COMMENT 'DOI标识符',
    pdf_url VARCHAR(512) NOT NULL COMMENT '论文文件存储路径（如OSS路径）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FULLTEXT INDEX idx_fulltext_search (title, summary, keywords),
    INDEX idx_published (published),
    INDEX idx_source (source),
    INDEX idx_authors (authors)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文信息表';
```

**PaperReference（管理论文引用信息，暂时不建）**

```MySQL
CREATE TABLE IF NOT EXISTS `paper_reference` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    paper_id BIGINT UNSIGNED NOT NULL COMMENT '本论文ID（引用者）',
    referenced_paper_id BIGINT UNSIGNED NOT NULL COMMENT '被引用论文ID（被引用者）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '建立关系时间',
    CONSTRAINT fk_ref_paper FOREIGN KEY (paper_id) REFERENCES `paper`(id),
    CONSTRAINT fk_ref_referenced FOREIGN KEY (referenced_paper_id) REFERENCES `paper`(id),
    UNIQUE KEY uq_paper_reference (paper_id, referenced_paper_id)  -- 防止重复关系
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文引用关系表';
```

**FavoriteFolder（收藏夹文件）**

后续收藏夹还可以放其他内容，比如用户自己上传的 PDF，不仅限于网站内的论文
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_folder` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '收藏夹ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    parent_id BIGINT UNSIGNED DEFAULT NULL COMMENT '上级收藏夹ID，顶级为NULL',
    name VARCHAR(100) NOT NULL COMMENT '收藏夹名称',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id(user_id),
    INDEX idx_parent_id(parent_id),
    CONSTRAINT fk_folder_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_folder_parent FOREIGN KEY (parent_id) REFERENCES `favorite_folder`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏夹（多级目录）表';```

// 其中目录层级可以通过以下代码计算
```MySQL
WITH RECURSIVE folder_path AS (
    SELECT id, parent_id, 1 AS level
    FROM FavoriteFolder
    WHERE id = 123  -- 要查询的收藏夹ID

    UNION ALL

    SELECT f.id, f.parent_id, fp.level + 1
    FROM FavoriteFolder f
    JOIN folder_path fp ON f.id = fp.parent_id
)
SELECT MAX(level) AS folder_level
FROM folder_path;
```

**FavoritePaper（收藏的论文）**
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_paper` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '用户收藏论文ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    folder_id BIGINT UNSIGNED NOT NULL COMMENT '所属收藏夹ID',
    paper_id BIGINT UNSIGNED NOT NULL COMMENT '被收藏的论文ID（关联Paper表）',
    custom_title VARCHAR(255) COMMENT '用户自定义的论文标题',
    translated_title VARCHAR(255) COMMENT '论文中文标题',
    translated_summary TEXT COMMENT '论文中文摘要',
    translated_abstract_summary TEXT COMMENT '中文简短总结',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id(user_id),
    INDEX idx_folder_id(folder_id),
    INDEX idx_paper_id(paper_id),
    UNIQUE KEY uniq_user_paper_folder(user_id, paper_id, folder_id),
    CONSTRAINT fk_favpaper_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_favpaper_folder FOREIGN KEY (folder_id) REFERENCES `favorite_folder`(id),
    CONSTRAINT fk_favpaper_paper FOREIGN KEY (paper_id) REFERENCES `paper`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏的论文表';
```

**FavoriteNote（收藏论文的笔记）**
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_note` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '笔记ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    favorite_paper_id BIGINT UNSIGNED NOT NULL COMMENT '用户收藏论文ID（关联FavoritePaper表）',
    title VARCHAR(255) NOT NULL COMMENT '笔记标题',
    content TEXT COMMENT '笔记内容',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id(user_id),
    INDEX idx_favpaper_id(favorite_paper_id),
    CONSTRAINT fk_note_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_note_favpaper FOREIGN KEY (favorite_paper_id) REFERENCES `favorite_paper`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏论文笔记表';
```