import '@ant-design/v5-patch-for-react-19';
import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { SearchProvider } from '@/context/SearchContext';
import { UserProvider } from '@/context/UserContext';
import { IconSidebarProvider } from '@/context/IconSideBarContext';
import { App } from 'antd';

import { ConfigProvider } from 'antd';
import theme from '@/context/ThemeConfig';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <App>
          <UserProvider>
            <IconSidebarProvider>
              <SearchProvider>
                <ConfigProvider theme={theme}>
                  {children}
                </ConfigProvider>
              </SearchProvider>
            </IconSidebarProvider>
          </UserProvider>
        </App>
      </body>
    </html>
  );
}
