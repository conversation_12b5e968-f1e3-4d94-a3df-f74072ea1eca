'use client';

import React from 'react';
import { Card, Avatar, Typography, Button, Space, Divider } from 'antd';
import { UserOutlined, LogoutOutlined, SettingOutlined } from '@ant-design/icons';
import { useUserContext } from '@/context/UserContext';

const { Title, Text } = Typography;

export default function UserInfoPanel() {
  const { user, logout } = useUserContext();

  if (!user) {
    return null; // 如果没有用户信息，不显示此面板
  }

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="p-6 h-full bg-white">
      <Card className="shadow-sm">
        <div className="text-center mb-6">
          <Avatar
            size={80}
            src={user.avatar_url}
            icon={<UserOutlined />}
            className="mb-4"
          />
          <Title level={4} className="mb-2">{user.username}</Title>
          <Text type="secondary">{user.email}</Text>
        </div>

        <Divider />

        <div className="space-y-4">
          <div>
            <Text strong>用户角色：</Text>
            <Text className="ml-2">{user.role}</Text>
          </div>

          {user.phone && (
            <div>
              <Text strong>手机号：</Text>
              <Text className="ml-2">{user.phone}</Text>
            </div>
          )}

          <div>
            <Text strong>注册时间：</Text>
            <Text className="ml-2">
              {new Date(user.created_at).toLocaleDateString()}
            </Text>
          </div>

          {user.last_login_at && (
            <div>
              <Text strong>最后登录：</Text>
              <Text className="ml-2">
                {new Date(user.last_login_at).toLocaleString()}
              </Text>
            </div>
          )}
        </div>

        <Divider />

        <Space direction="vertical" className="w-full">
          <Button
            type="default"
            icon={<SettingOutlined />}
            block
            className="mb-2"
          >
            账户设置
          </Button>
          <Button
            type="primary"
            danger
            icon={<LogoutOutlined />}
            onClick={handleLogout}
            block
          >
            退出登录
          </Button>
        </Space>
      </Card>
    </div>
  );
}