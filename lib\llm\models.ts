export interface ModelConfig {
  id: string;
  name: string;
  description: string;
}

export const models: ModelConfig[] = [

    // {
    //     id: 'qwen3_30b_a3b',
    //     name: 'Qwen3-30B-A3B',
    //     description: '速度起飞 Qwen3'
    // },
    // {
    //     id: 'deepseek_v3_op',
    //     name: 'DeepSeek-V3',
    //     description: '最新 DeepSeek-V3-0324'
    // },
    {
        id: 'deepseek_v3_sc',
        name: 'DeepSeek-V3',
        description: '最新 DeepSeek-V3-0324'
    },
    {
        id: 'deepseek_r1_op',
        name: 'DeepSeek-R1',
        description: '最新 DeepSeek-R1-0528'
    },
    // {
    //     id: 'deepseek_r1_sc',
    //     name: 'DeepSeek-R1',
    //     description: 'DeepSeek-R1'
    // },
    // {
    //     id: 'qwen_long_32b',
    //     name: 'QwenLong-L1-32B',
    //     description: '长上下文文档问答可媲美 Claude-3.7-Sonnet-Thinking'
    // },
    // 'claude4_sonnet': ModelSelector.claude4_sonnet,
    // 'gpt_4o': ModelSelector.gpt4o,
    // 'o4_mini': ModelSelector.o4_mini,
    // 'gemini_25_flash': ModelSelector.gemini25_flash,
    // {
    //     id: 'gpt_4o',
    //     name: 'GPT-4o',
    //     description: '速度 ↑ 通用能力 ↑  '
    // },
    {
        id: 'gpt41',
        name: 'GPT-4.1',
        description: '最新 4-14 发布，速度 ↑ 通用能力 ↑  '
    },
    {
        id: 'gemini_25_flash',
        name: 'gemini-2.5-flash',
        description: '最新 5-20 发布，速度 ↑↑↑ 通用能力 ↑ '
    },
    {
        id: 'claude4_sonnet',
        name: 'Claude4-Sonnet',
        description: '最新 5-23 模型，速度 ↓ 编程能力 ↑↑↑ '
    },
    // {
    //     id: 'o4_mini',
    //     name: 'o4-mini',
    //     description: 'GPT 系列 o4-mini 思考模型，通用能力 ↑  '
    // },
    // {
    //     id: 'qwen3_235b_a22b',
    //     name: 'Qwen3-235B-A22B',
    //     description: 'Qwen3 最大杯 MOE'
    // },
    // {
    //     id: 'glm4_32b',
    //     name: 'GLM4-32B',
    //     description: '智谱 均衡 (非思考)'
    // },
    // {
    //     id: 'glm4_32b_z1',
    //     name: 'GLM4-32B-Thinking',
    //     description: '智谱 均衡'
    // },
    // {
    //     id: 'qwen25_72b',
    //     name: 'Qwen2.5-72B',
    //     description: 'Qwen2.5 最大杯 (非思考，速度稍快)'
    // },
];

export const DEFAULT_MODEL = models[0];