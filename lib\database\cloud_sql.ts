import { pool } from './connect';
import type { User, CreateUserData } from '@/types/database/user';
import type { Conversation } from '@/types/database/conversation';
import type { ChatMessage } from '@/types/database/chat_message';
import type { Paper } from '@/types/database/paper';
import type { FavoriteFolder } from '@/types/database/favorite_folder';
import type { FavoritePaper } from '@/types/database/favorite_paper';
import type { FavoriteNote } from '@/types/database/favorite_note';

// ========== User ==========
export async function getUserById(id: number) {
  const [rows] = await pool.query('SELECT * FROM user WHERE id = ?', [id]);
  return (rows as User[])[0];
}

export async function getUserByEmail(email: string) {
  const [rows] = await pool.query('SELECT * FROM user WHERE email = ?', [email]);
  return (rows as User[])[0];
}

export async function createUser(user: CreateUserData) {
  const [result] = await pool.query('INSERT INTO user SET ?', [user]);
  return result;
}

export async function updateUser(id: number, user: Partial<User>) {
  const [result] = await pool.query('UPDATE user SET ? WHERE id = ?', [user, id]);
  return result;
}

export async function deleteUser(id: number) {
  const [result] = await pool.query('DELETE FROM user WHERE id = ?', [id]);
  return result;
}

// ========== Conversation ==========
export async function getConversationById(id: number) {
  const [rows] = await pool.query('SELECT * FROM conversation WHERE id = ?', [id]);
  return (rows as Conversation[])[0];
}

export async function getConversationsByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM conversation WHERE user_id = ?', [user_id]);
  return rows as Conversation[];
}

export async function createConversation(conversation: Conversation) {
  const [result] = await pool.query('INSERT INTO conversation SET ?', [conversation]);
  return result;
}

export async function updateConversation(id: number, conversation: Partial<Conversation>) {
  const [result] = await pool.query('UPDATE conversation SET ? WHERE id = ?', [conversation, id]);
  return result;
}

export async function deleteConversation(id: number) {
  const [result] = await pool.query('DELETE FROM conversation WHERE id = ?', [id]);
  return result;
}

// ========== ChatMessage ==========
export async function getMessagesByConversation(conversation_id: number) {
  const [rows] = await pool.query('SELECT * FROM chat_message WHERE conversation_id = ? ORDER BY created_at ASC', [conversation_id]);
  return rows as ChatMessage[];
}

export async function createChatMessage(message: ChatMessage) {
  const [result] = await pool.query('INSERT INTO chat_message SET ?', [message]);
  return result;
}

export async function deleteChatMessage(id: number) {
  const [result] = await pool.query('DELETE FROM chat_message WHERE id = ?', [id]);
  return result;
}

// ========== Paper ==========
export async function getPaperById(id: number) {
  const [rows] = await pool.query('SELECT * FROM paper WHERE id = ?', [id]);
  return (rows as Paper[])[0];
}

export async function searchPapers(keyword: string) {
  const [rows] = await pool.query('SELECT * FROM paper WHERE MATCH(title, summary, keywords) AGAINST(? IN NATURAL LANGUAGE MODE)', [keyword]);
  return rows as Paper[];
}

export async function createPaper(paper: Paper) {
  const [result] = await pool.query('INSERT INTO paper SET ?', [paper]);
  return result;
}

export async function updatePaper(id: number, paper: Partial<Paper>) {
  const [result] = await pool.query('UPDATE paper SET ? WHERE id = ?', [paper, id]);
  return result;
}

export async function deletePaper(id: number) {
  const [result] = await pool.query('DELETE FROM paper WHERE id = ?', [id]);
  return result;
}

// ========== FavoriteFolder ==========
export async function getFoldersByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_folder WHERE user_id = ?', [user_id]);
  return rows as FavoriteFolder[];
}

export async function createFavoriteFolder(folder: FavoriteFolder) {
  const [result] = await pool.query('INSERT INTO favorite_folder SET ?', [folder]);
  return result;
}

export async function updateFavoriteFolder(id: number, folder: Partial<FavoriteFolder>) {
  const [result] = await pool.query('UPDATE favorite_folder SET ? WHERE id = ?', [folder, id]);
  return result;
}

export async function deleteFavoriteFolder(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_folder WHERE id = ?', [id]);
  return result;
}

// ========== FavoritePaper ==========
export async function getFavoritePapersByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_paper WHERE user_id = ?', [user_id]);
  return rows as FavoritePaper[];
}

export async function createFavoritePaper(fav: FavoritePaper) {
  const [result] = await pool.query('INSERT INTO favorite_paper SET ?', [fav]);
  return result;
}

export async function updateFavoritePaper(id: number, fav: Partial<FavoritePaper>) {
  const [result] = await pool.query('UPDATE favorite_paper SET ? WHERE id = ?', [fav, id]);
  return result;
}

export async function deleteFavoritePaper(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_paper WHERE id = ?', [id]);
  return result;
}

// ========== FavoriteNote ==========
export async function getNotesByFavoritePaper(favorite_paper_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_note WHERE favorite_paper_id = ?', [favorite_paper_id]);
  return rows as FavoriteNote[];
}

export async function createFavoriteNote(note: FavoriteNote) {
  const [result] = await pool.query('INSERT INTO favorite_note SET ?', [note]);
  return result;
}

export async function updateFavoriteNote(id: number, note: Partial<FavoriteNote>) {
  const [result] = await pool.query('UPDATE favorite_note SET ? WHERE id = ?', [note, id]);
  return result;
}

export async function deleteFavoriteNote(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_note WHERE id = ?', [id]);
  return result;
}