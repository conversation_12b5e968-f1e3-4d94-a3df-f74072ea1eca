import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, createUser } from '@/lib/database/cloud_sql';
import { verifyCode } from '@/lib/auth/verification-codes';
import bcrypt from 'bcryptjs';

export async function POST(req: NextRequest) {
  try {
    const { email, password, username, phone, verificationCode } = await req.json();

    // 验证输入
    if (!email || !password || !username || !phone || !verificationCode) {
      return NextResponse.json(
        { error: '所有字段都是必填的' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      return NextResponse.json(
        { error: '手机号格式不正确' },
        { status: 400 }
      );
    }

    // 验证密码长度
    if (password.length < 6) {
      return NextResponse.json(
        { error: '密码至少需要6位字符' },
        { status: 400 }
      );
    }

    // 验证短信验证码
    const codeVerification = verifyCode(phone, verificationCode);
    if (!codeVerification.valid) {
      return NextResponse.json(
        { error: codeVerification.error },
        { status: 400 }
      );
    }

    // 检查用户是否已存在
    const existingUser = await getUserByEmail(email);
    if (existingUser) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 409 }
      );
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12);

    // 创建用户
    const newUser = {
      email,
      password: hashedPassword,
      username,
      phone,
      role: 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      last_login_at: new Date().toISOString(),
    };

    const result = await createUser(newUser);

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = newUser;
    const userResponse = {
      ...userWithoutPassword,
      id: (result as any).insertId, // 添加数据库生成的ID
    };

    return NextResponse.json({
      success: true,
      user: userResponse,
      message: '注册成功'
    });

  } catch (error) {
    console.error('注册错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
