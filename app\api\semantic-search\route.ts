import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const headers = {
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  };

  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  const page = searchParams.get('page') || 1;
  const limit = searchParams.get('limit') || 12;
  const years = searchParams.get('years') || '';
  const deepsearch = searchParams.get('deepsearch') || 'false';
  const source = searchParams.get('source') || 'arxiv';

  if (!query) {
    return NextResponse.json(
      { error: '搜索查询不能为空' },
      { status: 400, headers }
    );
  }

  try {
    console.log('api route source', source)
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BACKEND_URL}/semantic-search?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}&years=${years}&deepsearch=${deepsearch}&source=${source}`,
      { cache: 'no-store' }
    );
    
    if (!response.ok) {
      throw new Error('后端搜索服务响应错误');
    }
    const data = await response.json();
    return NextResponse.json(data, { headers });
    
  } catch (error) {
    console.error('语义搜索错误:', error);
    return NextResponse.json(
      { error: '搜索服务暂时不可用' },
      { status: 500, headers }
    );
  }
}
