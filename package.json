{"name": "axsight-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.4.0", "@types/bcryptjs": "^3.0.0", "@types/react-grid-layout": "^1.3.5", "allotment": "^1.20.4", "antd": "^5.26.0", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "katex": "^0.16.22", "lucide-react": "^0.514.0", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "mysql2": "^3.14.1", "next": "15.3.3", "nodemailer": "^6.9.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.2", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}