import { NextRequest, NextResponse } from 'next/server';
import { getUserByEmail, updateUser } from '@/lib/database/cloud_sql';
import { getCurrentMySQLDateTime } from '@/lib/utils/datetime';
import bcrypt from 'bcryptjs';

export async function POST(req: NextRequest) {
  try {
    const { email, password } = await req.json();

    // 验证输入
    if (!email || !password) {
      return NextResponse.json(
        { error: '邮箱和密码不能为空' },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await getUserByEmail(email);
    if (!user) {
      return NextResponse.json(
        { error: '用户不存在或密码错误' },
        { status: 401 }
      );
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: '用户不存在或密码错误' },
        { status: 401 }
      );
    }

    // 更新最后登录时间
    const currentTime = getCurrentMySQLDateTime();

    await updateUser(user.id, {
      last_login_at: currentTime
    });

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user;
    const userResponse = {
      ...userWithoutPassword,
      last_login_at: currentTime
    };

    return NextResponse.json({
      success: true,
      user: userResponse,
      message: '登录成功'
    });

  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
