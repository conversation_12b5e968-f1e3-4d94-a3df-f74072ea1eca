import React from 'react';
import { Divider, Tree } from 'antd';
import type { GetProps, TreeDataNode } from 'antd';
import { Typography } from 'antd';
const { Text } = Typography;

type DirectoryTreeProps = GetProps<typeof Tree.DirectoryTree>;

const { DirectoryTree } = Tree;

const treeData: TreeDataNode[] = [
  {
    title: 'parent 0',
    key: '0-0',
    children: [
      { title: 'leaf 0-0', key: '0-0-0', isLeaf: true },
      { title: 'leaf 0-1', key: '0-0-1', isLeaf: true },
    ],
  },
  {
    title: 'parent 1',
    key: '0-1',
    children: [
      { title: 'leaf 1-0', key: '0-1-0', isLeaf: true },
      { title: 'leaf 1-1', key: '0-1-1', isLeaf: true },
    ],
  },
];

const App: React.FC = () => {
  const onSelect: DirectoryTreeProps['onSelect'] = (keys, info) => {
    console.log('Trigger Select', keys, info);
  };

  const onExpand: DirectoryTreeProps['onExpand'] = (keys, info) => {
    console.log('Trigger Expand', keys, info);
  };

  return (
    <DirectoryTree
    //   multiple
      draggable
      defaultExpandAll
      expandAction={false}
      onSelect={onSelect}
      onExpand={onExpand}
      treeData={treeData}
    />
  );
};

export const Bookmark: React.FC = () => (
  <div
    style={{
    //   background: 'white',
      borderRight: '1px solid #e8e8e8',
      height: '100%',
      width: '100%',
      overflow: 'auto',
      display: 'flex',
      flexDirection: 'column'
    }}
  >
    <div style={{ padding: '16px' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <Text strong style={{ fontSize: '16px' }}>收藏</Text>
      </div>
      <Divider style={{ margin: '8px 0' }} />
      <App />
    </div>
  </div>
);

export default Bookmark;