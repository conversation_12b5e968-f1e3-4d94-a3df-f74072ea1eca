/**
 * 时间格式转换工具函数
 */

/**
 * 将 JavaScript Date 对象转换为 MySQL datetime 格式
 * @param date Date 对象，默认为当前时间
 * @returns MySQL datetime 格式字符串 (YYYY-MM-DD HH:mm:ss)
 */
export function toMySQLDateTime(date: Date = new Date()): string {
  return date.toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * 将 MySQL datetime 字符串转换为 JavaScript Date 对象
 * @param mysqlDateTime MySQL datetime 格式字符串
 * @returns Date 对象
 */
export function fromMySQLDateTime(mysqlDateTime: string): Date {
  return new Date(mysqlDateTime.replace(' ', 'T') + 'Z');
}

/**
 * 获取当前时间的 MySQL datetime 格式
 * @returns 当前时间的 MySQL datetime 格式字符串
 */
export function getCurrentMySQLDateTime(): string {
  return toMySQLDateTime();
}
