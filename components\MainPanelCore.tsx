'use client';

import React from 'react';
import { Typography, Card, Input, Button, Space } from 'antd';
import Search from './search/MainPanel';
import { useIconSidebarContext } from '@/context/IconSideBarContext';

const {Text } = Typography;

export default function MainPanel() {
  const { selectedKey } = useIconSidebarContext();
  const getContentByKey = () => {
    switch (selectedKey) {
      case 'search':    
        return <Search />;
      
      case 'bookmark':
        return <Search />;

      default:
        return (
          <div style={{ 
            padding: '24px', 
            height: '100%', 
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <Text style={{ color: '#999' }}>
              该功能正在开发中，敬请期待...
            </Text>
          </div>
        );
    }
  };

  return (
    <div style={{ 
      background: 'white',
      height: '100%',
      width: '100%',
      overflow: 'hidden'
    }}>
      {getContentByKey()}
    </div>
  );
};
