// 验证码存储管理
// 支持多人访问的内存存储，带自动清理机制

interface VerificationCodeData {
  code: string;
  expires: number;
  createdAt: number;
}

// 内存存储验证码（支持邮箱和手机号）
const verificationCodes = new Map<string, VerificationCodeData>();

// 定期清理过期验证码，防止内存泄漏
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of verificationCodes.entries()) {
    if (now > data.expires) {
      verificationCodes.delete(key);
    }
  }
}, 60000); // 每分钟清理一次

// 生成6位随机验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 存储验证码
export function storeVerificationCode(identifier: string, code: string, expiresInMinutes: number = 5): void {
  const now = Date.now();
  const expires = now + expiresInMinutes * 60 * 1000;
  verificationCodes.set(identifier, { code, expires, createdAt: now });
  console.log(`验证码已存储: ${identifier} -> ${code}, 过期时间: ${new Date(expires).toLocaleString()}`);
}

// 验证验证码
export function verifyCode(identifier: string, inputCode: string): { valid: boolean; error?: string } {
  console.log(`验证验证码: ${identifier} -> 输入: ${inputCode}`);
  const storedCodeData = verificationCodes.get(identifier);

  if (!storedCodeData) {
    console.log(`验证码不存在: ${identifier}`);
    return { valid: false, error: '验证码不存在或已过期' };
  }

  console.log(`存储的验证码: ${storedCodeData.code}, 过期时间: ${new Date(storedCodeData.expires).toLocaleString()}`);

  if (Date.now() > storedCodeData.expires) {
    console.log(`验证码已过期: ${identifier}`);
    verificationCodes.delete(identifier);
    return { valid: false, error: '验证码已过期' };
  }

  if (storedCodeData.code !== inputCode) {
    console.log(`验证码错误: 期望 ${storedCodeData.code}, 实际 ${inputCode}`);
    return { valid: false, error: '验证码错误' };
  }

  // 验证成功后删除验证码
  console.log(`验证码验证成功: ${identifier}`);
  verificationCodes.delete(identifier);
  return { valid: true };
}

// 检查是否可以发送验证码（防止频繁发送）
export function canSendCode(identifier: string): { canSend: boolean; remainingTime?: number } {
  const existingCode = verificationCodes.get(identifier);

  if (existingCode) {
    const now = Date.now();
    // 如果验证码已过期，清理它
    if (now > existingCode.expires) {
      verificationCodes.delete(identifier);
      console.log(`清理过期验证码: ${identifier}`);
      return { canSend: true };
    }

    // 检查是否在60秒内发送过验证码（防止频繁发送）
    const timeSinceCreated = now - existingCode.createdAt;
    if (timeSinceCreated < 60000) { // 60秒内不能重复发送
      const remainingTime = Math.ceil((60000 - timeSinceCreated) / 1000);
      console.log(`发送过于频繁: ${identifier}, 剩余时间: ${remainingTime}秒`);
      return { canSend: false, remainingTime };
    }
  }

  console.log(`可以发送验证码: ${identifier}`);
  return { canSend: true };
}

// 发送邮件验证码的函数
export async function sendEmailCode(email: string, code: string): Promise<boolean> {
  try {
    // 动态导入 nodemailer
    const nodemailer = await import('nodemailer');

    // 从环境变量读取邮箱配置
    const mailHost = 'smtp.163.com';
    const mailUser = process.env.SEND_EMAIL; // 发件人邮箱
    const mailPass = process.env.AUTH_163;   // 授权码

    if (!mailUser || !mailPass) {
      console.error('邮件配置不完整，请设置 SEND_EMAIL 和 AUTH_163 环境变量');
      return false;
    }

    // 创建邮件传输器
    const transporter = nodemailer.createTransport({
      host: mailHost,
      port: 25,
      secure: false, // 163邮箱使用25端口，不使用SSL
      auth: {
        user: mailUser,
        pass: mailPass,
      },
    });

    // 邮件内容
    const mailOptions = {
      from: `"Axsight 验证服务" <${mailUser}>`,
      to: email,
      subject: 'Axsight 注册验证码',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1890ff;">Axsight 注册验证码</h2>
          <p>您好！</p>
          <p>您正在注册 Axsight 账户，您的验证码是：</p>
          <div style="background-color: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #1890ff; letter-spacing: 5px;">${code}</span>
          </div>
          <p>验证码有效期为 5 分钟，请及时使用。</p>
          <p>如果您没有申请注册，请忽略此邮件。</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #999; font-size: 12px;">此邮件由系统自动发送，请勿回复。</p>
        </div>
      `,
    };

    // 发送邮件
    await transporter.sendMail(mailOptions);
    console.log(`验证码邮件发送成功: ${email}`);
    return true;

  } catch (error) {
    console.error('发送邮件失败:', error);
    return false;
  }
}

// 模拟发送短信的函数（保留以备后用）
export async function sendSMS(phone: string, code: string): Promise<boolean> {
  try {
    console.log(`发送验证码到 ${phone}: ${code}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return true;
  } catch (error) {
    console.error('发送短信失败:', error);
    return false;
  }
}
