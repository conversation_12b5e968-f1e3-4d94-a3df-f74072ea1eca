// 验证码存储管理
// 生产环境应使用Redis等持久化存储

interface VerificationCodeData {
  code: string;
  expires: number;
}

// 简单的内存存储验证码（支持邮箱和手机号）
const verificationCodes = new Map<string, VerificationCodeData>();

// 生成6位随机验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 存储验证码
export function storeVerificationCode(identifier: string, code: string, expiresInMinutes: number = 5): void {
  const expires = Date.now() + expiresInMinutes * 60 * 1000;
  verificationCodes.set(identifier, { code, expires });
}

// 验证验证码
export function verifyCode(identifier: string, inputCode: string): { valid: boolean; error?: string } {
  const storedCodeData = verificationCodes.get(identifier);

  if (!storedCodeData) {
    return { valid: false, error: '验证码不存在或已过期' };
  }

  if (Date.now() > storedCodeData.expires) {
    verificationCodes.delete(identifier);
    return { valid: false, error: '验证码已过期' };
  }

  if (storedCodeData.code !== inputCode) {
    return { valid: false, error: '验证码错误' };
  }

  // 验证成功后删除验证码
  verificationCodes.delete(identifier);
  return { valid: true };
}

// 检查是否可以发送验证码（防止频繁发送）
export function canSendCode(identifier: string): { canSend: boolean; remainingTime?: number } {
  const existingCode = verificationCodes.get(identifier);

  if (existingCode && Date.now() < existingCode.expires) {
    const remainingTime = Math.ceil((existingCode.expires - Date.now()) / 1000);
    return { canSend: false, remainingTime };
  }

  return { canSend: true };
}

// 发送邮件验证码的函数
export async function sendEmailCode(email: string, code: string): Promise<boolean> {
  try {
    // 这里可以集成真实的邮件服务
    // 比如使用 nodemailer 或其他邮件服务商API

    console.log(`发送验证码到邮箱 ${email}: ${code}`);

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 在开发环境下，我们直接返回成功
    // 生产环境中应该调用真实的邮件发送服务
    return true;
  } catch (error) {
    console.error('发送邮件失败:', error);
    return false;
  }
}

// 模拟发送短信的函数（保留以备后用）
export async function sendSMS(phone: string, code: string): Promise<boolean> {
  try {
    console.log(`发送验证码到 ${phone}: ${code}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return true;
  } catch (error) {
    console.error('发送短信失败:', error);
    return false;
  }
}
