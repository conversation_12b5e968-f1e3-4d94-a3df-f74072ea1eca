import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import path from 'path';

// 加载 .env.local 文件
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

const dbUser = process.env.DB_USER;
const dbPassword = process.env.DB_PASSWORD;

export const pool = mysql.createPool({
  host: 'rm-bp1lh6ays376812s2bo.mysql.rds.aliyuncs.com',
  user: dbUser,
  password: dbPassword,
  database: 'axsight_test',
  port: 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// 查询 User 表
export async function getUsers() {
  const [rows] = await pool.query('SELECT * FROM User');
  return rows;
}
