import React from "react";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import type { BubbleProps } from '@ant-design/x';
import { Tooltip } from 'antd';

// markdown 渲染函数，供 Chat 组件等复用
export const renderMarkdown: BubbleProps['messageRender'] = (content) => {
  // 预处理：将独占一行的 [ ... ] 替换为 $$ ... $$
  const preprocessed = (typeof content === 'string')
    ? content.replace(/^[ \t]*\[([\s\S]*?)\][ \t]*$/gm, (match, formula) => `$$\n${formula.trim()}\n$$`)
    : content;

  return (
    <div className="markdown-content ">
      <ReactMarkdown
        children={preprocessed}
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          code(props) {
            const { inline, className, children, ...rest } = props as any;
            return (
              <code
                className={`bg-gray-100 dark:bg-neutral-800 rounded px-1 font-mono text-sm ${className || ''}`}
                {...rest}
              >
                {children}
              </code>
            );
          },
          ol: ({ node, ...props }) => (
            <ol className="list-decimal ml-6 my-2 dark:text-gray-200" {...props} />
          ),
          ul: ({ node, ...props }) => (
            <ul className="list-disc ml-6 my-2 dark:text-gray-200" {...props} />
          ),
          li: ({ node, ...props }) => (
            <li className="mb-1 pl-1 dark:text-gray-300" {...props} />
          ),
          div: ({ node, className, ...props }) => {
            if (className && className.includes('katex-display')) {
              return (
                <div className="overflow-x-auto">
                  <div
                    className={className}
                    style={{ display: 'inline-block', minWidth: 'max-content', width: 'auto' }}
                    {...props}
                  />
                </div>
              );
            }
            return <div className={className} {...props} />;
          },
          h1: ({ node, ...props }) => (
            <h1 className="text-2xl font-bold mt-4 mb-2 dark:text-white" {...props} />
          ),
          h2: ({ node, ...props }) => (
            <h2 className="text-xl font-semibold mt-3 mb-2 dark:text-white" {...props} />
          ),
          h3: ({ node, ...props }) => (
            <h3 className="text-lg font-semibold mt-3 mb-1 dark:text-white" {...props} />
          ),
          h4: ({ node, ...props }) => (
            <h4 className="text-base font-semibold mt-2 mb-1 dark:text-white" {...props} />
          ),
          h5: ({ node, ...props }) => (
            <h5 className="text-sm font-semibold mt-2 mb-1 dark:text-white" {...props} />
          ),
          p: ({ node, ...props }) => (
            <p className="my-0 dark:text-white" {...props} />
          ),
          blockquote: ({ node, ...props }) => (
            <blockquote className="border-l-4 border-blue-400 bg-blue-50 dark:bg-neutral-800 dark:border-blue-600 pl-4 pr-2 py-2 my-2 dark:text-white italic" {...props} />
          ),
          table: ({ node, ...props }) => (
            <div className="overflow-x-auto my-2"><table className="min-w-full border dark:border-neutral-700" {...props} /></div>
          ),
          tr: ({ node, ...props }) => (
            <tr className="border-b dark:border-neutral-700" {...props} />
          ),
          th: ({ node, ...props }) => (
            <th className="px-3 py-2 bg-gray-100 dark:bg-neutral-700 font-semibold text-gray-700 dark:text-gray-200 border dark:border-neutral-700" {...props} />
          ),
          td: ({ node, ...props }) => (
            <td className="px-3 py-2 border dark:border-neutral-700 text-gray-700 dark:text-gray-200" {...props} />
          ),
          a: ({ node, href, children, ...props }) => (
            <Tooltip title={href} placement="top">
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-200 transition-colors"
                {...props}
              >
                {children}
              </a>
            </Tooltip>
          ),
          img: ({ node, src, alt, ...props }) => (
            <img
              src={src as string}
              alt={alt as string}
              className="max-w-full rounded shadow my-2 border dark:border-neutral-700"
              {...props}
            />
          ),
          strong: ({ node, ...props }) => (
            <strong className="font-bold text-black dark:text-white" {...props} />
          ),
          em: ({ node, ...props }) => (
            <em className="italic text-gray-700 dark:text-gray-300" {...props} />
          ),
          hr: () => (
            <hr className="my-4 border-t border-gray-300 dark:border-neutral-700" />
          ),
        }}
      />
    </div>
  );
};
