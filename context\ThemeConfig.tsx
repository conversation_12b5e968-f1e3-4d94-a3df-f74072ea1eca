'use client';

// theme/themeConfig.ts
import type { ThemeConfig } from 'antd';

const theme: ThemeConfig = {
  token: {
    // fontSize: 16,
    // colorPrimary: '#4d4d4d',
    // colorPrimaryHover: '#4d4d4d',
    // colorPrimaryActive: '#4d4d4d',
    // colorPrimaryBg: '#4d4d4d',
    // colorPrimaryBorderHover: '#4d4d4d',
  },
  // components: {
  //   Select: {
  //     colorBorder: 'transparent',
  //     // colorPrimaryHover: 'transparent',
  //     boxShadow: '0 4px 16px rgba(0,0,0,0.12)',
  //     borderRadius: 12,
  //     borderRadiusLG: 12,
  //     borderRadiusSM: 12,
  //     borderRadiusXS: 12,
  //   },
  // },
};

export default theme;