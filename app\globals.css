@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 滑条宽度等内容 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

/* 滑条本身的样式 */
::-webkit-scrollbar-thumb {
  background: #e7e7e7; /* 你可以换成你想要的颜色 */
  margin: 2px; /* 上 右 下 左，右侧2px间距 */
  border-radius: 3px;
}

/* 滑条的边距等信息 */
::-webkit-scrollbar-track {
  background: transparent;
  margin: 2px; /* 上 右 下 左，右侧2px间距 */
  border-radius: 3px;
}

/* ======================== 自定义 Tree 样式 Start ======================== */


/* 缩小展开/收起按钮的宽度 */
.ant-tree .ant-tree-switcher {
  width: 12px !important;   /* 默认大约 24px，可适当缩小 */
  /* min-width: 8px !important; */
  padding: 0 !important;    /* 去除多余内边距 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 节点内容包裹区（包含标题、图标等）:让高亮区域（蓝色）高度自适应且内容居中 */
.ant-tree .ant-tree-node-content-wrapper {
  flex: 1 1 auto !important;
  display: flex !important;
  align-items: center !important;
  min-height: 16px !important; /* 可根据实际需要调整 */
  padding: 0 4px !important;   /* 左右内边距可调 */
  box-sizing: border-box;
}

.ant-tree .ant-tree-switcher .ant-tree-switcher-icon {
  position: relative;
  top: 1px; /* 或 -1px，微调到完全居中为止 */
  right: -4px;
}

/* 让标题无多余边距 */
.ant-tree .ant-tree-title {
  margin: 0 !important;
  margin-bottom: 2px !important;
  padding: 0 !important;
  line-height: 1.6 !important;
}

/* 缩小 ant-tree 的缩进宽度，让子节点更靠左 */
.ant-tree .ant-tree-indent-unit {
  width: 16px !important;  /* 默认大约 24px，8px 更紧凑 */
}

/* 隐藏 ant-tree 的拖拽图标 */
.ant-tree .ant-tree-draggable-icon {
  display: none !important;
}

/* ======================== 自定义 Select 样式 Start ======================== */
/* 强制覆盖 Antd Select 的主按钮背景色 */
.ant-select-selector {
  background-color: #fff !important; /* 默认白色，可换成你想要的色值 */
  transition: background 0.2s, border-color 0.2s;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.04); /* 轻微阴影效果 */
  border-color: #cdcfd4 !important;
}

.ant-select-selector:hover,
.ant-select-selector:focus {
  background-color: #f3f4f6 !important; /* hover 时的灰色，可换成你想要的色值 */
  border-color: #d1d5db !important; /* hover/聚焦时边框变为灰色（Tailwind 的 gray-300） */
}
/* ======================== 自定义 Select 样式 End ======================== */