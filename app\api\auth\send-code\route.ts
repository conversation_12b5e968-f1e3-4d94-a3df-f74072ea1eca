import { NextRequest, NextResponse } from 'next/server';
import {
  generateVerificationCode,
  storeVerificationCode,
  canSendCode,
  sendEmailCode
} from '@/lib/auth/verification-codes';

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json();

    // 验证邮箱
    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址不能为空' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    // 检查是否可以发送验证码
    const { canSend, remainingTime } = canSendCode(email);
    if (!canSend) {
      return NextResponse.json(
        { error: `请等待 ${remainingTime} 秒后再试` },
        { status: 429 }
      );
    }

    // 生成验证码
    const code = generateVerificationCode();

    // 发送邮件
    const sendSuccess = await sendEmailCode(email, code);
    if (!sendSuccess) {
      return NextResponse.json(
        { error: '发送邮件失败，请稍后重试' },
        { status: 500 }
      );
    }

    // 存储验证码
    storeVerificationCode(email, code, 5); // 5分钟过期

    return NextResponse.json({
      success: true,
      message: '验证码发送成功',
      // 在开发环境下可以返回验证码，生产环境应该移除
      ...(process.env.NODE_ENV === 'development' && { code })
    });

  } catch (error) {
    console.error('发送验证码错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
