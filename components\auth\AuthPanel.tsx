'use client';

import React, { useState } from 'react';
import { useUserContext } from '@/context/UserContext';
import LoginPanel from './LoginPanel';
import UserInfoPanel from '../user_info/RightPanel';
import RegisterPanel from '../register/RightPanel';

type AuthView = 'login' | 'register' | 'userInfo';

export default function AuthPanel() {
  const { user } = useUserContext();
  const [currentView, setCurrentView] = useState<AuthView>('login');

  // 如果用户已登录，显示用户信息
  if (user) {
    return <UserInfoPanel />;
  }

  // 根据当前视图显示不同的面板
  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <LoginPanel 
            onSwitchToRegister={() => setCurrentView('register')} 
          />
        );
      case 'register':
        return <RegisterPanel />;
      default:
        return (
          <LoginPanel 
            onSwitchToRegister={() => setCurrentView('register')} 
          />
        );
    }
  };

  return (
    <div className="h-full">
      {renderCurrentView()}
    </div>
  );
}
