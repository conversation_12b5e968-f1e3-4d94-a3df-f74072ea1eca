import json
import os
import sys
import random
from tqdm import tqdm

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path: sys.path.append(ROOT_DIR)

from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Union

class EnvConfig:
    """环境变量配置管理器"""
    
    _instance = None
    _keys = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(EnvConfig, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, env_path: Union[str, Path] = None):
        # 优先级：手动指定 > 项目根目录 .env > 系统环境变量
        self.env_path = env_path or Path(__file__).parent.parent.parent / ".env.local"
        print(self.env_path)
        self._load_environment()
        
        # 只在第一次初始化时加载 keys
        if EnvConfig._keys is None:
            RROOT_DIR = os.path.dirname(ROOT_DIR)
            with open(os.path.join(RROOT_DIR, "siliconflow.key"), "r") as f:
                EnvConfig._keys = f.readlines()

    def _load_environment(self):
        """加载环境变量"""
        if isinstance(self.env_path, Path) and self.env_path.exists():
            load_dotenv(dotenv_path=self.env_path, override=True)
        elif isinstance(self.env_path, str) and Path(self.env_path).exists():
            load_dotenv(dotenv_path=self.env_path, override=True)

    @staticmethod
    def get(key: str, default: Optional[str] = None, required: bool = False) -> str:
        """获取环境变量（带验证功能）"""
        value = os.getenv(key, default)
        
        if required and not value:
            raise EnvironmentError(f"Required environment variable {key} is missing")
            
        return value or ""  # 保证返回字符串类型

    @staticmethod
    def get_sc_key(index: int = -1, have_balance: bool = False) -> str:
        """
        获取硅基流动随机模型key
        如果 index 为 -1，则随机选择一个key
        否则，选择第 index 个key
        如果 have_balance 为 True，则优先选择余额大于0.1的key
        """
        # 根据 have_balance 选择文件
        if have_balance:
            file_name = "siliconflow_balance.key"
        else:
            file_name = "siliconflow.key"

        # 加载文件
        if EnvConfig._keys is None:
            RROOT_DIR = os.path.dirname(ROOT_DIR)
            with open(os.path.join(RROOT_DIR, file_name), "r") as f:
                EnvConfig._keys = [line.strip() for line in f if line.strip()]
                
        # 随机选择一个key
        if index == -1:
            # if have_balance:
            #     while True:
            #         sc_key = random.choice(EnvConfig._keys).strip()
            #         balance = EnvConfig.get_sc_balance(sc_key)
            #         if float(balance) > 0.1:
            #             return sc_key
            # else:
            return random.choice(EnvConfig._keys).strip()

        # 按照 index 选择 key
        if index >= len(EnvConfig._keys):
            print(f"警告：索引 {index} 超出了可用密钥范围，将从头循环选择")
            index = index % len(EnvConfig._keys)
        return EnvConfig._keys[index].strip()

    @staticmethod
    def get_sc_balance(sc_key: str) -> str:
        import requests
        import json

        url = "https://api.siliconflow.cn/v1/user/info"
        headers = {"Authorization": f"Bearer {sc_key}"}

        response = requests.request("GET", url, headers=headers)
        result = json.loads(response.text)
        return result['data']['balance']

# 初始化配置（项目启动时调用）
config = EnvConfig()


def test_sc_balance():
    """测试硅基流动的财务情况"""

    import requests
    url = "https://api.siliconflow.cn/v1/user/info"

    # 加载文件 ROOT_DIR + siliconflow.key
    RROOT_DIR = os.path.dirname(ROOT_DIR)
    with open(os.path.join(RROOT_DIR, "siliconflow.key"), "r") as f:
        keys = f.readlines()
    
    infos = []
    for key in tqdm(keys):
        headers = {"Authorization": f"Bearer {key.strip()}"}
        response = requests.request("GET", url, headers=headers)
        info = json.loads(response.text)
        infos.append(info)
        if float(info['data']['balance']) < 0.1:
            print(f"余额不足0.1的key: {key.strip()}")

    # 保存到文件
    with open(os.path.join(RROOT_DIR, "siliconflow_balance.json"), "w") as f:
        f.write(json.dumps(infos, indent=4))

    return infos

# --------------------------
#           使用示例
# --------------------------

if __name__ == "__main__":
    # 获取带默认值的配置
    # db_host = config.get("DB_HOST", "localhost")
    
    # # 获取必须存在的配置（不存在会抛异常）
    # try:
    #     api_key = config.get("API_KEY", required=True)
    # except EnvironmentError as e:
    #     print(f"Configuration error: {e}")
    #     exit(1)

    # print(config.get_sc_random_key())
    filter_sc_balance_to_file()