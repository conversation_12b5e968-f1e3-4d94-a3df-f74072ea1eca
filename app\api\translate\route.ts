import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');

  if (!query || query.length === 0) {
    return NextResponse.json(
      { error: '搜索查询不能为空' },
      { status: 400 }
    );
  }

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/translate_multi`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
    });
    
    if (!response.ok) {
      throw new Error('后端翻译服务响应错误');
    }

    const data = await response.text();
    return NextResponse.json({ translation: data });
    
  } catch (error) {
    console.error('翻译错误:', error);
    return NextResponse.json(
      { error: '翻译服务暂时不可用' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const query = body.query;

    if (!query || query.length === 0) {
      return NextResponse.json(
        { error: '翻译文本不能为空' },
        { status: 400 }
      );
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/translate-multi`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(query),
    });

    if (!response.ok) {
      throw new Error('后端翻译服务响应错误');
    }

    const data = await response.text();
    return NextResponse.json({ translation: data });
    
  } catch (error) {
    console.error('翻译错误:', error);
    return NextResponse.json(
      { error: '翻译服务暂时不可用' },
      { status: 500 }
    );
  }
}
