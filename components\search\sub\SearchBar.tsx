"use client";

import React, { useEffect, useRef, useState } from 'react';
import { Input, Button, Select } from 'antd';
import { useSearchContext } from '@/context/SearchContext';
import { SearchOutlined } from '@ant-design/icons';
import { LuSearch } from "react-icons/lu";

const { Option } = Select;

export default function SearchBar() {
  const { 
    papers, setPapers,
    loading, setLoading,
    loadingStr, setLoadingStr,
    query, setQuery, 
    source, setSource, 
  } = useSearchContext();

  // 是否进行标题翻译
  const [isTitleTranslation, setIsTitleTranslation] = useState(true);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/semantic-search?query=${encodeURIComponent(query)}&source=${source}`);
      const data = await res.json();
      console.log('data', data)
      setPapers(data || []);
    } catch (e) {
      setPapers([]);
    }
    console.log('papers', papers)
    setLoading(false);
  };

 // 检查所有搜索结果的标题翻译
 useEffect(() => {
  const translateTitles = async () => {
    if (!isTitleTranslation) return;

    const needTranslation = papers.map((paper, index) => ({
      index,
      needsTranslation: !paper.translated_title,
      title: paper.title
    })).filter(item => item.needsTranslation);

    if (needTranslation.length === 0) return;

    try {
      setLoadingStr("翻译中...")

      const translationResponse = await fetch('/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: needTranslation.map(item => item.title)
        }),
      });

      if (!translationResponse.ok) {
        throw new Error(`标题翻译请求失败: ${translationResponse.statusText}`);
      }

      const data = await translationResponse.json();
      
      if (!data || !data.translation) {
        console.error('标题翻译返回数据格式错误:', data);
        return;
      }

      const translations = Array.isArray(data.translation) 
        ? data.translation 
        : JSON.parse(data.translation);

      if (translations.length !== needTranslation.length) {
        console.error('标题翻译返回数量不匹配');
        return;
      }

      setPapers(prev => {
        const newResults = [...prev];
        needTranslation.forEach((item, arrayIndex) => {
          if (translations[arrayIndex]) {
            newResults[item.index] = {
              ...newResults[item.index],
              translated_title: translations[arrayIndex]
            };
          }
        });
        return newResults;
      });

    } catch (error) {
      console.error('标题翻译失败:', error);
    } finally {
      setLoading(false);
      setLoadingStr("搜索中...")
    }
  };
  translateTitles();
}, [papers, isTitleTranslation, setLoading]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 items-center">
        <Input
          size='large'
          placeholder="请输入检索内容"
          value={query}
          onChange={e => setQuery(e.target.value)}
          onPressEnter={handleSearch}
          prefix={<LuSearch color="gray"/>}
          // className="w-80 "
          style={{ width: '21rem'}}
        />

      </div>
      <div className="flex items-center gap-2 justify-between w-full">
        <Select value={source} onChange={setSource} className="w-40" size='middle'>
          <Option value="arxiv">arXiv预印本</Option>
          <Option value="conference">已发表会议</Option>
          <Option value="journal">已发表期刊</Option>
        </Select>

        <Button
          loading={loading}
          onClick={handleSearch}
          size='middle'
          className="bg-blue-100 text-blue-700 hover:bg-blue-200 border-none shadow-none ml-4"
        >
          搜索
        </Button>
      </div>
    </div>
  );
}
