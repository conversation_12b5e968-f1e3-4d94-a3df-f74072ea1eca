import { NextRequest, NextResponse } from 'next/server';

// 定义允许的来源
const allowedOrigins = [
    'http://localhost:3000', 
    'http://localhost:5173', 
    'http://axsight.top', 
    'https://axsight.top', 
    ''
];

export async function OPTIONS(request: NextRequest) {
  console.log('OPTIONS');
  const origin = request.headers.get('origin') || '';
  console.log('origin', origin);
  if (allowedOrigins.includes(origin)) {
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
        'Access-Control-Max-Age': '86400', // 24小时
      },
    });
  }
  return new NextResponse(
    JSON.stringify({ error: '无效的来源域名,请检查请求来源是否在允许列表中' }),
    { status: 403 }
  );
}

export async function POST(request: NextRequest) {
  const origin = request.headers.get('origin') || '';
  if (!allowedOrigins.includes(origin)) {
    return new NextResponse(
      JSON.stringify({ error: '无效的来源域名,请检查请求来源是否在允许列表中' }), 
      { status: 403 }
    );
  }

  try {
    const data = await request.json();
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/abstract-refinement-multi`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch from backend');
    }

    const result = await response.json();
    const res = NextResponse.json(result);
    res.headers.set('Access-Control-Allow-Origin', origin); // 设置CORS头
    return res;
    
  } catch (error) {
    console.error('Error in abstract-refinement-multi:', error);
    return new NextResponse(
      JSON.stringify({ error: 'Internal Server Error' }),
      { status: 500, headers: { 'Access-Control-Allow-Origin': origin } }
    );
  }
}